{"extDescription": {"message": "欺骗您的浏览器配置文件。包括一些隐私增强选项。"}, "notifications-profileChange": {"message": "配置文件已更改："}, "notifications-unableToGetIPInfo": {"message": "无法获取IP信息"}, "notifications-usingIPInfo": {"message": "使用IP信息："}, "notifications-usingIPRule": {"message": "使用IP规则："}, "options-about-issueTracker": {"message": "问题跟踪"}, "options-about-knownIssues": {"message": "已知问题"}, "options-about-wiki": {"message": "Wiki"}, "options-about-support": {"message": "支持"}, "options-about-sourceCode": {"message": "源码"}, "options-about-translate": {"message": "帮助翻译"}, "options-import-couldNotImport": {"message": "无法导入文件"}, "options-import-invalid-config": {"message": "设置无效：缺少配置"}, "options-import-invalid-excluded": {"message": "设置无效：缺少排除项"}, "options-import-invalid-excludedProfile": {"message": "无效设置：排除项包含无效的配置文件"}, "options-import-invalid-headers": {"message": "设置无效：缺少头"}, "options-import-invalid-ipRuleId": {"message": "设置无效：IP规则id无效"}, "options-import-invalid-ipRuleName": {"message": "设置无效：缺少IP规则名称"}, "options-import-invalid-ipRuleRange": {"message": "设置无效：IP规则范围无效"}, "options-import-invalid-ipRules": {"message": "设置无效：缺少IP规则"}, "options-import-invalid-ipRulesDupe": {"message": "设置无效：发现重复的IP规则id"}, "options-import-invalid-options": {"message": "设置无效：缺少选项"}, "options-import-invalid-profile": {"message": "设置无效：缺少配置设置"}, "options-import-invalid-setting": {"message": "设置值无效："}, "options-import-invalid-settings": {"message": "设置无效：缺少设置"}, "options-import-invalid-spoofIP": {"message": "设置无效：欺骗头部IP范围无效"}, "options-import-invalid-version": {"message": "设置无效：版本不被接受"}, "options-import-invalid-whitelist": {"message": "设置无效：缺少白名单"}, "options-import-invalid-whitelistDupe": {"message": "设置无效：发现重复的白名单规则id"}, "options-import-invalid-whitelistId": {"message": "设置无效：白名单规则id无效"}, "options-import-invalid-whitelistName": {"message": "设置无效：缺少白名单规则名称"}, "options-import-invalid-whitelistOpt": {"message": "设置无效：白名单规则选项无效"}, "options-import-invalid-whitelistSpoofIP": {"message": "设置无效：白名单规则的欺骗IP无效"}, "options-import-success": {"message": "导入设置成功。正在重新加载扩展..."}, "options-ipRules-editorTitle": {"message": "IP规则编辑器"}, "options-ipRules-ipRule": {"message": "IP规则"}, "options-ipRules-reload": {"message": "重新加载IP信息"}, "options-ipRules-textareaLabel": {"message": "IP范围/地址"}, "options-ipRules-textareaPlaceholder": {"message": "每行一个IP/IP范围"}, "options-modal-askDelete": {"message": "您确定要删除这条规则吗？"}, "options-modal-askReset": {"message": "您确定要重置您的设置？"}, "options-modal-confirmDelete": {"message": "是的，将其删除！"}, "options-modal-confirmReset": {"message": "是，重置我的设置！"}, "options-settings": {"message": "设置"}, "options-settings-import": {"message": "导入"}, "options-settings-importing": {"message": "正在导入设置"}, "options-settings-export": {"message": "导出"}, "options-settings-reset": {"message": "重置为默认"}, "options-settings-permissions": {"message": "Chameleon可以控制一些Firefox首选项，如抵制指纹识别或启用跟踪保护。这可能会与其他扩展冲突。您可以通过移除隐私权限退出Chameleon对这些首选项的控制。请注意，移除此权限将会重置这些首选项。如果不存在，您可以请求此权限。"}, "options-settings-permissions-legacy": {"message": "您需要安装Chameleon的专用版本以启用隐私权限。这是因为支持跨平台/新版的扩展的权限有些复杂的问题。更多详细信息可在下方链接的wiki找到。"}, "options-settings-permissions-legacy-wiki": {"message": "更多信息请查看wiki"}, "options-settings-permissions-request": {"message": "请求隐私权限"}, "options-settings-permissions-remove": {"message": "移除隐私权限"}, "options-tab-about": {"message": "关于"}, "options-tab-ipRules": {"message": "IP规则"}, "options-whitelist-acceptLang": {"message": "Accept-Language"}, "options-whitelist-editorTitle": {"message": "白名单规则编辑器"}, "options-whitelist-headerIPLabel": {"message": "头部IP(Via & X-Forwarded-For)"}, "options-whitelist-options-audioContext": {"message": "启用欺骗音频上下文"}, "options-whitelist-options-clientRects": {"message": "启用欺骗客户端矩形"}, "options-whitelist-options-cssExfil": {"message": "阻止CSS Exfil"}, "options-whitelist-options-mediaDevices": {"message": "阻止媒体设备"}, "options-whitelist-options-name": {"message": "启用保护窗口名称"}, "options-whitelist-options-tz": {"message": "启用时区欺骗"}, "options-whitelist-options-ws": {"message": "禁用WebSocket"}, "options-whitelist-rule": {"message": "白名单规则"}, "options-whitelist-sitesTip": {"message": "每行一条规则：域名@@[可选正则表达式]"}, "options-whitelist-textareaLabel": {"message": "IP范围/地址"}, "options-whitelist-textareaPlaceholder": {"message": "每行一个IP/IP范围"}, "popup-home-change": {"message": "更改"}, "popup-home-currentProfile": {"message": "当前配置文件"}, "popup-home-currentProfile-defaultLanguage": {"message": "默认语言"}, "popup-home-currentProfile-defaultScreen": {"message": "默认屏幕"}, "popup-home-currentProfile-defaultTimezone": {"message": "默认时区"}, "popup-home-currentProfile-gettingTimezone": {"message": "正在获取IP信息"}, "popup-home-currentProfile-screenProfile": {"message": "屏幕（配置文件）"}, "popup-home-disabled": {"message": "Chameleon已禁用"}, "popup-home-enabled": {"message": "Chameleon已启用"}, "popup-home-notification-disabled": {"message": "通知关"}, "popup-home-notification-enabled": {"message": "通知开"}, "popup-home-onThisPage": {"message": "在本页面"}, "popup-home-theme-dark": {"message": "深色"}, "popup-home-theme-light": {"message": "浅色"}, "popup-profile-changePeriodically": {"message": "定期更改"}, "popup-profile-devicePhone": {"message": "手机"}, "popup-profile-deviceTablet": {"message": "平板"}, "popup-profile-interval-no": {"message": "否"}, "popup-profile-interval-custom": {"message": "自定义间隔"}, "popup-profile-interval-customMax": {"message": "最大值(分钟)："}, "popup-profile-interval-customMin": {"message": "最小值(分钟)："}, "popup-profile-interval-minute": {"message": "每分钟"}, "popup-profile-interval-5minutes": {"message": "每5分钟"}, "popup-profile-interval-10minutes": {"message": "每10分钟"}, "popup-profile-interval-20minutes": {"message": "每20分钟"}, "popup-profile-interval-30minutes": {"message": "每30分钟"}, "popup-profile-interval-40minutes": {"message": "每40分钟"}, "popup-profile-interval-50minutes": {"message": "每50分钟"}, "popup-profile-interval-hour": {"message": "每小时"}, "popup-profile-exclude": {"message": "排除"}, "popup-profile-randomAndroid": {"message": "随机Android浏览器"}, "popup-profile-randomIOS": {"message": "随机的iOS浏览器"}, "popup-profile-randomMacOS": {"message": "随机macOS浏览器"}, "popup-profile-randomLinux": {"message": "随机Linux浏览器"}, "popup-profile-randomWindows": {"message": "随机Windows浏览器"}, "popup-profile-random": {"message": "随机"}, "popup-profile-randomDesktopProfile": {"message": "随机配置文件(桌面)"}, "popup-profile-randomMobileProfile": {"message": "随机配置文件(移动)"}, "popup-profile-showProfileOnIcon": {"message": "在图标上显示浏览器配置文件"}, "popup-headers": {"message": "头信息"}, "popup-headers-enableDNT": {"message": "启用DNT(Do Not Track)"}, "popup-headers-preventEtag": {"message": "阻止Etag跟踪"}, "popup-headers-refererWarning": {"message": "请勿修改以下选项的about:config设置。"}, "popup-headers-referer-trimming": {"message": "Referer微调政策"}, "popup-headers-referer-trimming-sendFullURI": {"message": "发送完整URI(默认)"}, "popup-headers-referer-trimming-schemeHostPortPath": {"message": "方案、主机、端口+路径"}, "popup-headers-referer-trimming-schemeHostPort": {"message": "方案、主机+端口"}, "popup-headers-referer-xorigin": {"message": "Referer X Origin策略"}, "popup-headers-referer-xorigin-alwaysSend": {"message": "总是发送(默认)"}, "popup-headers-referer-xorigin-matchBaseDomain": {"message": "匹配基本域名"}, "popup-headers-referer-xorigin-matchHost": {"message": "匹配主机"}, "popup-headers-spoofAcceptLang": {"message": "欺骗Accept-Language"}, "popup-headers-spoofIP": {"message": "欺骗X-Forwarded-For/Via IP"}, "popup-headers-spoofIP-random": {"message": "随机IP"}, "popup-headers-spoofIP-custom": {"message": "自定义IP"}, "popup-headers-spoofIP-rangeFrom": {"message": "来源范围"}, "popup-headers-spoofIP-rangeTo": {"message": "目标范围"}, "popup-options": {"message": "选项"}, "popup-options-grantPermissions": {"message": "授予修改设置的权限"}, "popup-options-injection": {"message": "注入"}, "popup-options-injection-limitTabHistory": {"message": "限制标签页历史"}, "popup-options-injection-protectWinName": {"message": "保护窗口名称"}, "popup-options-injection-audioContext": {"message": "欺骗音频上下文"}, "popup-options-injection-clientRects": {"message": "欺骗客户端矩形"}, "popup-options-injection-protectKBFingerprint": {"message": "保护键盘指纹"}, "popup-options-injection-protectKBFingerprintDelay": {"message": "延迟(毫秒)"}, "popup-options-injection-screen": {"message": "屏幕大小"}, "popup-options-injection-spoofFontFingerprint": {"message": "欺骗字体指纹"}, "popup-options-standard": {"message": "标准"}, "popup-options-standard-blockMediaDevices": {"message": "阻止媒体设备"}, "popup-options-standard-blockCSSExfil": {"message": "阻止CSS Exfil"}, "popup-options-standard-disableWebRTC": {"message": "禁用WebRTC"}, "popup-options-standard-firstPartyIsolation": {"message": "启用第一方隔离"}, "popup-options-standard-resistFingerprinting": {"message": "抵制指纹识别"}, "popup-options-standard-spoofMediaDevices": {"message": "欺骗媒体设备"}, "popup-options-standard-trackingProtection": {"message": "跟踪保护模式"}, "popup-options-standard-trackingProtection-on": {"message": "开"}, "popup-options-standard-trackingProtection-off": {"message": "关"}, "popup-options-standard-trackingProtection-privateBrowsing": {"message": "在隐私浏览中启用"}, "popup-options-standard-webRTCPolicy": {"message": "WebRTC策略"}, "popup-options-standard-webRTCPolicy-nonProxified": {"message": "禁用非代理的UDP"}, "popup-options-standard-webRTCPolicy-public": {"message": "仅使用公共接口(最佳)"}, "popup-options-standard-webRTCPolicy-publicPrivate": {"message": "使用公共和私有接口"}, "popup-options-standard-webSockets-blockAll": {"message": "全部阻止"}, "popup-options-standard-webSockets-blockThirdParty": {"message": "阻止第三方"}, "popup-options-cookie": {"message": "<PERSON><PERSON>选项"}, "popup-options-cookieNotPersistent": {"message": "窗口关闭后删除cookie和站点数据"}, "popup-options-cookiePolicy": {"message": "策略"}, "popup-options-cookiePolicy-allowVisited": {"message": "允许访问过的第三方"}, "popup-options-cookiePolicy-rejectAll": {"message": "全部阻止"}, "popup-options-cookiePolicy-rejectThirdParty": {"message": "阻止第三方"}, "popup-options-cookiePolicy-rejectTrackers": {"message": "拒绝跟踪器"}, "popup-options-cookiePolicy-rejectTrackersPartitionForeign": {"message": "阻隔跟踪器和第三方cookie"}, "popup-whitelist-contextMenu": {"message": "添加右键菜单条目到当前打开标签页白名单中的域名"}, "popup-whitelist-defaultProfileLabel": {"message": "默认配置文件"}, "popup-whitelist-enable": {"message": "启用白名单"}, "popup-whitelist-isNotWhitelisted": {"message": "未加入白名单"}, "popup-whitelist-isWhitelisted": {"message": "已加入白名单"}, "popup-whitelist-open": {"message": "在白名单中打开"}, "text-addToRule": {"message": "添加到规则：$RULE_NAME$", "placeholders": {"rule_name": {"content": "$1", "example": "Firefox whitelist rule profile"}}}, "text-allowAll": {"message": "全部允许"}, "text-cancel": {"message": "取消"}, "text-createNewRule": {"message": "创建新规则"}, "text-default": {"message": "默认"}, "text-defaultWhitelistProfile": {"message": "默认白名单配置文件"}, "text-disableReferer": {"message": "禁用Referer"}, "text-language": {"message": "语言"}, "text-name": {"message": "名称"}, "text-profile": {"message": "配置文件"}, "text-realProfile": {"message": "真实配置文件"}, "text-removeFromRule": {"message": "从规则中移除：$RULE_NAME$", "placeholders": {"rule_name": {"content": "$1", "example": "Firefox whitelist rule profile"}}}, "text-save": {"message": "保存"}, "text-screen": {"message": "屏幕"}, "text-searchRules": {"message": "搜索规则"}, "text-startupDelay": {"message": "启动延迟（秒）"}, "text-timezone": {"message": "时区"}, "text-whitelist": {"message": "已加入白名单"}}