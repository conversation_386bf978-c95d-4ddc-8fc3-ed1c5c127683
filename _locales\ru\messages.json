{"extDescription": {"message": "Подменяет профиль вашего браузера. Предоставляет ряд других возможностей повышения конфиденциальности."}, "notifications-profileChange": {"message": "Профиль изменен:"}, "notifications-unableToGetIPInfo": {"message": "Невозможно получить информацию об IP-адресе"}, "notifications-usingIPInfo": {"message": "Используемая информация о IP:"}, "notifications-usingIPRule": {"message": "Используемые правила IP:"}, "options-about-issueTracker": {"message": "Трекер ошибок"}, "options-about-knownIssues": {"message": "Известные проблемы"}, "options-about-wiki": {"message": "Вики"}, "options-about-support": {"message": "Поддержка"}, "options-about-sourceCode": {"message": "Исходный код"}, "options-about-translate": {"message": "Помощь с переводом"}, "options-import-couldNotImport": {"message": "Не удалось импортировать файл"}, "options-import-invalid-config": {"message": "Неверные настройки: отсутствует конфигурация"}, "options-import-invalid-excluded": {"message": "Некорректные настройки: отсутствует исключение"}, "options-import-invalid-excludedProfile": {"message": "Неправильные настройки: исключение имеет недействительный профиль"}, "options-import-invalid-headers": {"message": "Неверные настройки: отсутствуют заголовки"}, "options-import-invalid-ipRuleId": {"message": "Недопустимые настройки: неверный идентификатор IP правила"}, "options-import-invalid-ipRuleName": {"message": "Неверные настройки: отсутствует имя IP правила"}, "options-import-invalid-ipRuleRange": {"message": "Недопустимые настройки: неверный диапазон IP правил"}, "options-import-invalid-ipRules": {"message": "Неверные настройки: отсутствуют IP правила"}, "options-import-invalid-ipRulesDupe": {"message": "Недопустимые настройки: найден дубликат IP правила"}, "options-import-invalid-options": {"message": "Неверные настройки: отсутствуют параметры"}, "options-import-invalid-profile": {"message": "Неверные настройки: отсутствует профиль"}, "options-import-invalid-setting": {"message": "Неверное значение параметра:"}, "options-import-invalid-settings": {"message": "Неверные настройки: отсутствуют настройки"}, "options-import-invalid-spoofIP": {"message": "Неверные настройки: Диа<PERSON>азон IP-адреса поддельных заголовков недействителен"}, "options-import-invalid-version": {"message": "Недопустимые настройки: версия не принята"}, "options-import-invalid-whitelist": {"message": "Неверные настройки: отсутствует белый список"}, "options-import-invalid-whitelistDupe": {"message": "Недопустимые настройки: найден дубликат правила из белого списка"}, "options-import-invalid-whitelistId": {"message": "Недопустимые настройки: недействительный идентификатор правила белого списка"}, "options-import-invalid-whitelistName": {"message": "Недопустимые настройки: отсутствует имя правила из белого списка"}, "options-import-invalid-whitelistOpt": {"message": "Недействительные настройки: некорректные настройки правила белого списка"}, "options-import-invalid-whitelistSpoofIP": {"message": "Некорректные настройки: недопустимое правило белого списка для подмены IP-адреса"}, "options-import-success": {"message": "Настройки успешно импортированы. Перезагрузка расширения..."}, "options-ipRules-editorTitle": {"message": "Редактор правил IP"}, "options-ipRules-ipRule": {"message": "Правило IP"}, "options-ipRules-reload": {"message": "Перезагрузить информацию о IP"}, "options-ipRules-textareaLabel": {"message": "Диапазоны IP / Адреса"}, "options-ipRules-textareaPlaceholder": {"message": "Один IP/IP диапазон на строку"}, "options-modal-askDelete": {"message": "Вы уверены, что хотите удалить правило?"}, "options-modal-askReset": {"message": "Вы уверены, что хотите сбросить все настройки?"}, "options-modal-confirmDelete": {"message": "Да, удалить его!"}, "options-modal-confirmReset": {"message": "Да, сбросить мои настройки!"}, "options-settings": {"message": "Настройки"}, "options-settings-import": {"message": "Импорт"}, "options-settings-importing": {"message": "Параметры импорта"}, "options-settings-export": {"message": "Экспорт"}, "options-settings-reset": {"message": "Восстановить значения по умолчанию"}, "options-settings-permissions": {"message": "Chameleon может управлять некоторыми настройками Firefox, такими как отпечаток пальцев или включение защиты отслеживания. Это может привести к конфликту с другим расширением. Вы можете отказаться от чамелеона, управляя этими настройками, удалив разрешение на конфиденциальность. Пожалуйста, обратите внимание, что удаление этого разрешения сбросит эти настройки. Вы можете запросить это разрешение, если оно отсутствует."}, "options-settings-permissions-legacy": {"message": "Вам потребуется установить специальную версию Chameleon для включения разрешений на конфиденциальность. Это связано с некоторыми сложностями с поддерживаемыми разрешениями для кросс-платформы/новой версии этого расширения. Более подробную информацию вы можете найти в вики написаные снизу."}, "options-settings-permissions-legacy-wiki": {"message": "Подробнее на Вики"}, "options-settings-permissions-request": {"message": "Запросить разрешения"}, "options-settings-permissions-remove": {"message": "Удалить разрешения"}, "options-tab-about": {"message": "О программе"}, "options-tab-ipRules": {"message": "IP правила"}, "options-whitelist-acceptLang": {"message": "Подменять Accept-Language"}, "options-whitelist-editorTitle": {"message": "Редактор правил белого списка"}, "options-whitelist-headerIPLabel": {"message": "IP для заголовков (\"Via\" и \"X-Forwarded-For\")"}, "options-whitelist-options-audioContext": {"message": "Включить подмену аудио контекста"}, "options-whitelist-options-clientRects": {"message": "Включить подмену клиентских rects"}, "options-whitelist-options-cssExfil": {"message": "Блокировать CSS Exfil"}, "options-whitelist-options-mediaDevices": {"message": "Блокировать медиа-устройства"}, "options-whitelist-options-name": {"message": "Включить защиту имени окна"}, "options-whitelist-options-tz": {"message": "Включить подмену часового пояса"}, "options-whitelist-options-ws": {"message": "Отключить WebSocket"}, "options-whitelist-rule": {"message": "Правило белого списка"}, "options-whitelist-sitesTip": {"message": "Одно правило на строку:  domain@@[optional regex pattern]"}, "options-whitelist-textareaLabel": {"message": "Диапазоны IP / Адреса"}, "options-whitelist-textareaPlaceholder": {"message": "Один IP/IP диапазон на строку"}, "popup-home-change": {"message": "Изменить"}, "popup-home-currentProfile": {"message": "Текущий профиль"}, "popup-home-currentProfile-defaultLanguage": {"message": "Язык по умолчанию"}, "popup-home-currentProfile-defaultScreen": {"message": "Экран по умолчанию"}, "popup-home-currentProfile-defaultTimezone": {"message": "Часовой пояс по умолчанию"}, "popup-home-currentProfile-gettingTimezone": {"message": "Получение информации об IP-адресе"}, "popup-home-currentProfile-screenProfile": {"message": "<PERSON>к<PERSON><PERSON><PERSON> (профиль)"}, "popup-home-disabled": {"message": "Chamel<PERSON> отключен"}, "popup-home-enabled": {"message": "Chameleon включен"}, "popup-home-notification-disabled": {"message": "Отключить уведомления"}, "popup-home-notification-enabled": {"message": "Включить уведомления"}, "popup-home-onThisPage": {"message": "на этой странице"}, "popup-home-theme-dark": {"message": "Тёмная"}, "popup-home-theme-light": {"message": "Светлая"}, "popup-profile-changePeriodically": {"message": "Изменять периодически"}, "popup-profile-devicePhone": {"message": "Телефон"}, "popup-profile-deviceTablet": {"message": "План<PERSON>ет"}, "popup-profile-interval-no": {"message": "Нет"}, "popup-profile-interval-custom": {"message": "Произвольный интервал"}, "popup-profile-interval-customMax": {"message": "Макс. (минуты)"}, "popup-profile-interval-customMin": {"message": "Мин. (минуты)"}, "popup-profile-interval-minute": {"message": "Каждую минуту"}, "popup-profile-interval-5minutes": {"message": "Каждые 5 минут"}, "popup-profile-interval-10minutes": {"message": "Каждые 10 минут"}, "popup-profile-interval-20minutes": {"message": "Каждые 20 минут"}, "popup-profile-interval-30minutes": {"message": "Каждые 30 минут"}, "popup-profile-interval-40minutes": {"message": "Каждые 40 минут"}, "popup-profile-interval-50minutes": {"message": "Каждые 50 минут"}, "popup-profile-interval-hour": {"message": "Каждый час"}, "popup-profile-exclude": {"message": "Исключить"}, "popup-profile-randomAndroid": {"message": "Случайные браузеры Android"}, "popup-profile-randomIOS": {"message": "Случайные браузеры iOS"}, "popup-profile-randomMacOS": {"message": "Случайные браузеры macOS"}, "popup-profile-randomLinux": {"message": "Случайные браузеры Linux"}, "popup-profile-randomWindows": {"message": "Случайные браузеры Windows"}, "popup-profile-random": {"message": "Случайный"}, "popup-profile-randomDesktopProfile": {"message": "Случайный профиль (Настольный)"}, "popup-profile-randomMobileProfile": {"message": "Случайный профиль (Мобильный)"}, "popup-profile-showProfileOnIcon": {"message": "Показать профиль браузера на значке"}, "popup-headers": {"message": "Заголовки"}, "popup-headers-enableDNT": {"message": "Включить \"DNT\" (Не отслеживать)"}, "popup-headers-preventEtag": {"message": "Предотвращать отслеживание по \"Etag\""}, "popup-headers-refererWarning": {"message": "Не изменяйте настройки через about:config для параметров ниже."}, "popup-headers-referer-trimming": {"message": "Политика обрезки \"Referer\""}, "popup-headers-referer-trimming-sendFullURI": {"message": "Отправлять полный URI (по умолчанию)"}, "popup-headers-referer-trimming-schemeHostPortPath": {"message": "Scheme, host, port + path"}, "popup-headers-referer-trimming-schemeHostPort": {"message": "Scheme, host + port"}, "popup-headers-referer-xorigin": {"message": "Referer X Origin Policy"}, "popup-headers-referer-xorigin-alwaysSend": {"message": "Всегда отправлять (по умолчанию)"}, "popup-headers-referer-xorigin-matchBaseDomain": {"message": "При совпадении базового домена"}, "popup-headers-referer-xorigin-matchHost": {"message": "При совпадении хоста"}, "popup-headers-spoofAcceptLang": {"message": "Подменять \"Accept-Language\""}, "popup-headers-spoofIP": {"message": "Подмена X-Forwarded-For/Via IP"}, "popup-headers-spoofIP-random": {"message": "Случайный IP"}, "popup-headers-spoofIP-custom": {"message": "Произвольный IP"}, "popup-headers-spoofIP-rangeFrom": {"message": "Диапазон от"}, "popup-headers-spoofIP-rangeTo": {"message": "Диапазон до"}, "popup-options": {"message": "Параметры"}, "popup-options-grantPermissions": {"message": "Предоставить разрешение на изменение настроек"}, "popup-options-injection": {"message": "Внедрение"}, "popup-options-injection-limitTabHistory": {"message": "Ограничить историю вкладок"}, "popup-options-injection-protectWinName": {"message": "Защищать \"window name\""}, "popup-options-injection-audioContext": {"message": "Подменять отпечаток аудио"}, "popup-options-injection-clientRects": {"message": "Подменять \"Client Rects\""}, "popup-options-injection-protectKBFingerprint": {"message": "Защищать от клавиатурных отпечатков"}, "popup-options-injection-protectKBFingerprintDelay": {"message": "Задержка (мс)"}, "popup-options-injection-screen": {"message": "Размер экрана"}, "popup-options-injection-spoofFontFingerprint": {"message": "Подменять отпечаток шрифтов"}, "popup-options-standard": {"message": "Стандартный"}, "popup-options-standard-blockMediaDevices": {"message": "Блокировать медиа устройства"}, "popup-options-standard-blockCSSExfil": {"message": "Защищать от атаки CSS Exfil"}, "popup-options-standard-disableWebRTC": {"message": "Отключить WebRTC"}, "popup-options-standard-firstPartyIsolation": {"message": "Включить изоляцию 1го уровня"}, "popup-options-standard-resistFingerprinting": {"message": "Препятствовать сбору отпечатков"}, "popup-options-standard-spoofMediaDevices": {"message": "Подделка медиа устройства"}, "popup-options-standard-trackingProtection": {"message": "Режим защиты от отслеживания"}, "popup-options-standard-trackingProtection-on": {"message": "<PERSON><PERSON><PERSON>"}, "popup-options-standard-trackingProtection-off": {"message": "Вы<PERSON>л"}, "popup-options-standard-trackingProtection-privateBrowsing": {"message": "Включить в приватном просмотре"}, "popup-options-standard-webRTCPolicy": {"message": "Политика WebRTC"}, "popup-options-standard-webRTCPolicy-nonProxified": {"message": "Отключить непроксированный UDP"}, "popup-options-standard-webRTCPolicy-public": {"message": "Использовать только публичный интерфейс (лучшее)"}, "popup-options-standard-webRTCPolicy-publicPrivate": {"message": "Использовать публичный и частный интерфейс"}, "popup-options-standard-webSockets-blockAll": {"message": "Блокировать все"}, "popup-options-standard-webSockets-blockThirdParty": {"message": "Блокировать сторонние"}, "popup-options-cookie": {"message": "<PERSON><PERSON>"}, "popup-options-cookieNotPersistent": {"message": "Удалить cookies и данные сайта после закрытия окна"}, "popup-options-cookiePolicy": {"message": "Политика"}, "popup-options-cookiePolicy-allowVisited": {"message": "Разрешить посещение"}, "popup-options-cookiePolicy-rejectAll": {"message": "Отклонить все"}, "popup-options-cookiePolicy-rejectThirdParty": {"message": "Отклонить сторонние"}, "popup-options-cookiePolicy-rejectTrackers": {"message": "Блокировать трекеры"}, "popup-options-cookiePolicy-rejectTrackersPartitionForeign": {"message": "Отклонить трекеры и third-party куки"}, "popup-whitelist-contextMenu": {"message": "Добавить пункт в контекстное меню для открытия текущего домена во вкладке белого списка"}, "popup-whitelist-defaultProfileLabel": {"message": "Профиль по умолчанию"}, "popup-whitelist-enable": {"message": "Включить белый список"}, "popup-whitelist-isNotWhitelisted": {"message": "не в белом списке"}, "popup-whitelist-isWhitelisted": {"message": "в белом списке"}, "popup-whitelist-open": {"message": "Открыть белый список"}, "text-addToRule": {"message": "Добавить к правилу: $RULE_NAME$", "placeholders": {"rule_name": {"content": "$1", "example": "Firefox whitelist rule profile"}}}, "text-allowAll": {"message": "Разрешить все"}, "text-cancel": {"message": "Отменить"}, "text-createNewRule": {"message": "Создать новое правило"}, "text-default": {"message": "По умолчанию"}, "text-defaultWhitelistProfile": {"message": "Профиль белого списка по умолчанию"}, "text-disableReferer": {"message": "Отключить \"referer\""}, "text-language": {"message": "Язык"}, "text-name": {"message": "Имя"}, "text-profile": {"message": "Профиль"}, "text-realProfile": {"message": "Реальный профиль"}, "text-removeFromRule": {"message": "Удалить из правила: $RULE_NAME$", "placeholders": {"rule_name": {"content": "$1", "example": "Firefox whitelist rule profile"}}}, "text-save": {"message": "Сохранить"}, "text-screen": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "text-searchRules": {"message": "Поиск правил"}, "text-startupDelay": {"message": "Задержка запуска (сек)"}, "text-timezone": {"message": "Часовой пояс"}, "text-whitelist": {"message": "Белый список"}}