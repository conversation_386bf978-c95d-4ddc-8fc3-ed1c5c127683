!function(e){var t={};function a(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,a),o.l=!0,o.exports}a.m=e,a.c=t,a.d=function(e,t,n){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)a.d(n,o,function(t){return e[t]}.bind(null,o));return n},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a(a.s=195)}({1:function(e,t,a){"use strict";(function(e){
/*!
 * Vue.js v2.6.14
 * (c) 2014-2021 Evan You
 * Released under the MIT License.
 */
var a=Object.freeze({});function n(e){return null==e}function o(e){return null!=e}function i(e){return!0===e}function r(e){return"string"==typeof e||"number"==typeof e||"symbol"==typeof e||"boolean"==typeof e}function s(e){return null!==e&&"object"==typeof e}var u=Object.prototype.toString;function c(e){return"[object Object]"===u.call(e)}function l(e){return"[object RegExp]"===u.call(e)}function p(e){var t=parseFloat(String(e));return t>=0&&Math.floor(t)===t&&isFinite(e)}function m(e){return o(e)&&"function"==typeof e.then&&"function"==typeof e.catch}function f(e){return null==e?"":Array.isArray(e)||c(e)&&e.toString===u?JSON.stringify(e,null,2):String(e)}function h(e){var t=parseFloat(e);return isNaN(t)?e:t}function d(e,t){for(var a=Object.create(null),n=e.split(","),o=0;o<n.length;o++)a[n[o]]=!0;return t?function(e){return a[e.toLowerCase()]}:function(e){return a[e]}}d("slot,component",!0);var g=d("key,ref,slot,slot-scope,is");function b(e,t){if(e.length){var a=e.indexOf(t);if(a>-1)return e.splice(a,1)}}var k=Object.prototype.hasOwnProperty;function v(e,t){return k.call(e,t)}function y(e){var t=Object.create(null);return function(a){return t[a]||(t[a]=e(a))}}var j=/-(\w)/g,w=y((function(e){return e.replace(j,(function(e,t){return t?t.toUpperCase():""}))})),_=y((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})),A=/\B([A-Z])/g,x=y((function(e){return e.replace(A,"-$1").toLowerCase()}));var z=Function.prototype.bind?function(e,t){return e.bind(t)}:function(e,t){function a(a){var n=arguments.length;return n?n>1?e.apply(t,arguments):e.call(t,a):e.call(t)}return a._length=e.length,a};function S(e,t){t=t||0;for(var a=e.length-t,n=new Array(a);a--;)n[a]=e[a+t];return n}function C(e,t){for(var a in t)e[a]=t[a];return e}function E(e){for(var t={},a=0;a<e.length;a++)e[a]&&C(t,e[a]);return t}function T(e,t,a){}var I=function(e,t,a){return!1},P=function(e){return e};function M(e,t){if(e===t)return!0;var a=s(e),n=s(t);if(!a||!n)return!a&&!n&&String(e)===String(t);try{var o=Array.isArray(e),i=Array.isArray(t);if(o&&i)return e.length===t.length&&e.every((function(e,a){return M(e,t[a])}));if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();if(o||i)return!1;var r=Object.keys(e),u=Object.keys(t);return r.length===u.length&&r.every((function(a){return M(e[a],t[a])}))}catch(e){return!1}}function O(e,t){for(var a=0;a<e.length;a++)if(M(e[a],t))return a;return-1}function F(e){var t=!1;return function(){t||(t=!0,e.apply(this,arguments))}}var D=["component","directive","filter"],L=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch"],N={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!1,devtools:!1,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:I,isReservedAttr:I,isUnknownElement:I,getTagNamespace:T,parsePlatformTagName:P,mustUseProp:I,async:!0,_lifecycleHooks:L},R=/a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function q(e,t,a,n){Object.defineProperty(e,t,{value:a,enumerable:!!n,writable:!0,configurable:!0})}var B=new RegExp("[^"+R.source+".$_\\d]");var U,$="__proto__"in{},H="undefined"!=typeof window,G="undefined"!=typeof WXEnvironment&&!!WXEnvironment.platform,W=G&&WXEnvironment.platform.toLowerCase(),V=H&&window.navigator.userAgent.toLowerCase(),Y=V&&/msie|trident/.test(V),K=V&&V.indexOf("msie 9.0")>0,X=V&&V.indexOf("edge/")>0,J=(V&&V.indexOf("android"),V&&/iphone|ipad|ipod|ios/.test(V)||"ios"===W),Z=(V&&/chrome\/\d+/.test(V),V&&/phantomjs/.test(V),V&&V.match(/firefox\/(\d+)/)),Q={}.watch,ee=!1;if(H)try{var te={};Object.defineProperty(te,"passive",{get:function(){ee=!0}}),window.addEventListener("test-passive",null,te)}catch(e){}var ae=function(){return void 0===U&&(U=!H&&!G&&"undefined"!=typeof window&&(window.process&&"server"===window.process.env.VUE_ENV)),U},ne=H&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function oe(e){return"function"==typeof e&&/native code/.test(e.toString())}var ie,re="undefined"!=typeof Symbol&&oe(Symbol)&&"undefined"!=typeof Reflect&&oe(Reflect.ownKeys);ie="undefined"!=typeof Set&&oe(Set)?Set:function(){function e(){this.set=Object.create(null)}return e.prototype.has=function(e){return!0===this.set[e]},e.prototype.add=function(e){this.set[e]=!0},e.prototype.clear=function(){this.set=Object.create(null)},e}();var se=T,ue=0,ce=function(){this.id=ue++,this.subs=[]};ce.prototype.addSub=function(e){this.subs.push(e)},ce.prototype.removeSub=function(e){b(this.subs,e)},ce.prototype.depend=function(){ce.target&&ce.target.addDep(this)},ce.prototype.notify=function(){var e=this.subs.slice();for(var t=0,a=e.length;t<a;t++)e[t].update()},ce.target=null;var le=[];function pe(e){le.push(e),ce.target=e}function me(){le.pop(),ce.target=le[le.length-1]}var fe=function(e,t,a,n,o,i,r,s){this.tag=e,this.data=t,this.children=a,this.text=n,this.elm=o,this.ns=void 0,this.context=i,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=t&&t.key,this.componentOptions=r,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=s,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1},he={child:{configurable:!0}};he.child.get=function(){return this.componentInstance},Object.defineProperties(fe.prototype,he);var de=function(e){void 0===e&&(e="");var t=new fe;return t.text=e,t.isComment=!0,t};function ge(e){return new fe(void 0,void 0,void 0,String(e))}function be(e){var t=new fe(e.tag,e.data,e.children&&e.children.slice(),e.text,e.elm,e.context,e.componentOptions,e.asyncFactory);return t.ns=e.ns,t.isStatic=e.isStatic,t.key=e.key,t.isComment=e.isComment,t.fnContext=e.fnContext,t.fnOptions=e.fnOptions,t.fnScopeId=e.fnScopeId,t.asyncMeta=e.asyncMeta,t.isCloned=!0,t}var ke=Array.prototype,ve=Object.create(ke);["push","pop","shift","unshift","splice","sort","reverse"].forEach((function(e){var t=ke[e];q(ve,e,(function(){for(var a=[],n=arguments.length;n--;)a[n]=arguments[n];var o,i=t.apply(this,a),r=this.__ob__;switch(e){case"push":case"unshift":o=a;break;case"splice":o=a.slice(2)}return o&&r.observeArray(o),r.dep.notify(),i}))}));var ye=Object.getOwnPropertyNames(ve),je=!0;function we(e){je=e}var _e=function(e){this.value=e,this.dep=new ce,this.vmCount=0,q(e,"__ob__",this),Array.isArray(e)?($?function(e,t){e.__proto__=t}(e,ve):function(e,t,a){for(var n=0,o=a.length;n<o;n++){var i=a[n];q(e,i,t[i])}}(e,ve,ye),this.observeArray(e)):this.walk(e)};function Ae(e,t){var a;if(s(e)&&!(e instanceof fe))return v(e,"__ob__")&&e.__ob__ instanceof _e?a=e.__ob__:je&&!ae()&&(Array.isArray(e)||c(e))&&Object.isExtensible(e)&&!e._isVue&&(a=new _e(e)),t&&a&&a.vmCount++,a}function xe(e,t,a,n,o){var i=new ce,r=Object.getOwnPropertyDescriptor(e,t);if(!r||!1!==r.configurable){var s=r&&r.get,u=r&&r.set;s&&!u||2!==arguments.length||(a=e[t]);var c=!o&&Ae(a);Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){var t=s?s.call(e):a;return ce.target&&(i.depend(),c&&(c.dep.depend(),Array.isArray(t)&&Ce(t))),t},set:function(t){var n=s?s.call(e):a;t===n||t!=t&&n!=n||s&&!u||(u?u.call(e,t):a=t,c=!o&&Ae(t),i.notify())}})}}function ze(e,t,a){if(Array.isArray(e)&&p(t))return e.length=Math.max(e.length,t),e.splice(t,1,a),a;if(t in e&&!(t in Object.prototype))return e[t]=a,a;var n=e.__ob__;return e._isVue||n&&n.vmCount?a:n?(xe(n.value,t,a),n.dep.notify(),a):(e[t]=a,a)}function Se(e,t){if(Array.isArray(e)&&p(t))e.splice(t,1);else{var a=e.__ob__;e._isVue||a&&a.vmCount||v(e,t)&&(delete e[t],a&&a.dep.notify())}}function Ce(e){for(var t=void 0,a=0,n=e.length;a<n;a++)(t=e[a])&&t.__ob__&&t.__ob__.dep.depend(),Array.isArray(t)&&Ce(t)}_e.prototype.walk=function(e){for(var t=Object.keys(e),a=0;a<t.length;a++)xe(e,t[a])},_e.prototype.observeArray=function(e){for(var t=0,a=e.length;t<a;t++)Ae(e[t])};var Ee=N.optionMergeStrategies;function Te(e,t){if(!t)return e;for(var a,n,o,i=re?Reflect.ownKeys(t):Object.keys(t),r=0;r<i.length;r++)"__ob__"!==(a=i[r])&&(n=e[a],o=t[a],v(e,a)?n!==o&&c(n)&&c(o)&&Te(n,o):ze(e,a,o));return e}function Ie(e,t,a){return a?function(){var n="function"==typeof t?t.call(a,a):t,o="function"==typeof e?e.call(a,a):e;return n?Te(n,o):o}:t?e?function(){return Te("function"==typeof t?t.call(this,this):t,"function"==typeof e?e.call(this,this):e)}:t:e}function Pe(e,t){var a=t?e?e.concat(t):Array.isArray(t)?t:[t]:e;return a?function(e){for(var t=[],a=0;a<e.length;a++)-1===t.indexOf(e[a])&&t.push(e[a]);return t}(a):a}function Me(e,t,a,n){var o=Object.create(e||null);return t?C(o,t):o}Ee.data=function(e,t,a){return a?Ie(e,t,a):t&&"function"!=typeof t?e:Ie(e,t)},L.forEach((function(e){Ee[e]=Pe})),D.forEach((function(e){Ee[e+"s"]=Me})),Ee.watch=function(e,t,a,n){if(e===Q&&(e=void 0),t===Q&&(t=void 0),!t)return Object.create(e||null);if(!e)return t;var o={};for(var i in C(o,e),t){var r=o[i],s=t[i];r&&!Array.isArray(r)&&(r=[r]),o[i]=r?r.concat(s):Array.isArray(s)?s:[s]}return o},Ee.props=Ee.methods=Ee.inject=Ee.computed=function(e,t,a,n){if(!e)return t;var o=Object.create(null);return C(o,e),t&&C(o,t),o},Ee.provide=Ie;var Oe=function(e,t){return void 0===t?e:t};function Fe(e,t,a){if("function"==typeof t&&(t=t.options),function(e,t){var a=e.props;if(a){var n,o,i={};if(Array.isArray(a))for(n=a.length;n--;)"string"==typeof(o=a[n])&&(i[w(o)]={type:null});else if(c(a))for(var r in a)o=a[r],i[w(r)]=c(o)?o:{type:o};else 0;e.props=i}}(t),function(e,t){var a=e.inject;if(a){var n=e.inject={};if(Array.isArray(a))for(var o=0;o<a.length;o++)n[a[o]]={from:a[o]};else if(c(a))for(var i in a){var r=a[i];n[i]=c(r)?C({from:i},r):{from:r}}else 0}}(t),function(e){var t=e.directives;if(t)for(var a in t){var n=t[a];"function"==typeof n&&(t[a]={bind:n,update:n})}}(t),!t._base&&(t.extends&&(e=Fe(e,t.extends,a)),t.mixins))for(var n=0,o=t.mixins.length;n<o;n++)e=Fe(e,t.mixins[n],a);var i,r={};for(i in e)s(i);for(i in t)v(e,i)||s(i);function s(n){var o=Ee[n]||Oe;r[n]=o(e[n],t[n],a,n)}return r}function De(e,t,a,n){if("string"==typeof a){var o=e[t];if(v(o,a))return o[a];var i=w(a);if(v(o,i))return o[i];var r=_(i);return v(o,r)?o[r]:o[a]||o[i]||o[r]}}function Le(e,t,a,n){var o=t[e],i=!v(a,e),r=a[e],s=Be(Boolean,o.type);if(s>-1)if(i&&!v(o,"default"))r=!1;else if(""===r||r===x(e)){var u=Be(String,o.type);(u<0||s<u)&&(r=!0)}if(void 0===r){r=function(e,t,a){if(!v(t,"default"))return;var n=t.default;0;if(e&&e.$options.propsData&&void 0===e.$options.propsData[a]&&void 0!==e._props[a])return e._props[a];return"function"==typeof n&&"Function"!==Re(t.type)?n.call(e):n}(n,o,e);var c=je;we(!0),Ae(r),we(c)}return r}var Ne=/^\s*function (\w+)/;function Re(e){var t=e&&e.toString().match(Ne);return t?t[1]:""}function qe(e,t){return Re(e)===Re(t)}function Be(e,t){if(!Array.isArray(t))return qe(t,e)?0:-1;for(var a=0,n=t.length;a<n;a++)if(qe(t[a],e))return a;return-1}function Ue(e,t,a){pe();try{if(t)for(var n=t;n=n.$parent;){var o=n.$options.errorCaptured;if(o)for(var i=0;i<o.length;i++)try{if(!1===o[i].call(n,e,t,a))return}catch(e){He(e,n,"errorCaptured hook")}}He(e,t,a)}finally{me()}}function $e(e,t,a,n,o){var i;try{(i=a?e.apply(t,a):e.call(t))&&!i._isVue&&m(i)&&!i._handled&&(i.catch((function(e){return Ue(e,n,o+" (Promise/async)")})),i._handled=!0)}catch(e){Ue(e,n,o)}return i}function He(e,t,a){if(N.errorHandler)try{return N.errorHandler.call(null,e,t,a)}catch(t){t!==e&&Ge(t,null,"config.errorHandler")}Ge(e,t,a)}function Ge(e,t,a){if(!H&&!G||"undefined"==typeof console)throw e;console.error(e)}var We,Ve=!1,Ye=[],Ke=!1;function Xe(){Ke=!1;var e=Ye.slice(0);Ye.length=0;for(var t=0;t<e.length;t++)e[t]()}if("undefined"!=typeof Promise&&oe(Promise)){var Je=Promise.resolve();We=function(){Je.then(Xe),J&&setTimeout(T)},Ve=!0}else if(Y||"undefined"==typeof MutationObserver||!oe(MutationObserver)&&"[object MutationObserverConstructor]"!==MutationObserver.toString())We=void 0!==e&&oe(e)?function(){e(Xe)}:function(){setTimeout(Xe,0)};else{var Ze=1,Qe=new MutationObserver(Xe),et=document.createTextNode(String(Ze));Qe.observe(et,{characterData:!0}),We=function(){Ze=(Ze+1)%2,et.data=String(Ze)},Ve=!0}function tt(e,t){var a;if(Ye.push((function(){if(e)try{e.call(t)}catch(e){Ue(e,t,"nextTick")}else a&&a(t)})),Ke||(Ke=!0,We()),!e&&"undefined"!=typeof Promise)return new Promise((function(e){a=e}))}var at=new ie;function nt(e){!function e(t,a){var n,o,i=Array.isArray(t);if(!i&&!s(t)||Object.isFrozen(t)||t instanceof fe)return;if(t.__ob__){var r=t.__ob__.dep.id;if(a.has(r))return;a.add(r)}if(i)for(n=t.length;n--;)e(t[n],a);else for(o=Object.keys(t),n=o.length;n--;)e(t[o[n]],a)}(e,at),at.clear()}var ot=y((function(e){var t="&"===e.charAt(0),a="~"===(e=t?e.slice(1):e).charAt(0),n="!"===(e=a?e.slice(1):e).charAt(0);return{name:e=n?e.slice(1):e,once:a,capture:n,passive:t}}));function it(e,t){function a(){var e=arguments,n=a.fns;if(!Array.isArray(n))return $e(n,null,arguments,t,"v-on handler");for(var o=n.slice(),i=0;i<o.length;i++)$e(o[i],null,e,t,"v-on handler")}return a.fns=e,a}function rt(e,t,a,o,r,s){var u,c,l,p;for(u in e)c=e[u],l=t[u],p=ot(u),n(c)||(n(l)?(n(c.fns)&&(c=e[u]=it(c,s)),i(p.once)&&(c=e[u]=r(p.name,c,p.capture)),a(p.name,c,p.capture,p.passive,p.params)):c!==l&&(l.fns=c,e[u]=l));for(u in t)n(e[u])&&o((p=ot(u)).name,t[u],p.capture)}function st(e,t,a){var r;e instanceof fe&&(e=e.data.hook||(e.data.hook={}));var s=e[t];function u(){a.apply(this,arguments),b(r.fns,u)}n(s)?r=it([u]):o(s.fns)&&i(s.merged)?(r=s).fns.push(u):r=it([s,u]),r.merged=!0,e[t]=r}function ut(e,t,a,n,i){if(o(t)){if(v(t,a))return e[a]=t[a],i||delete t[a],!0;if(v(t,n))return e[a]=t[n],i||delete t[n],!0}return!1}function ct(e){return r(e)?[ge(e)]:Array.isArray(e)?function e(t,a){var s,u,c,l,p=[];for(s=0;s<t.length;s++)n(u=t[s])||"boolean"==typeof u||(c=p.length-1,l=p[c],Array.isArray(u)?u.length>0&&(lt((u=e(u,(a||"")+"_"+s))[0])&&lt(l)&&(p[c]=ge(l.text+u[0].text),u.shift()),p.push.apply(p,u)):r(u)?lt(l)?p[c]=ge(l.text+u):""!==u&&p.push(ge(u)):lt(u)&&lt(l)?p[c]=ge(l.text+u.text):(i(t._isVList)&&o(u.tag)&&n(u.key)&&o(a)&&(u.key="__vlist"+a+"_"+s+"__"),p.push(u)));return p}(e):void 0}function lt(e){return o(e)&&o(e.text)&&!1===e.isComment}function pt(e,t){if(e){for(var a=Object.create(null),n=re?Reflect.ownKeys(e):Object.keys(e),o=0;o<n.length;o++){var i=n[o];if("__ob__"!==i){for(var r=e[i].from,s=t;s;){if(s._provided&&v(s._provided,r)){a[i]=s._provided[r];break}s=s.$parent}if(!s)if("default"in e[i]){var u=e[i].default;a[i]="function"==typeof u?u.call(t):u}else 0}}return a}}function mt(e,t){if(!e||!e.length)return{};for(var a={},n=0,o=e.length;n<o;n++){var i=e[n],r=i.data;if(r&&r.attrs&&r.attrs.slot&&delete r.attrs.slot,i.context!==t&&i.fnContext!==t||!r||null==r.slot)(a.default||(a.default=[])).push(i);else{var s=r.slot,u=a[s]||(a[s]=[]);"template"===i.tag?u.push.apply(u,i.children||[]):u.push(i)}}for(var c in a)a[c].every(ft)&&delete a[c];return a}function ft(e){return e.isComment&&!e.asyncFactory||" "===e.text}function ht(e){return e.isComment&&e.asyncFactory}function dt(e,t,n){var o,i=Object.keys(t).length>0,r=e?!!e.$stable:!i,s=e&&e.$key;if(e){if(e._normalized)return e._normalized;if(r&&n&&n!==a&&s===n.$key&&!i&&!n.$hasNormal)return n;for(var u in o={},e)e[u]&&"$"!==u[0]&&(o[u]=gt(t,u,e[u]))}else o={};for(var c in t)c in o||(o[c]=bt(t,c));return e&&Object.isExtensible(e)&&(e._normalized=o),q(o,"$stable",r),q(o,"$key",s),q(o,"$hasNormal",i),o}function gt(e,t,a){var n=function(){var e=arguments.length?a.apply(null,arguments):a({}),t=(e=e&&"object"==typeof e&&!Array.isArray(e)?[e]:ct(e))&&e[0];return e&&(!t||1===e.length&&t.isComment&&!ht(t))?void 0:e};return a.proxy&&Object.defineProperty(e,t,{get:n,enumerable:!0,configurable:!0}),n}function bt(e,t){return function(){return e[t]}}function kt(e,t){var a,n,i,r,u;if(Array.isArray(e)||"string"==typeof e)for(a=new Array(e.length),n=0,i=e.length;n<i;n++)a[n]=t(e[n],n);else if("number"==typeof e)for(a=new Array(e),n=0;n<e;n++)a[n]=t(n+1,n);else if(s(e))if(re&&e[Symbol.iterator]){a=[];for(var c=e[Symbol.iterator](),l=c.next();!l.done;)a.push(t(l.value,a.length)),l=c.next()}else for(r=Object.keys(e),a=new Array(r.length),n=0,i=r.length;n<i;n++)u=r[n],a[n]=t(e[u],u,n);return o(a)||(a=[]),a._isVList=!0,a}function vt(e,t,a,n){var o,i=this.$scopedSlots[e];i?(a=a||{},n&&(a=C(C({},n),a)),o=i(a)||("function"==typeof t?t():t)):o=this.$slots[e]||("function"==typeof t?t():t);var r=a&&a.slot;return r?this.$createElement("template",{slot:r},o):o}function yt(e){return De(this.$options,"filters",e)||P}function jt(e,t){return Array.isArray(e)?-1===e.indexOf(t):e!==t}function wt(e,t,a,n,o){var i=N.keyCodes[t]||a;return o&&n&&!N.keyCodes[t]?jt(o,n):i?jt(i,e):n?x(n)!==t:void 0===e}function _t(e,t,a,n,o){if(a)if(s(a)){var i;Array.isArray(a)&&(a=E(a));var r=function(r){if("class"===r||"style"===r||g(r))i=e;else{var s=e.attrs&&e.attrs.type;i=n||N.mustUseProp(t,s,r)?e.domProps||(e.domProps={}):e.attrs||(e.attrs={})}var u=w(r),c=x(r);u in i||c in i||(i[r]=a[r],o&&((e.on||(e.on={}))["update:"+r]=function(e){a[r]=e}))};for(var u in a)r(u)}else;return e}function At(e,t){var a=this._staticTrees||(this._staticTrees=[]),n=a[e];return n&&!t||zt(n=a[e]=this.$options.staticRenderFns[e].call(this._renderProxy,null,this),"__static__"+e,!1),n}function xt(e,t,a){return zt(e,"__once__"+t+(a?"_"+a:""),!0),e}function zt(e,t,a){if(Array.isArray(e))for(var n=0;n<e.length;n++)e[n]&&"string"!=typeof e[n]&&St(e[n],t+"_"+n,a);else St(e,t,a)}function St(e,t,a){e.isStatic=!0,e.key=t,e.isOnce=a}function Ct(e,t){if(t)if(c(t)){var a=e.on=e.on?C({},e.on):{};for(var n in t){var o=a[n],i=t[n];a[n]=o?[].concat(o,i):i}}else;return e}function Et(e,t,a,n){t=t||{$stable:!a};for(var o=0;o<e.length;o++){var i=e[o];Array.isArray(i)?Et(i,t,a):i&&(i.proxy&&(i.fn.proxy=!0),t[i.key]=i.fn)}return n&&(t.$key=n),t}function Tt(e,t){for(var a=0;a<t.length;a+=2){var n=t[a];"string"==typeof n&&n&&(e[t[a]]=t[a+1])}return e}function It(e,t){return"string"==typeof e?t+e:e}function Pt(e){e._o=xt,e._n=h,e._s=f,e._l=kt,e._t=vt,e._q=M,e._i=O,e._m=At,e._f=yt,e._k=wt,e._b=_t,e._v=ge,e._e=de,e._u=Et,e._g=Ct,e._d=Tt,e._p=It}function Mt(e,t,n,o,r){var s,u=this,c=r.options;v(o,"_uid")?(s=Object.create(o))._original=o:(s=o,o=o._original);var l=i(c._compiled),p=!l;this.data=e,this.props=t,this.children=n,this.parent=o,this.listeners=e.on||a,this.injections=pt(c.inject,o),this.slots=function(){return u.$slots||dt(e.scopedSlots,u.$slots=mt(n,o)),u.$slots},Object.defineProperty(this,"scopedSlots",{enumerable:!0,get:function(){return dt(e.scopedSlots,this.slots())}}),l&&(this.$options=c,this.$slots=this.slots(),this.$scopedSlots=dt(e.scopedSlots,this.$slots)),c._scopeId?this._c=function(e,t,a,n){var i=qt(s,e,t,a,n,p);return i&&!Array.isArray(i)&&(i.fnScopeId=c._scopeId,i.fnContext=o),i}:this._c=function(e,t,a,n){return qt(s,e,t,a,n,p)}}function Ot(e,t,a,n,o){var i=be(e);return i.fnContext=a,i.fnOptions=n,t.slot&&((i.data||(i.data={})).slot=t.slot),i}function Ft(e,t){for(var a in t)e[w(a)]=t[a]}Pt(Mt.prototype);var Dt={init:function(e,t){if(e.componentInstance&&!e.componentInstance._isDestroyed&&e.data.keepAlive){var a=e;Dt.prepatch(a,a)}else{(e.componentInstance=function(e,t){var a={_isComponent:!0,_parentVnode:e,parent:t},n=e.data.inlineTemplate;o(n)&&(a.render=n.render,a.staticRenderFns=n.staticRenderFns);return new e.componentOptions.Ctor(a)}(e,Kt)).$mount(t?e.elm:void 0,t)}},prepatch:function(e,t){var n=t.componentOptions;!function(e,t,n,o,i){0;var r=o.data.scopedSlots,s=e.$scopedSlots,u=!!(r&&!r.$stable||s!==a&&!s.$stable||r&&e.$scopedSlots.$key!==r.$key||!r&&e.$scopedSlots.$key),c=!!(i||e.$options._renderChildren||u);e.$options._parentVnode=o,e.$vnode=o,e._vnode&&(e._vnode.parent=o);if(e.$options._renderChildren=i,e.$attrs=o.data.attrs||a,e.$listeners=n||a,t&&e.$options.props){we(!1);for(var l=e._props,p=e.$options._propKeys||[],m=0;m<p.length;m++){var f=p[m],h=e.$options.props;l[f]=Le(f,h,t,e)}we(!0),e.$options.propsData=t}n=n||a;var d=e.$options._parentListeners;e.$options._parentListeners=n,Yt(e,n,d),c&&(e.$slots=mt(i,o.context),e.$forceUpdate());0}(t.componentInstance=e.componentInstance,n.propsData,n.listeners,t,n.children)},insert:function(e){var t,a=e.context,n=e.componentInstance;n._isMounted||(n._isMounted=!0,Qt(n,"mounted")),e.data.keepAlive&&(a._isMounted?((t=n)._inactive=!1,ta.push(t)):Zt(n,!0))},destroy:function(e){var t=e.componentInstance;t._isDestroyed||(e.data.keepAlive?function e(t,a){if(a&&(t._directInactive=!0,Jt(t)))return;if(!t._inactive){t._inactive=!0;for(var n=0;n<t.$children.length;n++)e(t.$children[n]);Qt(t,"deactivated")}}(t,!0):t.$destroy())}},Lt=Object.keys(Dt);function Nt(e,t,r,u,c){if(!n(e)){var l=r.$options._base;if(s(e)&&(e=l.extend(e)),"function"==typeof e){var p;if(n(e.cid)&&void 0===(e=function(e,t){if(i(e.error)&&o(e.errorComp))return e.errorComp;if(o(e.resolved))return e.resolved;var a=Ut;a&&o(e.owners)&&-1===e.owners.indexOf(a)&&e.owners.push(a);if(i(e.loading)&&o(e.loadingComp))return e.loadingComp;if(a&&!o(e.owners)){var r=e.owners=[a],u=!0,c=null,l=null;a.$on("hook:destroyed",(function(){return b(r,a)}));var p=function(e){for(var t=0,a=r.length;t<a;t++)r[t].$forceUpdate();e&&(r.length=0,null!==c&&(clearTimeout(c),c=null),null!==l&&(clearTimeout(l),l=null))},f=F((function(a){e.resolved=$t(a,t),u?r.length=0:p(!0)})),h=F((function(t){o(e.errorComp)&&(e.error=!0,p(!0))})),d=e(f,h);return s(d)&&(m(d)?n(e.resolved)&&d.then(f,h):m(d.component)&&(d.component.then(f,h),o(d.error)&&(e.errorComp=$t(d.error,t)),o(d.loading)&&(e.loadingComp=$t(d.loading,t),0===d.delay?e.loading=!0:c=setTimeout((function(){c=null,n(e.resolved)&&n(e.error)&&(e.loading=!0,p(!1))}),d.delay||200)),o(d.timeout)&&(l=setTimeout((function(){l=null,n(e.resolved)&&h(null)}),d.timeout)))),u=!1,e.loading?e.loadingComp:e.resolved}}(p=e,l)))return function(e,t,a,n,o){var i=de();return i.asyncFactory=e,i.asyncMeta={data:t,context:a,children:n,tag:o},i}(p,t,r,u,c);t=t||{},ja(e),o(t.model)&&function(e,t){var a=e.model&&e.model.prop||"value",n=e.model&&e.model.event||"input";(t.attrs||(t.attrs={}))[a]=t.model.value;var i=t.on||(t.on={}),r=i[n],s=t.model.callback;o(r)?(Array.isArray(r)?-1===r.indexOf(s):r!==s)&&(i[n]=[s].concat(r)):i[n]=s}(e.options,t);var f=function(e,t,a){var i=t.options.props;if(!n(i)){var r={},s=e.attrs,u=e.props;if(o(s)||o(u))for(var c in i){var l=x(c);ut(r,u,c,l,!0)||ut(r,s,c,l,!1)}return r}}(t,e);if(i(e.options.functional))return function(e,t,n,i,r){var s=e.options,u={},c=s.props;if(o(c))for(var l in c)u[l]=Le(l,c,t||a);else o(n.attrs)&&Ft(u,n.attrs),o(n.props)&&Ft(u,n.props);var p=new Mt(n,u,r,i,e),m=s.render.call(null,p._c,p);if(m instanceof fe)return Ot(m,n,p.parent,s,p);if(Array.isArray(m)){for(var f=ct(m)||[],h=new Array(f.length),d=0;d<f.length;d++)h[d]=Ot(f[d],n,p.parent,s,p);return h}}(e,f,t,r,u);var h=t.on;if(t.on=t.nativeOn,i(e.options.abstract)){var d=t.slot;t={},d&&(t.slot=d)}!function(e){for(var t=e.hook||(e.hook={}),a=0;a<Lt.length;a++){var n=Lt[a],o=t[n],i=Dt[n];o===i||o&&o._merged||(t[n]=o?Rt(i,o):i)}}(t);var g=e.options.name||c;return new fe("vue-component-"+e.cid+(g?"-"+g:""),t,void 0,void 0,void 0,r,{Ctor:e,propsData:f,listeners:h,tag:c,children:u},p)}}}function Rt(e,t){var a=function(a,n){e(a,n),t(a,n)};return a._merged=!0,a}function qt(e,t,a,u,c,l){return(Array.isArray(a)||r(a))&&(c=u,u=a,a=void 0),i(l)&&(c=2),function(e,t,a,r,u){if(o(a)&&o(a.__ob__))return de();o(a)&&o(a.is)&&(t=a.is);if(!t)return de();0;Array.isArray(r)&&"function"==typeof r[0]&&((a=a||{}).scopedSlots={default:r[0]},r.length=0);2===u?r=ct(r):1===u&&(r=function(e){for(var t=0;t<e.length;t++)if(Array.isArray(e[t]))return Array.prototype.concat.apply([],e);return e}(r));var c,l;if("string"==typeof t){var p;l=e.$vnode&&e.$vnode.ns||N.getTagNamespace(t),c=N.isReservedTag(t)?new fe(N.parsePlatformTagName(t),a,r,void 0,void 0,e):a&&a.pre||!o(p=De(e.$options,"components",t))?new fe(t,a,r,void 0,void 0,e):Nt(p,a,e,r,t)}else c=Nt(t,a,e,r);return Array.isArray(c)?c:o(c)?(o(l)&&function e(t,a,r){t.ns=a,"foreignObject"===t.tag&&(a=void 0,r=!0);if(o(t.children))for(var s=0,u=t.children.length;s<u;s++){var c=t.children[s];o(c.tag)&&(n(c.ns)||i(r)&&"svg"!==c.tag)&&e(c,a,r)}}(c,l),o(a)&&function(e){s(e.style)&&nt(e.style);s(e.class)&&nt(e.class)}(a),c):de()}(e,t,a,u,c)}var Bt,Ut=null;function $t(e,t){return(e.__esModule||re&&"Module"===e[Symbol.toStringTag])&&(e=e.default),s(e)?t.extend(e):e}function Ht(e){if(Array.isArray(e))for(var t=0;t<e.length;t++){var a=e[t];if(o(a)&&(o(a.componentOptions)||ht(a)))return a}}function Gt(e,t){Bt.$on(e,t)}function Wt(e,t){Bt.$off(e,t)}function Vt(e,t){var a=Bt;return function n(){var o=t.apply(null,arguments);null!==o&&a.$off(e,n)}}function Yt(e,t,a){Bt=e,rt(t,a||{},Gt,Wt,Vt,e),Bt=void 0}var Kt=null;function Xt(e){var t=Kt;return Kt=e,function(){Kt=t}}function Jt(e){for(;e&&(e=e.$parent);)if(e._inactive)return!0;return!1}function Zt(e,t){if(t){if(e._directInactive=!1,Jt(e))return}else if(e._directInactive)return;if(e._inactive||null===e._inactive){e._inactive=!1;for(var a=0;a<e.$children.length;a++)Zt(e.$children[a]);Qt(e,"activated")}}function Qt(e,t){pe();var a=e.$options[t],n=t+" hook";if(a)for(var o=0,i=a.length;o<i;o++)$e(a[o],e,null,e,n);e._hasHookEvent&&e.$emit("hook:"+t),me()}var ea=[],ta=[],aa={},na=!1,oa=!1,ia=0;var ra=0,sa=Date.now;if(H&&!Y){var ua=window.performance;ua&&"function"==typeof ua.now&&sa()>document.createEvent("Event").timeStamp&&(sa=function(){return ua.now()})}function ca(){var e,t;for(ra=sa(),oa=!0,ea.sort((function(e,t){return e.id-t.id})),ia=0;ia<ea.length;ia++)(e=ea[ia]).before&&e.before(),t=e.id,aa[t]=null,e.run();var a=ta.slice(),n=ea.slice();ia=ea.length=ta.length=0,aa={},na=oa=!1,function(e){for(var t=0;t<e.length;t++)e[t]._inactive=!0,Zt(e[t],!0)}(a),function(e){var t=e.length;for(;t--;){var a=e[t],n=a.vm;n._watcher===a&&n._isMounted&&!n._isDestroyed&&Qt(n,"updated")}}(n),ne&&N.devtools&&ne.emit("flush")}var la=0,pa=function(e,t,a,n,o){this.vm=e,o&&(e._watcher=this),e._watchers.push(this),n?(this.deep=!!n.deep,this.user=!!n.user,this.lazy=!!n.lazy,this.sync=!!n.sync,this.before=n.before):this.deep=this.user=this.lazy=this.sync=!1,this.cb=a,this.id=++la,this.active=!0,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new ie,this.newDepIds=new ie,this.expression="","function"==typeof t?this.getter=t:(this.getter=function(e){if(!B.test(e)){var t=e.split(".");return function(e){for(var a=0;a<t.length;a++){if(!e)return;e=e[t[a]]}return e}}}(t),this.getter||(this.getter=T)),this.value=this.lazy?void 0:this.get()};pa.prototype.get=function(){var e;pe(this);var t=this.vm;try{e=this.getter.call(t,t)}catch(e){if(!this.user)throw e;Ue(e,t,'getter for watcher "'+this.expression+'"')}finally{this.deep&&nt(e),me(),this.cleanupDeps()}return e},pa.prototype.addDep=function(e){var t=e.id;this.newDepIds.has(t)||(this.newDepIds.add(t),this.newDeps.push(e),this.depIds.has(t)||e.addSub(this))},pa.prototype.cleanupDeps=function(){for(var e=this.deps.length;e--;){var t=this.deps[e];this.newDepIds.has(t.id)||t.removeSub(this)}var a=this.depIds;this.depIds=this.newDepIds,this.newDepIds=a,this.newDepIds.clear(),a=this.deps,this.deps=this.newDeps,this.newDeps=a,this.newDeps.length=0},pa.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():function(e){var t=e.id;if(null==aa[t]){if(aa[t]=!0,oa){for(var a=ea.length-1;a>ia&&ea[a].id>e.id;)a--;ea.splice(a+1,0,e)}else ea.push(e);na||(na=!0,tt(ca))}}(this)},pa.prototype.run=function(){if(this.active){var e=this.get();if(e!==this.value||s(e)||this.deep){var t=this.value;if(this.value=e,this.user){var a='callback for watcher "'+this.expression+'"';$e(this.cb,this.vm,[e,t],this.vm,a)}else this.cb.call(this.vm,e,t)}}},pa.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},pa.prototype.depend=function(){for(var e=this.deps.length;e--;)this.deps[e].depend()},pa.prototype.teardown=function(){if(this.active){this.vm._isBeingDestroyed||b(this.vm._watchers,this);for(var e=this.deps.length;e--;)this.deps[e].removeSub(this);this.active=!1}};var ma={enumerable:!0,configurable:!0,get:T,set:T};function fa(e,t,a){ma.get=function(){return this[t][a]},ma.set=function(e){this[t][a]=e},Object.defineProperty(e,a,ma)}function ha(e){e._watchers=[];var t=e.$options;t.props&&function(e,t){var a=e.$options.propsData||{},n=e._props={},o=e.$options._propKeys=[];e.$parent&&we(!1);var i=function(i){o.push(i);var r=Le(i,t,a,e);xe(n,i,r),i in e||fa(e,"_props",i)};for(var r in t)i(r);we(!0)}(e,t.props),t.methods&&function(e,t){e.$options.props;for(var a in t)e[a]="function"!=typeof t[a]?T:z(t[a],e)}(e,t.methods),t.data?function(e){var t=e.$options.data;c(t=e._data="function"==typeof t?function(e,t){pe();try{return e.call(t,t)}catch(e){return Ue(e,t,"data()"),{}}finally{me()}}(t,e):t||{})||(t={});var a=Object.keys(t),n=e.$options.props,o=(e.$options.methods,a.length);for(;o--;){var i=a[o];0,n&&v(n,i)||(r=void 0,36!==(r=(i+"").charCodeAt(0))&&95!==r&&fa(e,"_data",i))}var r;Ae(t,!0)}(e):Ae(e._data={},!0),t.computed&&function(e,t){var a=e._computedWatchers=Object.create(null),n=ae();for(var o in t){var i=t[o],r="function"==typeof i?i:i.get;0,n||(a[o]=new pa(e,r||T,T,da)),o in e||ga(e,o,i)}}(e,t.computed),t.watch&&t.watch!==Q&&function(e,t){for(var a in t){var n=t[a];if(Array.isArray(n))for(var o=0;o<n.length;o++)va(e,a,n[o]);else va(e,a,n)}}(e,t.watch)}var da={lazy:!0};function ga(e,t,a){var n=!ae();"function"==typeof a?(ma.get=n?ba(t):ka(a),ma.set=T):(ma.get=a.get?n&&!1!==a.cache?ba(t):ka(a.get):T,ma.set=a.set||T),Object.defineProperty(e,t,ma)}function ba(e){return function(){var t=this._computedWatchers&&this._computedWatchers[e];if(t)return t.dirty&&t.evaluate(),ce.target&&t.depend(),t.value}}function ka(e){return function(){return e.call(this,this)}}function va(e,t,a,n){return c(a)&&(n=a,a=a.handler),"string"==typeof a&&(a=e[a]),e.$watch(t,a,n)}var ya=0;function ja(e){var t=e.options;if(e.super){var a=ja(e.super);if(a!==e.superOptions){e.superOptions=a;var n=function(e){var t,a=e.options,n=e.sealedOptions;for(var o in a)a[o]!==n[o]&&(t||(t={}),t[o]=a[o]);return t}(e);n&&C(e.extendOptions,n),(t=e.options=Fe(a,e.extendOptions)).name&&(t.components[t.name]=e)}}return t}function wa(e){this._init(e)}function _a(e){e.cid=0;var t=1;e.extend=function(e){e=e||{};var a=this,n=a.cid,o=e._Ctor||(e._Ctor={});if(o[n])return o[n];var i=e.name||a.options.name;var r=function(e){this._init(e)};return(r.prototype=Object.create(a.prototype)).constructor=r,r.cid=t++,r.options=Fe(a.options,e),r.super=a,r.options.props&&function(e){var t=e.options.props;for(var a in t)fa(e.prototype,"_props",a)}(r),r.options.computed&&function(e){var t=e.options.computed;for(var a in t)ga(e.prototype,a,t[a])}(r),r.extend=a.extend,r.mixin=a.mixin,r.use=a.use,D.forEach((function(e){r[e]=a[e]})),i&&(r.options.components[i]=r),r.superOptions=a.options,r.extendOptions=e,r.sealedOptions=C({},r.options),o[n]=r,r}}function Aa(e){return e&&(e.Ctor.options.name||e.tag)}function xa(e,t){return Array.isArray(e)?e.indexOf(t)>-1:"string"==typeof e?e.split(",").indexOf(t)>-1:!!l(e)&&e.test(t)}function za(e,t){var a=e.cache,n=e.keys,o=e._vnode;for(var i in a){var r=a[i];if(r){var s=r.name;s&&!t(s)&&Sa(a,i,n,o)}}}function Sa(e,t,a,n){var o=e[t];!o||n&&o.tag===n.tag||o.componentInstance.$destroy(),e[t]=null,b(a,t)}!function(e){e.prototype._init=function(e){var t=this;t._uid=ya++,t._isVue=!0,e&&e._isComponent?function(e,t){var a=e.$options=Object.create(e.constructor.options),n=t._parentVnode;a.parent=t.parent,a._parentVnode=n;var o=n.componentOptions;a.propsData=o.propsData,a._parentListeners=o.listeners,a._renderChildren=o.children,a._componentTag=o.tag,t.render&&(a.render=t.render,a.staticRenderFns=t.staticRenderFns)}(t,e):t.$options=Fe(ja(t.constructor),e||{},t),t._renderProxy=t,t._self=t,function(e){var t=e.$options,a=t.parent;if(a&&!t.abstract){for(;a.$options.abstract&&a.$parent;)a=a.$parent;a.$children.push(e)}e.$parent=a,e.$root=a?a.$root:e,e.$children=[],e.$refs={},e._watcher=null,e._inactive=null,e._directInactive=!1,e._isMounted=!1,e._isDestroyed=!1,e._isBeingDestroyed=!1}(t),function(e){e._events=Object.create(null),e._hasHookEvent=!1;var t=e.$options._parentListeners;t&&Yt(e,t)}(t),function(e){e._vnode=null,e._staticTrees=null;var t=e.$options,n=e.$vnode=t._parentVnode,o=n&&n.context;e.$slots=mt(t._renderChildren,o),e.$scopedSlots=a,e._c=function(t,a,n,o){return qt(e,t,a,n,o,!1)},e.$createElement=function(t,a,n,o){return qt(e,t,a,n,o,!0)};var i=n&&n.data;xe(e,"$attrs",i&&i.attrs||a,null,!0),xe(e,"$listeners",t._parentListeners||a,null,!0)}(t),Qt(t,"beforeCreate"),function(e){var t=pt(e.$options.inject,e);t&&(we(!1),Object.keys(t).forEach((function(a){xe(e,a,t[a])})),we(!0))}(t),ha(t),function(e){var t=e.$options.provide;t&&(e._provided="function"==typeof t?t.call(e):t)}(t),Qt(t,"created"),t.$options.el&&t.$mount(t.$options.el)}}(wa),function(e){var t={get:function(){return this._data}},a={get:function(){return this._props}};Object.defineProperty(e.prototype,"$data",t),Object.defineProperty(e.prototype,"$props",a),e.prototype.$set=ze,e.prototype.$delete=Se,e.prototype.$watch=function(e,t,a){if(c(t))return va(this,e,t,a);(a=a||{}).user=!0;var n=new pa(this,e,t,a);if(a.immediate){var o='callback for immediate watcher "'+n.expression+'"';pe(),$e(t,this,[n.value],this,o),me()}return function(){n.teardown()}}}(wa),function(e){var t=/^hook:/;e.prototype.$on=function(e,a){var n=this;if(Array.isArray(e))for(var o=0,i=e.length;o<i;o++)n.$on(e[o],a);else(n._events[e]||(n._events[e]=[])).push(a),t.test(e)&&(n._hasHookEvent=!0);return n},e.prototype.$once=function(e,t){var a=this;function n(){a.$off(e,n),t.apply(a,arguments)}return n.fn=t,a.$on(e,n),a},e.prototype.$off=function(e,t){var a=this;if(!arguments.length)return a._events=Object.create(null),a;if(Array.isArray(e)){for(var n=0,o=e.length;n<o;n++)a.$off(e[n],t);return a}var i,r=a._events[e];if(!r)return a;if(!t)return a._events[e]=null,a;for(var s=r.length;s--;)if((i=r[s])===t||i.fn===t){r.splice(s,1);break}return a},e.prototype.$emit=function(e){var t=this,a=t._events[e];if(a){a=a.length>1?S(a):a;for(var n=S(arguments,1),o='event handler for "'+e+'"',i=0,r=a.length;i<r;i++)$e(a[i],t,n,t,o)}return t}}(wa),function(e){e.prototype._update=function(e,t){var a=this,n=a.$el,o=a._vnode,i=Xt(a);a._vnode=e,a.$el=o?a.__patch__(o,e):a.__patch__(a.$el,e,t,!1),i(),n&&(n.__vue__=null),a.$el&&(a.$el.__vue__=a),a.$vnode&&a.$parent&&a.$vnode===a.$parent._vnode&&(a.$parent.$el=a.$el)},e.prototype.$forceUpdate=function(){this._watcher&&this._watcher.update()},e.prototype.$destroy=function(){var e=this;if(!e._isBeingDestroyed){Qt(e,"beforeDestroy"),e._isBeingDestroyed=!0;var t=e.$parent;!t||t._isBeingDestroyed||e.$options.abstract||b(t.$children,e),e._watcher&&e._watcher.teardown();for(var a=e._watchers.length;a--;)e._watchers[a].teardown();e._data.__ob__&&e._data.__ob__.vmCount--,e._isDestroyed=!0,e.__patch__(e._vnode,null),Qt(e,"destroyed"),e.$off(),e.$el&&(e.$el.__vue__=null),e.$vnode&&(e.$vnode.parent=null)}}}(wa),function(e){Pt(e.prototype),e.prototype.$nextTick=function(e){return tt(e,this)},e.prototype._render=function(){var e,t=this,a=t.$options,n=a.render,o=a._parentVnode;o&&(t.$scopedSlots=dt(o.data.scopedSlots,t.$slots,t.$scopedSlots)),t.$vnode=o;try{Ut=t,e=n.call(t._renderProxy,t.$createElement)}catch(a){Ue(a,t,"render"),e=t._vnode}finally{Ut=null}return Array.isArray(e)&&1===e.length&&(e=e[0]),e instanceof fe||(e=de()),e.parent=o,e}}(wa);var Ca=[String,RegExp,Array],Ea={KeepAlive:{name:"keep-alive",abstract:!0,props:{include:Ca,exclude:Ca,max:[String,Number]},methods:{cacheVNode:function(){var e=this.cache,t=this.keys,a=this.vnodeToCache,n=this.keyToCache;if(a){var o=a.tag,i=a.componentInstance,r=a.componentOptions;e[n]={name:Aa(r),tag:o,componentInstance:i},t.push(n),this.max&&t.length>parseInt(this.max)&&Sa(e,t[0],t,this._vnode),this.vnodeToCache=null}}},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){for(var e in this.cache)Sa(this.cache,e,this.keys)},mounted:function(){var e=this;this.cacheVNode(),this.$watch("include",(function(t){za(e,(function(e){return xa(t,e)}))})),this.$watch("exclude",(function(t){za(e,(function(e){return!xa(t,e)}))}))},updated:function(){this.cacheVNode()},render:function(){var e=this.$slots.default,t=Ht(e),a=t&&t.componentOptions;if(a){var n=Aa(a),o=this.include,i=this.exclude;if(o&&(!n||!xa(o,n))||i&&n&&xa(i,n))return t;var r=this.cache,s=this.keys,u=null==t.key?a.Ctor.cid+(a.tag?"::"+a.tag:""):t.key;r[u]?(t.componentInstance=r[u].componentInstance,b(s,u),s.push(u)):(this.vnodeToCache=t,this.keyToCache=u),t.data.keepAlive=!0}return t||e&&e[0]}}};!function(e){var t={get:function(){return N}};Object.defineProperty(e,"config",t),e.util={warn:se,extend:C,mergeOptions:Fe,defineReactive:xe},e.set=ze,e.delete=Se,e.nextTick=tt,e.observable=function(e){return Ae(e),e},e.options=Object.create(null),D.forEach((function(t){e.options[t+"s"]=Object.create(null)})),e.options._base=e,C(e.options.components,Ea),function(e){e.use=function(e){var t=this._installedPlugins||(this._installedPlugins=[]);if(t.indexOf(e)>-1)return this;var a=S(arguments,1);return a.unshift(this),"function"==typeof e.install?e.install.apply(e,a):"function"==typeof e&&e.apply(null,a),t.push(e),this}}(e),function(e){e.mixin=function(e){return this.options=Fe(this.options,e),this}}(e),_a(e),function(e){D.forEach((function(t){e[t]=function(e,a){return a?("component"===t&&c(a)&&(a.name=a.name||e,a=this.options._base.extend(a)),"directive"===t&&"function"==typeof a&&(a={bind:a,update:a}),this.options[t+"s"][e]=a,a):this.options[t+"s"][e]}}))}(e)}(wa),Object.defineProperty(wa.prototype,"$isServer",{get:ae}),Object.defineProperty(wa.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(wa,"FunctionalRenderContext",{value:Mt}),wa.version="2.6.14";var Ta=d("style,class"),Ia=d("input,textarea,option,select,progress"),Pa=d("contenteditable,draggable,spellcheck"),Ma=d("events,caret,typing,plaintext-only"),Oa=d("allowfullscreen,async,autofocus,autoplay,checked,compact,controls,declare,default,defaultchecked,defaultmuted,defaultselected,defer,disabled,enabled,formnovalidate,hidden,indeterminate,inert,ismap,itemscope,loop,multiple,muted,nohref,noresize,noshade,novalidate,nowrap,open,pauseonexit,readonly,required,reversed,scoped,seamless,selected,sortable,truespeed,typemustmatch,visible"),Fa="http://www.w3.org/1999/xlink",Da=function(e){return":"===e.charAt(5)&&"xlink"===e.slice(0,5)},La=function(e){return Da(e)?e.slice(6,e.length):""},Na=function(e){return null==e||!1===e};function Ra(e){for(var t=e.data,a=e,n=e;o(n.componentInstance);)(n=n.componentInstance._vnode)&&n.data&&(t=qa(n.data,t));for(;o(a=a.parent);)a&&a.data&&(t=qa(t,a.data));return function(e,t){if(o(e)||o(t))return Ba(e,Ua(t));return""}(t.staticClass,t.class)}function qa(e,t){return{staticClass:Ba(e.staticClass,t.staticClass),class:o(e.class)?[e.class,t.class]:t.class}}function Ba(e,t){return e?t?e+" "+t:e:t||""}function Ua(e){return Array.isArray(e)?function(e){for(var t,a="",n=0,i=e.length;n<i;n++)o(t=Ua(e[n]))&&""!==t&&(a&&(a+=" "),a+=t);return a}(e):s(e)?function(e){var t="";for(var a in e)e[a]&&(t&&(t+=" "),t+=a);return t}(e):"string"==typeof e?e:""}var $a={svg:"http://www.w3.org/2000/svg",math:"http://www.w3.org/1998/Math/MathML"},Ha=d("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,menuitem,summary,content,element,shadow,template,blockquote,iframe,tfoot"),Ga=d("svg,animate,circle,clippath,cursor,defs,desc,ellipse,filter,font-face,foreignobject,g,glyph,image,line,marker,mask,missing-glyph,path,pattern,polygon,polyline,rect,switch,symbol,text,textpath,tspan,use,view",!0),Wa=function(e){return Ha(e)||Ga(e)};var Va=Object.create(null);var Ya=d("text,number,password,search,email,tel,url");var Ka=Object.freeze({createElement:function(e,t){var a=document.createElement(e);return"select"!==e||t.data&&t.data.attrs&&void 0!==t.data.attrs.multiple&&a.setAttribute("multiple","multiple"),a},createElementNS:function(e,t){return document.createElementNS($a[e],t)},createTextNode:function(e){return document.createTextNode(e)},createComment:function(e){return document.createComment(e)},insertBefore:function(e,t,a){e.insertBefore(t,a)},removeChild:function(e,t){e.removeChild(t)},appendChild:function(e,t){e.appendChild(t)},parentNode:function(e){return e.parentNode},nextSibling:function(e){return e.nextSibling},tagName:function(e){return e.tagName},setTextContent:function(e,t){e.textContent=t},setStyleScope:function(e,t){e.setAttribute(t,"")}}),Xa={create:function(e,t){Ja(t)},update:function(e,t){e.data.ref!==t.data.ref&&(Ja(e,!0),Ja(t))},destroy:function(e){Ja(e,!0)}};function Ja(e,t){var a=e.data.ref;if(o(a)){var n=e.context,i=e.componentInstance||e.elm,r=n.$refs;t?Array.isArray(r[a])?b(r[a],i):r[a]===i&&(r[a]=void 0):e.data.refInFor?Array.isArray(r[a])?r[a].indexOf(i)<0&&r[a].push(i):r[a]=[i]:r[a]=i}}var Za=new fe("",{},[]),Qa=["create","activate","update","remove","destroy"];function en(e,t){return e.key===t.key&&e.asyncFactory===t.asyncFactory&&(e.tag===t.tag&&e.isComment===t.isComment&&o(e.data)===o(t.data)&&function(e,t){if("input"!==e.tag)return!0;var a,n=o(a=e.data)&&o(a=a.attrs)&&a.type,i=o(a=t.data)&&o(a=a.attrs)&&a.type;return n===i||Ya(n)&&Ya(i)}(e,t)||i(e.isAsyncPlaceholder)&&n(t.asyncFactory.error))}function tn(e,t,a){var n,i,r={};for(n=t;n<=a;++n)o(i=e[n].key)&&(r[i]=n);return r}var an={create:nn,update:nn,destroy:function(e){nn(e,Za)}};function nn(e,t){(e.data.directives||t.data.directives)&&function(e,t){var a,n,o,i=e===Za,r=t===Za,s=rn(e.data.directives,e.context),u=rn(t.data.directives,t.context),c=[],l=[];for(a in u)n=s[a],o=u[a],n?(o.oldValue=n.value,o.oldArg=n.arg,un(o,"update",t,e),o.def&&o.def.componentUpdated&&l.push(o)):(un(o,"bind",t,e),o.def&&o.def.inserted&&c.push(o));if(c.length){var p=function(){for(var a=0;a<c.length;a++)un(c[a],"inserted",t,e)};i?st(t,"insert",p):p()}l.length&&st(t,"postpatch",(function(){for(var a=0;a<l.length;a++)un(l[a],"componentUpdated",t,e)}));if(!i)for(a in s)u[a]||un(s[a],"unbind",e,e,r)}(e,t)}var on=Object.create(null);function rn(e,t){var a,n,o=Object.create(null);if(!e)return o;for(a=0;a<e.length;a++)(n=e[a]).modifiers||(n.modifiers=on),o[sn(n)]=n,n.def=De(t.$options,"directives",n.name);return o}function sn(e){return e.rawName||e.name+"."+Object.keys(e.modifiers||{}).join(".")}function un(e,t,a,n,o){var i=e.def&&e.def[t];if(i)try{i(a.elm,e,a,n,o)}catch(n){Ue(n,a.context,"directive "+e.name+" "+t+" hook")}}var cn=[Xa,an];function ln(e,t){var a=t.componentOptions;if(!(o(a)&&!1===a.Ctor.options.inheritAttrs||n(e.data.attrs)&&n(t.data.attrs))){var i,r,s=t.elm,u=e.data.attrs||{},c=t.data.attrs||{};for(i in o(c.__ob__)&&(c=t.data.attrs=C({},c)),c)r=c[i],u[i]!==r&&pn(s,i,r,t.data.pre);for(i in(Y||X)&&c.value!==u.value&&pn(s,"value",c.value),u)n(c[i])&&(Da(i)?s.removeAttributeNS(Fa,La(i)):Pa(i)||s.removeAttribute(i))}}function pn(e,t,a,n){n||e.tagName.indexOf("-")>-1?mn(e,t,a):Oa(t)?Na(a)?e.removeAttribute(t):(a="allowfullscreen"===t&&"EMBED"===e.tagName?"true":t,e.setAttribute(t,a)):Pa(t)?e.setAttribute(t,function(e,t){return Na(t)||"false"===t?"false":"contenteditable"===e&&Ma(t)?t:"true"}(t,a)):Da(t)?Na(a)?e.removeAttributeNS(Fa,La(t)):e.setAttributeNS(Fa,t,a):mn(e,t,a)}function mn(e,t,a){if(Na(a))e.removeAttribute(t);else{if(Y&&!K&&"TEXTAREA"===e.tagName&&"placeholder"===t&&""!==a&&!e.__ieph){var n=function(t){t.stopImmediatePropagation(),e.removeEventListener("input",n)};e.addEventListener("input",n),e.__ieph=!0}e.setAttribute(t,a)}}var fn={create:ln,update:ln};function hn(e,t){var a=t.elm,i=t.data,r=e.data;if(!(n(i.staticClass)&&n(i.class)&&(n(r)||n(r.staticClass)&&n(r.class)))){var s=Ra(t),u=a._transitionClasses;o(u)&&(s=Ba(s,Ua(u))),s!==a._prevClass&&(a.setAttribute("class",s),a._prevClass=s)}}var dn,gn={create:hn,update:hn};function bn(e,t,a){var n=dn;return function o(){var i=t.apply(null,arguments);null!==i&&yn(e,o,a,n)}}var kn=Ve&&!(Z&&Number(Z[1])<=53);function vn(e,t,a,n){if(kn){var o=ra,i=t;t=i._wrapper=function(e){if(e.target===e.currentTarget||e.timeStamp>=o||e.timeStamp<=0||e.target.ownerDocument!==document)return i.apply(this,arguments)}}dn.addEventListener(e,t,ee?{capture:a,passive:n}:a)}function yn(e,t,a,n){(n||dn).removeEventListener(e,t._wrapper||t,a)}function jn(e,t){if(!n(e.data.on)||!n(t.data.on)){var a=t.data.on||{},i=e.data.on||{};dn=t.elm,function(e){if(o(e.__r)){var t=Y?"change":"input";e[t]=[].concat(e.__r,e[t]||[]),delete e.__r}o(e.__c)&&(e.change=[].concat(e.__c,e.change||[]),delete e.__c)}(a),rt(a,i,vn,yn,bn,t.context),dn=void 0}}var wn,_n={create:jn,update:jn};function An(e,t){if(!n(e.data.domProps)||!n(t.data.domProps)){var a,i,r=t.elm,s=e.data.domProps||{},u=t.data.domProps||{};for(a in o(u.__ob__)&&(u=t.data.domProps=C({},u)),s)a in u||(r[a]="");for(a in u){if(i=u[a],"textContent"===a||"innerHTML"===a){if(t.children&&(t.children.length=0),i===s[a])continue;1===r.childNodes.length&&r.removeChild(r.childNodes[0])}if("value"===a&&"PROGRESS"!==r.tagName){r._value=i;var c=n(i)?"":String(i);xn(r,c)&&(r.value=c)}else if("innerHTML"===a&&Ga(r.tagName)&&n(r.innerHTML)){(wn=wn||document.createElement("div")).innerHTML="<svg>"+i+"</svg>";for(var l=wn.firstChild;r.firstChild;)r.removeChild(r.firstChild);for(;l.firstChild;)r.appendChild(l.firstChild)}else if(i!==s[a])try{r[a]=i}catch(e){}}}}function xn(e,t){return!e.composing&&("OPTION"===e.tagName||function(e,t){var a=!0;try{a=document.activeElement!==e}catch(e){}return a&&e.value!==t}(e,t)||function(e,t){var a=e.value,n=e._vModifiers;if(o(n)){if(n.number)return h(a)!==h(t);if(n.trim)return a.trim()!==t.trim()}return a!==t}(e,t))}var zn={create:An,update:An},Sn=y((function(e){var t={},a=/:(.+)/;return e.split(/;(?![^(]*\))/g).forEach((function(e){if(e){var n=e.split(a);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}));function Cn(e){var t=En(e.style);return e.staticStyle?C(e.staticStyle,t):t}function En(e){return Array.isArray(e)?E(e):"string"==typeof e?Sn(e):e}var Tn,In=/^--/,Pn=/\s*!important$/,Mn=function(e,t,a){if(In.test(t))e.style.setProperty(t,a);else if(Pn.test(a))e.style.setProperty(x(t),a.replace(Pn,""),"important");else{var n=Fn(t);if(Array.isArray(a))for(var o=0,i=a.length;o<i;o++)e.style[n]=a[o];else e.style[n]=a}},On=["Webkit","Moz","ms"],Fn=y((function(e){if(Tn=Tn||document.createElement("div").style,"filter"!==(e=w(e))&&e in Tn)return e;for(var t=e.charAt(0).toUpperCase()+e.slice(1),a=0;a<On.length;a++){var n=On[a]+t;if(n in Tn)return n}}));function Dn(e,t){var a=t.data,i=e.data;if(!(n(a.staticStyle)&&n(a.style)&&n(i.staticStyle)&&n(i.style))){var r,s,u=t.elm,c=i.staticStyle,l=i.normalizedStyle||i.style||{},p=c||l,m=En(t.data.style)||{};t.data.normalizedStyle=o(m.__ob__)?C({},m):m;var f=function(e,t){var a,n={};if(t)for(var o=e;o.componentInstance;)(o=o.componentInstance._vnode)&&o.data&&(a=Cn(o.data))&&C(n,a);(a=Cn(e.data))&&C(n,a);for(var i=e;i=i.parent;)i.data&&(a=Cn(i.data))&&C(n,a);return n}(t,!0);for(s in p)n(f[s])&&Mn(u,s,"");for(s in f)(r=f[s])!==p[s]&&Mn(u,s,null==r?"":r)}}var Ln={create:Dn,update:Dn},Nn=/\s+/;function Rn(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(Nn).forEach((function(t){return e.classList.add(t)})):e.classList.add(t);else{var a=" "+(e.getAttribute("class")||"")+" ";a.indexOf(" "+t+" ")<0&&e.setAttribute("class",(a+t).trim())}}function qn(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(Nn).forEach((function(t){return e.classList.remove(t)})):e.classList.remove(t),e.classList.length||e.removeAttribute("class");else{for(var a=" "+(e.getAttribute("class")||"")+" ",n=" "+t+" ";a.indexOf(n)>=0;)a=a.replace(n," ");(a=a.trim())?e.setAttribute("class",a):e.removeAttribute("class")}}function Bn(e){if(e){if("object"==typeof e){var t={};return!1!==e.css&&C(t,Un(e.name||"v")),C(t,e),t}return"string"==typeof e?Un(e):void 0}}var Un=y((function(e){return{enterClass:e+"-enter",enterToClass:e+"-enter-to",enterActiveClass:e+"-enter-active",leaveClass:e+"-leave",leaveToClass:e+"-leave-to",leaveActiveClass:e+"-leave-active"}})),$n=H&&!K,Hn="transition",Gn="transitionend",Wn="animation",Vn="animationend";$n&&(void 0===window.ontransitionend&&void 0!==window.onwebkittransitionend&&(Hn="WebkitTransition",Gn="webkitTransitionEnd"),void 0===window.onanimationend&&void 0!==window.onwebkitanimationend&&(Wn="WebkitAnimation",Vn="webkitAnimationEnd"));var Yn=H?window.requestAnimationFrame?window.requestAnimationFrame.bind(window):setTimeout:function(e){return e()};function Kn(e){Yn((function(){Yn(e)}))}function Xn(e,t){var a=e._transitionClasses||(e._transitionClasses=[]);a.indexOf(t)<0&&(a.push(t),Rn(e,t))}function Jn(e,t){e._transitionClasses&&b(e._transitionClasses,t),qn(e,t)}function Zn(e,t,a){var n=eo(e,t),o=n.type,i=n.timeout,r=n.propCount;if(!o)return a();var s="transition"===o?Gn:Vn,u=0,c=function(){e.removeEventListener(s,l),a()},l=function(t){t.target===e&&++u>=r&&c()};setTimeout((function(){u<r&&c()}),i+1),e.addEventListener(s,l)}var Qn=/\b(transform|all)(,|$)/;function eo(e,t){var a,n=window.getComputedStyle(e),o=(n[Hn+"Delay"]||"").split(", "),i=(n[Hn+"Duration"]||"").split(", "),r=to(o,i),s=(n[Wn+"Delay"]||"").split(", "),u=(n[Wn+"Duration"]||"").split(", "),c=to(s,u),l=0,p=0;return"transition"===t?r>0&&(a="transition",l=r,p=i.length):"animation"===t?c>0&&(a="animation",l=c,p=u.length):p=(a=(l=Math.max(r,c))>0?r>c?"transition":"animation":null)?"transition"===a?i.length:u.length:0,{type:a,timeout:l,propCount:p,hasTransform:"transition"===a&&Qn.test(n[Hn+"Property"])}}function to(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max.apply(null,t.map((function(t,a){return ao(t)+ao(e[a])})))}function ao(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}function no(e,t){var a=e.elm;o(a._leaveCb)&&(a._leaveCb.cancelled=!0,a._leaveCb());var i=Bn(e.data.transition);if(!n(i)&&!o(a._enterCb)&&1===a.nodeType){for(var r=i.css,u=i.type,c=i.enterClass,l=i.enterToClass,p=i.enterActiveClass,m=i.appearClass,f=i.appearToClass,d=i.appearActiveClass,g=i.beforeEnter,b=i.enter,k=i.afterEnter,v=i.enterCancelled,y=i.beforeAppear,j=i.appear,w=i.afterAppear,_=i.appearCancelled,A=i.duration,x=Kt,z=Kt.$vnode;z&&z.parent;)x=z.context,z=z.parent;var S=!x._isMounted||!e.isRootInsert;if(!S||j||""===j){var C=S&&m?m:c,E=S&&d?d:p,T=S&&f?f:l,I=S&&y||g,P=S&&"function"==typeof j?j:b,M=S&&w||k,O=S&&_||v,D=h(s(A)?A.enter:A);0;var L=!1!==r&&!K,N=ro(P),R=a._enterCb=F((function(){L&&(Jn(a,T),Jn(a,E)),R.cancelled?(L&&Jn(a,C),O&&O(a)):M&&M(a),a._enterCb=null}));e.data.show||st(e,"insert",(function(){var t=a.parentNode,n=t&&t._pending&&t._pending[e.key];n&&n.tag===e.tag&&n.elm._leaveCb&&n.elm._leaveCb(),P&&P(a,R)})),I&&I(a),L&&(Xn(a,C),Xn(a,E),Kn((function(){Jn(a,C),R.cancelled||(Xn(a,T),N||(io(D)?setTimeout(R,D):Zn(a,u,R)))}))),e.data.show&&(t&&t(),P&&P(a,R)),L||N||R()}}}function oo(e,t){var a=e.elm;o(a._enterCb)&&(a._enterCb.cancelled=!0,a._enterCb());var i=Bn(e.data.transition);if(n(i)||1!==a.nodeType)return t();if(!o(a._leaveCb)){var r=i.css,u=i.type,c=i.leaveClass,l=i.leaveToClass,p=i.leaveActiveClass,m=i.beforeLeave,f=i.leave,d=i.afterLeave,g=i.leaveCancelled,b=i.delayLeave,k=i.duration,v=!1!==r&&!K,y=ro(f),j=h(s(k)?k.leave:k);0;var w=a._leaveCb=F((function(){a.parentNode&&a.parentNode._pending&&(a.parentNode._pending[e.key]=null),v&&(Jn(a,l),Jn(a,p)),w.cancelled?(v&&Jn(a,c),g&&g(a)):(t(),d&&d(a)),a._leaveCb=null}));b?b(_):_()}function _(){w.cancelled||(!e.data.show&&a.parentNode&&((a.parentNode._pending||(a.parentNode._pending={}))[e.key]=e),m&&m(a),v&&(Xn(a,c),Xn(a,p),Kn((function(){Jn(a,c),w.cancelled||(Xn(a,l),y||(io(j)?setTimeout(w,j):Zn(a,u,w)))}))),f&&f(a,w),v||y||w())}}function io(e){return"number"==typeof e&&!isNaN(e)}function ro(e){if(n(e))return!1;var t=e.fns;return o(t)?ro(Array.isArray(t)?t[0]:t):(e._length||e.length)>1}function so(e,t){!0!==t.data.show&&no(t)}var uo=function(e){var t,a,s={},u=e.modules,c=e.nodeOps;for(t=0;t<Qa.length;++t)for(s[Qa[t]]=[],a=0;a<u.length;++a)o(u[a][Qa[t]])&&s[Qa[t]].push(u[a][Qa[t]]);function l(e){var t=c.parentNode(e);o(t)&&c.removeChild(t,e)}function p(e,t,a,n,r,u,l){if(o(e.elm)&&o(u)&&(e=u[l]=be(e)),e.isRootInsert=!r,!function(e,t,a,n){var r=e.data;if(o(r)){var u=o(e.componentInstance)&&r.keepAlive;if(o(r=r.hook)&&o(r=r.init)&&r(e,!1),o(e.componentInstance))return m(e,t),f(a,e.elm,n),i(u)&&function(e,t,a,n){var i,r=e;for(;r.componentInstance;)if(r=r.componentInstance._vnode,o(i=r.data)&&o(i=i.transition)){for(i=0;i<s.activate.length;++i)s.activate[i](Za,r);t.push(r);break}f(a,e.elm,n)}(e,t,a,n),!0}}(e,t,a,n)){var p=e.data,d=e.children,g=e.tag;o(g)?(e.elm=e.ns?c.createElementNS(e.ns,g):c.createElement(g,e),k(e),h(e,d,t),o(p)&&b(e,t),f(a,e.elm,n)):i(e.isComment)?(e.elm=c.createComment(e.text),f(a,e.elm,n)):(e.elm=c.createTextNode(e.text),f(a,e.elm,n))}}function m(e,t){o(e.data.pendingInsert)&&(t.push.apply(t,e.data.pendingInsert),e.data.pendingInsert=null),e.elm=e.componentInstance.$el,g(e)?(b(e,t),k(e)):(Ja(e),t.push(e))}function f(e,t,a){o(e)&&(o(a)?c.parentNode(a)===e&&c.insertBefore(e,t,a):c.appendChild(e,t))}function h(e,t,a){if(Array.isArray(t)){0;for(var n=0;n<t.length;++n)p(t[n],a,e.elm,null,!0,t,n)}else r(e.text)&&c.appendChild(e.elm,c.createTextNode(String(e.text)))}function g(e){for(;e.componentInstance;)e=e.componentInstance._vnode;return o(e.tag)}function b(e,a){for(var n=0;n<s.create.length;++n)s.create[n](Za,e);o(t=e.data.hook)&&(o(t.create)&&t.create(Za,e),o(t.insert)&&a.push(e))}function k(e){var t;if(o(t=e.fnScopeId))c.setStyleScope(e.elm,t);else for(var a=e;a;)o(t=a.context)&&o(t=t.$options._scopeId)&&c.setStyleScope(e.elm,t),a=a.parent;o(t=Kt)&&t!==e.context&&t!==e.fnContext&&o(t=t.$options._scopeId)&&c.setStyleScope(e.elm,t)}function v(e,t,a,n,o,i){for(;n<=o;++n)p(a[n],i,e,t,!1,a,n)}function y(e){var t,a,n=e.data;if(o(n))for(o(t=n.hook)&&o(t=t.destroy)&&t(e),t=0;t<s.destroy.length;++t)s.destroy[t](e);if(o(t=e.children))for(a=0;a<e.children.length;++a)y(e.children[a])}function j(e,t,a){for(;t<=a;++t){var n=e[t];o(n)&&(o(n.tag)?(w(n),y(n)):l(n.elm))}}function w(e,t){if(o(t)||o(e.data)){var a,n=s.remove.length+1;for(o(t)?t.listeners+=n:t=function(e,t){function a(){0==--a.listeners&&l(e)}return a.listeners=t,a}(e.elm,n),o(a=e.componentInstance)&&o(a=a._vnode)&&o(a.data)&&w(a,t),a=0;a<s.remove.length;++a)s.remove[a](e,t);o(a=e.data.hook)&&o(a=a.remove)?a(e,t):t()}else l(e.elm)}function _(e,t,a,n){for(var i=a;i<n;i++){var r=t[i];if(o(r)&&en(e,r))return i}}function A(e,t,a,r,u,l){if(e!==t){o(t.elm)&&o(r)&&(t=r[u]=be(t));var m=t.elm=e.elm;if(i(e.isAsyncPlaceholder))o(t.asyncFactory.resolved)?S(e.elm,t,a):t.isAsyncPlaceholder=!0;else if(i(t.isStatic)&&i(e.isStatic)&&t.key===e.key&&(i(t.isCloned)||i(t.isOnce)))t.componentInstance=e.componentInstance;else{var f,h=t.data;o(h)&&o(f=h.hook)&&o(f=f.prepatch)&&f(e,t);var d=e.children,b=t.children;if(o(h)&&g(t)){for(f=0;f<s.update.length;++f)s.update[f](e,t);o(f=h.hook)&&o(f=f.update)&&f(e,t)}n(t.text)?o(d)&&o(b)?d!==b&&function(e,t,a,i,r){var s,u,l,m=0,f=0,h=t.length-1,d=t[0],g=t[h],b=a.length-1,k=a[0],y=a[b],w=!r;for(0;m<=h&&f<=b;)n(d)?d=t[++m]:n(g)?g=t[--h]:en(d,k)?(A(d,k,i,a,f),d=t[++m],k=a[++f]):en(g,y)?(A(g,y,i,a,b),g=t[--h],y=a[--b]):en(d,y)?(A(d,y,i,a,b),w&&c.insertBefore(e,d.elm,c.nextSibling(g.elm)),d=t[++m],y=a[--b]):en(g,k)?(A(g,k,i,a,f),w&&c.insertBefore(e,g.elm,d.elm),g=t[--h],k=a[++f]):(n(s)&&(s=tn(t,m,h)),n(u=o(k.key)?s[k.key]:_(k,t,m,h))?p(k,i,e,d.elm,!1,a,f):en(l=t[u],k)?(A(l,k,i,a,f),t[u]=void 0,w&&c.insertBefore(e,l.elm,d.elm)):p(k,i,e,d.elm,!1,a,f),k=a[++f]);m>h?v(e,n(a[b+1])?null:a[b+1].elm,a,f,b,i):f>b&&j(t,m,h)}(m,d,b,a,l):o(b)?(o(e.text)&&c.setTextContent(m,""),v(m,null,b,0,b.length-1,a)):o(d)?j(d,0,d.length-1):o(e.text)&&c.setTextContent(m,""):e.text!==t.text&&c.setTextContent(m,t.text),o(h)&&o(f=h.hook)&&o(f=f.postpatch)&&f(e,t)}}}function x(e,t,a){if(i(a)&&o(e.parent))e.parent.data.pendingInsert=t;else for(var n=0;n<t.length;++n)t[n].data.hook.insert(t[n])}var z=d("attrs,class,staticClass,staticStyle,key");function S(e,t,a,n){var r,s=t.tag,u=t.data,c=t.children;if(n=n||u&&u.pre,t.elm=e,i(t.isComment)&&o(t.asyncFactory))return t.isAsyncPlaceholder=!0,!0;if(o(u)&&(o(r=u.hook)&&o(r=r.init)&&r(t,!0),o(r=t.componentInstance)))return m(t,a),!0;if(o(s)){if(o(c))if(e.hasChildNodes())if(o(r=u)&&o(r=r.domProps)&&o(r=r.innerHTML)){if(r!==e.innerHTML)return!1}else{for(var l=!0,p=e.firstChild,f=0;f<c.length;f++){if(!p||!S(p,c[f],a,n)){l=!1;break}p=p.nextSibling}if(!l||p)return!1}else h(t,c,a);if(o(u)){var d=!1;for(var g in u)if(!z(g)){d=!0,b(t,a);break}!d&&u.class&&nt(u.class)}}else e.data!==t.text&&(e.data=t.text);return!0}return function(e,t,a,r){if(!n(t)){var u,l=!1,m=[];if(n(e))l=!0,p(t,m);else{var f=o(e.nodeType);if(!f&&en(e,t))A(e,t,m,null,null,r);else{if(f){if(1===e.nodeType&&e.hasAttribute("data-server-rendered")&&(e.removeAttribute("data-server-rendered"),a=!0),i(a)&&S(e,t,m))return x(t,m,!0),e;u=e,e=new fe(c.tagName(u).toLowerCase(),{},[],void 0,u)}var h=e.elm,d=c.parentNode(h);if(p(t,m,h._leaveCb?null:d,c.nextSibling(h)),o(t.parent))for(var b=t.parent,k=g(t);b;){for(var v=0;v<s.destroy.length;++v)s.destroy[v](b);if(b.elm=t.elm,k){for(var w=0;w<s.create.length;++w)s.create[w](Za,b);var _=b.data.hook.insert;if(_.merged)for(var z=1;z<_.fns.length;z++)_.fns[z]()}else Ja(b);b=b.parent}o(d)?j([e],0,0):o(e.tag)&&y(e)}}return x(t,m,l),t.elm}o(e)&&y(e)}}({nodeOps:Ka,modules:[fn,gn,_n,zn,Ln,H?{create:so,activate:so,remove:function(e,t){!0!==e.data.show?oo(e,t):t()}}:{}].concat(cn)});K&&document.addEventListener("selectionchange",(function(){var e=document.activeElement;e&&e.vmodel&&bo(e,"input")}));var co={inserted:function(e,t,a,n){"select"===a.tag?(n.elm&&!n.elm._vOptions?st(a,"postpatch",(function(){co.componentUpdated(e,t,a)})):lo(e,t,a.context),e._vOptions=[].map.call(e.options,fo)):("textarea"===a.tag||Ya(e.type))&&(e._vModifiers=t.modifiers,t.modifiers.lazy||(e.addEventListener("compositionstart",ho),e.addEventListener("compositionend",go),e.addEventListener("change",go),K&&(e.vmodel=!0)))},componentUpdated:function(e,t,a){if("select"===a.tag){lo(e,t,a.context);var n=e._vOptions,o=e._vOptions=[].map.call(e.options,fo);if(o.some((function(e,t){return!M(e,n[t])})))(e.multiple?t.value.some((function(e){return mo(e,o)})):t.value!==t.oldValue&&mo(t.value,o))&&bo(e,"change")}}};function lo(e,t,a){po(e,t,a),(Y||X)&&setTimeout((function(){po(e,t,a)}),0)}function po(e,t,a){var n=t.value,o=e.multiple;if(!o||Array.isArray(n)){for(var i,r,s=0,u=e.options.length;s<u;s++)if(r=e.options[s],o)i=O(n,fo(r))>-1,r.selected!==i&&(r.selected=i);else if(M(fo(r),n))return void(e.selectedIndex!==s&&(e.selectedIndex=s));o||(e.selectedIndex=-1)}}function mo(e,t){return t.every((function(t){return!M(t,e)}))}function fo(e){return"_value"in e?e._value:e.value}function ho(e){e.target.composing=!0}function go(e){e.target.composing&&(e.target.composing=!1,bo(e.target,"input"))}function bo(e,t){var a=document.createEvent("HTMLEvents");a.initEvent(t,!0,!0),e.dispatchEvent(a)}function ko(e){return!e.componentInstance||e.data&&e.data.transition?e:ko(e.componentInstance._vnode)}var vo={model:co,show:{bind:function(e,t,a){var n=t.value,o=(a=ko(a)).data&&a.data.transition,i=e.__vOriginalDisplay="none"===e.style.display?"":e.style.display;n&&o?(a.data.show=!0,no(a,(function(){e.style.display=i}))):e.style.display=n?i:"none"},update:function(e,t,a){var n=t.value;!n!=!t.oldValue&&((a=ko(a)).data&&a.data.transition?(a.data.show=!0,n?no(a,(function(){e.style.display=e.__vOriginalDisplay})):oo(a,(function(){e.style.display="none"}))):e.style.display=n?e.__vOriginalDisplay:"none")},unbind:function(e,t,a,n,o){o||(e.style.display=e.__vOriginalDisplay)}}},yo={name:String,appear:Boolean,css:Boolean,mode:String,type:String,enterClass:String,leaveClass:String,enterToClass:String,leaveToClass:String,enterActiveClass:String,leaveActiveClass:String,appearClass:String,appearActiveClass:String,appearToClass:String,duration:[Number,String,Object]};function jo(e){var t=e&&e.componentOptions;return t&&t.Ctor.options.abstract?jo(Ht(t.children)):e}function wo(e){var t={},a=e.$options;for(var n in a.propsData)t[n]=e[n];var o=a._parentListeners;for(var i in o)t[w(i)]=o[i];return t}function _o(e,t){if(/\d-keep-alive$/.test(t.tag))return e("keep-alive",{props:t.componentOptions.propsData})}var Ao=function(e){return e.tag||ht(e)},xo=function(e){return"show"===e.name},zo={name:"transition",props:yo,abstract:!0,render:function(e){var t=this,a=this.$slots.default;if(a&&(a=a.filter(Ao)).length){0;var n=this.mode;0;var o=a[0];if(function(e){for(;e=e.parent;)if(e.data.transition)return!0}(this.$vnode))return o;var i=jo(o);if(!i)return o;if(this._leaving)return _o(e,o);var s="__transition-"+this._uid+"-";i.key=null==i.key?i.isComment?s+"comment":s+i.tag:r(i.key)?0===String(i.key).indexOf(s)?i.key:s+i.key:i.key;var u=(i.data||(i.data={})).transition=wo(this),c=this._vnode,l=jo(c);if(i.data.directives&&i.data.directives.some(xo)&&(i.data.show=!0),l&&l.data&&!function(e,t){return t.key===e.key&&t.tag===e.tag}(i,l)&&!ht(l)&&(!l.componentInstance||!l.componentInstance._vnode.isComment)){var p=l.data.transition=C({},u);if("out-in"===n)return this._leaving=!0,st(p,"afterLeave",(function(){t._leaving=!1,t.$forceUpdate()})),_o(e,o);if("in-out"===n){if(ht(i))return c;var m,f=function(){m()};st(u,"afterEnter",f),st(u,"enterCancelled",f),st(p,"delayLeave",(function(e){m=e}))}}return o}}},So=C({tag:String,moveClass:String},yo);function Co(e){e.elm._moveCb&&e.elm._moveCb(),e.elm._enterCb&&e.elm._enterCb()}function Eo(e){e.data.newPos=e.elm.getBoundingClientRect()}function To(e){var t=e.data.pos,a=e.data.newPos,n=t.left-a.left,o=t.top-a.top;if(n||o){e.data.moved=!0;var i=e.elm.style;i.transform=i.WebkitTransform="translate("+n+"px,"+o+"px)",i.transitionDuration="0s"}}delete So.mode;var Io={Transition:zo,TransitionGroup:{props:So,beforeMount:function(){var e=this,t=this._update;this._update=function(a,n){var o=Xt(e);e.__patch__(e._vnode,e.kept,!1,!0),e._vnode=e.kept,o(),t.call(e,a,n)}},render:function(e){for(var t=this.tag||this.$vnode.data.tag||"span",a=Object.create(null),n=this.prevChildren=this.children,o=this.$slots.default||[],i=this.children=[],r=wo(this),s=0;s<o.length;s++){var u=o[s];if(u.tag)if(null!=u.key&&0!==String(u.key).indexOf("__vlist"))i.push(u),a[u.key]=u,(u.data||(u.data={})).transition=r;else;}if(n){for(var c=[],l=[],p=0;p<n.length;p++){var m=n[p];m.data.transition=r,m.data.pos=m.elm.getBoundingClientRect(),a[m.key]?c.push(m):l.push(m)}this.kept=e(t,null,c),this.removed=l}return e(t,null,i)},updated:function(){var e=this.prevChildren,t=this.moveClass||(this.name||"v")+"-move";e.length&&this.hasMove(e[0].elm,t)&&(e.forEach(Co),e.forEach(Eo),e.forEach(To),this._reflow=document.body.offsetHeight,e.forEach((function(e){if(e.data.moved){var a=e.elm,n=a.style;Xn(a,t),n.transform=n.WebkitTransform=n.transitionDuration="",a.addEventListener(Gn,a._moveCb=function e(n){n&&n.target!==a||n&&!/transform$/.test(n.propertyName)||(a.removeEventListener(Gn,e),a._moveCb=null,Jn(a,t))})}})))},methods:{hasMove:function(e,t){if(!$n)return!1;if(this._hasMove)return this._hasMove;var a=e.cloneNode();e._transitionClasses&&e._transitionClasses.forEach((function(e){qn(a,e)})),Rn(a,t),a.style.display="none",this.$el.appendChild(a);var n=eo(a);return this.$el.removeChild(a),this._hasMove=n.hasTransform}}}};wa.config.mustUseProp=function(e,t,a){return"value"===a&&Ia(e)&&"button"!==t||"selected"===a&&"option"===e||"checked"===a&&"input"===e||"muted"===a&&"video"===e},wa.config.isReservedTag=Wa,wa.config.isReservedAttr=Ta,wa.config.getTagNamespace=function(e){return Ga(e)?"svg":"math"===e?"math":void 0},wa.config.isUnknownElement=function(e){if(!H)return!0;if(Wa(e))return!1;if(e=e.toLowerCase(),null!=Va[e])return Va[e];var t=document.createElement(e);return e.indexOf("-")>-1?Va[e]=t.constructor===window.HTMLUnknownElement||t.constructor===window.HTMLElement:Va[e]=/HTMLUnknownElement/.test(t.toString())},C(wa.options.directives,vo),C(wa.options.components,Io),wa.prototype.__patch__=H?uo:T,wa.prototype.$mount=function(e,t){return function(e,t,a){var n;return e.$el=t,e.$options.render||(e.$options.render=de),Qt(e,"beforeMount"),n=function(){e._update(e._render(),a)},new pa(e,n,T,{before:function(){e._isMounted&&!e._isDestroyed&&Qt(e,"beforeUpdate")}},!0),a=!1,null==e.$vnode&&(e._isMounted=!0,Qt(e,"mounted")),e}(this,e=e&&H?function(e){if("string"==typeof e){var t=document.querySelector(e);return t||document.createElement("div")}return e}(e):void 0,t)},H&&setTimeout((function(){N.devtools&&ne&&ne.emit("init",wa)}),0),t.a=wa}).call(this,a(20).setImmediate)},10:function(e,t,a){"use strict";var n=a(11),o={};o.rules=a(12).map((function(e){return{rule:e,suffix:e.replace(/^(\*\.|\!)/,""),punySuffix:-1,wildcard:"*"===e.charAt(0),exception:"!"===e.charAt(0)}})),o.endsWith=function(e,t){return-1!==e.indexOf(t,e.length-t.length)},o.findRule=function(e){var t=n.toASCII(e);return o.rules.reduce((function(e,a){return-1===a.punySuffix&&(a.punySuffix=n.toASCII(a.suffix)),o.endsWith(t,"."+a.punySuffix)||t===a.punySuffix?a:e}),null)},t.errorCodes={DOMAIN_TOO_SHORT:"Domain name too short.",DOMAIN_TOO_LONG:"Domain name too long. It should be no more than 255 chars.",LABEL_STARTS_WITH_DASH:"Domain name label can not start with a dash.",LABEL_ENDS_WITH_DASH:"Domain name label can not end with a dash.",LABEL_TOO_LONG:"Domain name label should be at most 63 chars long.",LABEL_TOO_SHORT:"Domain name label should be at least 1 character long.",LABEL_INVALID_CHARS:"Domain name label can only contain alphanumeric characters or dashes."},o.validate=function(e){var t=n.toASCII(e);if(t.length<1)return"DOMAIN_TOO_SHORT";if(t.length>255)return"DOMAIN_TOO_LONG";for(var a,o=t.split("."),i=0;i<o.length;++i){if(!(a=o[i]).length)return"LABEL_TOO_SHORT";if(a.length>63)return"LABEL_TOO_LONG";if("-"===a.charAt(0))return"LABEL_STARTS_WITH_DASH";if("-"===a.charAt(a.length-1))return"LABEL_ENDS_WITH_DASH";if(!/^[a-z0-9\-]+$/.test(a))return"LABEL_INVALID_CHARS"}},t.parse=function(e){if("string"!=typeof e)throw new TypeError("Domain name must be a string.");var a=e.slice(0).toLowerCase();"."===a.charAt(a.length-1)&&(a=a.slice(0,a.length-1));var i=o.validate(a);if(i)return{input:e,error:{message:t.errorCodes[i],code:i}};var r={input:e,tld:null,sld:null,domain:null,subdomain:null,listed:!1},s=a.split(".");if("local"===s[s.length-1])return r;var u=function(){return/xn--/.test(a)?(r.domain&&(r.domain=n.toASCII(r.domain)),r.subdomain&&(r.subdomain=n.toASCII(r.subdomain)),r):r},c=o.findRule(a);if(!c)return s.length<2?r:(r.tld=s.pop(),r.sld=s.pop(),r.domain=[r.sld,r.tld].join("."),s.length&&(r.subdomain=s.pop()),u());r.listed=!0;var l=c.suffix.split("."),p=s.slice(0,s.length-l.length);return c.exception&&p.push(l.shift()),r.tld=l.join("."),p.length?(c.wildcard&&(l.unshift(p.pop()),r.tld=l.join(".")),p.length?(r.sld=p.pop(),r.domain=[r.sld,r.tld].join("."),p.length&&(r.subdomain=p.join(".")),u()):u()):u()},t.get=function(e){return e&&t.parse(e).domain||null},t.isValid=function(e){var a=t.parse(e);return Boolean(a.domain&&a.listed)}},11:function(e,t,a){(function(e){var n;/*! https://mths.be/punycode v1.4.1 by @mathias */!function(o){t&&t.nodeType,e&&e.nodeType;var i="object"==typeof window&&window;i.global!==i&&i.window!==i&&i.self;var r,s=2147483647,u=/^xn--/,c=/[^\x20-\x7E]/,l=/[\x2E\u3002\uFF0E\uFF61]/g,p={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},m=Math.floor,f=String.fromCharCode;function h(e){throw new RangeError(p[e])}function d(e,t){for(var a=e.length,n=[];a--;)n[a]=t(e[a]);return n}function g(e,t){var a=e.split("@"),n="";return a.length>1&&(n=a[0]+"@",e=a[1]),n+d((e=e.replace(l,".")).split("."),t).join(".")}function b(e){for(var t,a,n=[],o=0,i=e.length;o<i;)(t=e.charCodeAt(o++))>=55296&&t<=56319&&o<i?56320==(64512&(a=e.charCodeAt(o++)))?n.push(((1023&t)<<10)+(1023&a)+65536):(n.push(t),o--):n.push(t);return n}function k(e){return d(e,(function(e){var t="";return e>65535&&(t+=f((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=f(e)})).join("")}function v(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function y(e,t,a){var n=0;for(e=a?m(e/700):e>>1,e+=m(e/t);e>455;n+=36)e=m(e/35);return m(n+36*e/(e+38))}function j(e){var t,a,n,o,i,r,u,c,l,p,f,d=[],g=e.length,b=0,v=128,j=72;for((a=e.lastIndexOf("-"))<0&&(a=0),n=0;n<a;++n)e.charCodeAt(n)>=128&&h("not-basic"),d.push(e.charCodeAt(n));for(o=a>0?a+1:0;o<g;){for(i=b,r=1,u=36;o>=g&&h("invalid-input"),((c=(f=e.charCodeAt(o++))-48<10?f-22:f-65<26?f-65:f-97<26?f-97:36)>=36||c>m((s-b)/r))&&h("overflow"),b+=c*r,!(c<(l=u<=j?1:u>=j+26?26:u-j));u+=36)r>m(s/(p=36-l))&&h("overflow"),r*=p;j=y(b-i,t=d.length+1,0==i),m(b/t)>s-v&&h("overflow"),v+=m(b/t),b%=t,d.splice(b++,0,v)}return k(d)}function w(e){var t,a,n,o,i,r,u,c,l,p,d,g,k,j,w,_=[];for(g=(e=b(e)).length,t=128,a=0,i=72,r=0;r<g;++r)(d=e[r])<128&&_.push(f(d));for(n=o=_.length,o&&_.push("-");n<g;){for(u=s,r=0;r<g;++r)(d=e[r])>=t&&d<u&&(u=d);for(u-t>m((s-a)/(k=n+1))&&h("overflow"),a+=(u-t)*k,t=u,r=0;r<g;++r)if((d=e[r])<t&&++a>s&&h("overflow"),d==t){for(c=a,l=36;!(c<(p=l<=i?1:l>=i+26?26:l-i));l+=36)w=c-p,j=36-p,_.push(f(v(p+w%j,0))),c=m(w/j);_.push(f(v(c,0))),i=y(a,k,n==o),a=0,++n}++a,++t}return _.join("")}r={version:"1.4.1",ucs2:{decode:b,encode:k},decode:j,encode:w,toASCII:function(e){return g(e,(function(e){return c.test(e)?"xn--"+w(e):e}))},toUnicode:function(e){return g(e,(function(e){return u.test(e)?j(e.slice(4).toLowerCase()):e}))}},void 0===(n=function(){return r}.call(t,a,t,e))||(e.exports=n)}()}).call(this,a(8)(e))},12:function(e){e.exports=JSON.parse('["ac","com.ac","edu.ac","gov.ac","net.ac","mil.ac","org.ac","ad","nom.ad","ae","co.ae","net.ae","org.ae","sch.ae","ac.ae","gov.ae","mil.ae","aero","accident-investigation.aero","accident-prevention.aero","aerobatic.aero","aeroclub.aero","aerodrome.aero","agents.aero","aircraft.aero","airline.aero","airport.aero","air-surveillance.aero","airtraffic.aero","air-traffic-control.aero","ambulance.aero","amusement.aero","association.aero","author.aero","ballooning.aero","broker.aero","caa.aero","cargo.aero","catering.aero","certification.aero","championship.aero","charter.aero","civilaviation.aero","club.aero","conference.aero","consultant.aero","consulting.aero","control.aero","council.aero","crew.aero","design.aero","dgca.aero","educator.aero","emergency.aero","engine.aero","engineer.aero","entertainment.aero","equipment.aero","exchange.aero","express.aero","federation.aero","flight.aero","freight.aero","fuel.aero","gliding.aero","government.aero","groundhandling.aero","group.aero","hanggliding.aero","homebuilt.aero","insurance.aero","journal.aero","journalist.aero","leasing.aero","logistics.aero","magazine.aero","maintenance.aero","media.aero","microlight.aero","modelling.aero","navigation.aero","parachuting.aero","paragliding.aero","passenger-association.aero","pilot.aero","press.aero","production.aero","recreation.aero","repbody.aero","res.aero","research.aero","rotorcraft.aero","safety.aero","scientist.aero","services.aero","show.aero","skydiving.aero","software.aero","student.aero","trader.aero","trading.aero","trainer.aero","union.aero","workinggroup.aero","works.aero","af","gov.af","com.af","org.af","net.af","edu.af","ag","com.ag","org.ag","net.ag","co.ag","nom.ag","ai","off.ai","com.ai","net.ai","org.ai","al","com.al","edu.al","gov.al","mil.al","net.al","org.al","am","co.am","com.am","commune.am","net.am","org.am","ao","ed.ao","gv.ao","og.ao","co.ao","pb.ao","it.ao","aq","ar","com.ar","edu.ar","gob.ar","gov.ar","int.ar","mil.ar","musica.ar","net.ar","org.ar","tur.ar","arpa","e164.arpa","in-addr.arpa","ip6.arpa","iris.arpa","uri.arpa","urn.arpa","as","gov.as","asia","at","ac.at","co.at","gv.at","or.at","au","com.au","net.au","org.au","edu.au","gov.au","asn.au","id.au","info.au","conf.au","oz.au","act.au","nsw.au","nt.au","qld.au","sa.au","tas.au","vic.au","wa.au","act.edu.au","catholic.edu.au","nsw.edu.au","nt.edu.au","qld.edu.au","sa.edu.au","tas.edu.au","vic.edu.au","wa.edu.au","qld.gov.au","sa.gov.au","tas.gov.au","vic.gov.au","wa.gov.au","education.tas.edu.au","schools.nsw.edu.au","aw","com.aw","ax","az","com.az","net.az","int.az","gov.az","org.az","edu.az","info.az","pp.az","mil.az","name.az","pro.az","biz.az","ba","com.ba","edu.ba","gov.ba","mil.ba","net.ba","org.ba","bb","biz.bb","co.bb","com.bb","edu.bb","gov.bb","info.bb","net.bb","org.bb","store.bb","tv.bb","*.bd","be","ac.be","bf","gov.bf","bg","a.bg","b.bg","c.bg","d.bg","e.bg","f.bg","g.bg","h.bg","i.bg","j.bg","k.bg","l.bg","m.bg","n.bg","o.bg","p.bg","q.bg","r.bg","s.bg","t.bg","u.bg","v.bg","w.bg","x.bg","y.bg","z.bg","0.bg","1.bg","2.bg","3.bg","4.bg","5.bg","6.bg","7.bg","8.bg","9.bg","bh","com.bh","edu.bh","net.bh","org.bh","gov.bh","bi","co.bi","com.bi","edu.bi","or.bi","org.bi","biz","bj","asso.bj","barreau.bj","gouv.bj","bm","com.bm","edu.bm","gov.bm","net.bm","org.bm","bn","com.bn","edu.bn","gov.bn","net.bn","org.bn","bo","com.bo","edu.bo","gob.bo","int.bo","org.bo","net.bo","mil.bo","tv.bo","web.bo","academia.bo","agro.bo","arte.bo","blog.bo","bolivia.bo","ciencia.bo","cooperativa.bo","democracia.bo","deporte.bo","ecologia.bo","economia.bo","empresa.bo","indigena.bo","industria.bo","info.bo","medicina.bo","movimiento.bo","musica.bo","natural.bo","nombre.bo","noticias.bo","patria.bo","politica.bo","profesional.bo","plurinacional.bo","pueblo.bo","revista.bo","salud.bo","tecnologia.bo","tksat.bo","transporte.bo","wiki.bo","br","9guacu.br","abc.br","adm.br","adv.br","agr.br","aju.br","am.br","anani.br","aparecida.br","arq.br","art.br","ato.br","b.br","barueri.br","belem.br","bhz.br","bio.br","blog.br","bmd.br","boavista.br","bsb.br","campinagrande.br","campinas.br","caxias.br","cim.br","cng.br","cnt.br","com.br","contagem.br","coop.br","cri.br","cuiaba.br","curitiba.br","def.br","ecn.br","eco.br","edu.br","emp.br","eng.br","esp.br","etc.br","eti.br","far.br","feira.br","flog.br","floripa.br","fm.br","fnd.br","fortal.br","fot.br","foz.br","fst.br","g12.br","ggf.br","goiania.br","gov.br","ac.gov.br","al.gov.br","am.gov.br","ap.gov.br","ba.gov.br","ce.gov.br","df.gov.br","es.gov.br","go.gov.br","ma.gov.br","mg.gov.br","ms.gov.br","mt.gov.br","pa.gov.br","pb.gov.br","pe.gov.br","pi.gov.br","pr.gov.br","rj.gov.br","rn.gov.br","ro.gov.br","rr.gov.br","rs.gov.br","sc.gov.br","se.gov.br","sp.gov.br","to.gov.br","gru.br","imb.br","ind.br","inf.br","jab.br","jampa.br","jdf.br","joinville.br","jor.br","jus.br","leg.br","lel.br","londrina.br","macapa.br","maceio.br","manaus.br","maringa.br","mat.br","med.br","mil.br","morena.br","mp.br","mus.br","natal.br","net.br","niteroi.br","*.nom.br","not.br","ntr.br","odo.br","ong.br","org.br","osasco.br","palmas.br","poa.br","ppg.br","pro.br","psc.br","psi.br","pvh.br","qsl.br","radio.br","rec.br","recife.br","ribeirao.br","rio.br","riobranco.br","riopreto.br","salvador.br","sampa.br","santamaria.br","santoandre.br","saobernardo.br","saogonca.br","sjc.br","slg.br","slz.br","sorocaba.br","srv.br","taxi.br","tc.br","teo.br","the.br","tmp.br","trd.br","tur.br","tv.br","udi.br","vet.br","vix.br","vlog.br","wiki.br","zlg.br","bs","com.bs","net.bs","org.bs","edu.bs","gov.bs","bt","com.bt","edu.bt","gov.bt","net.bt","org.bt","bv","bw","co.bw","org.bw","by","gov.by","mil.by","com.by","of.by","bz","com.bz","net.bz","org.bz","edu.bz","gov.bz","ca","ab.ca","bc.ca","mb.ca","nb.ca","nf.ca","nl.ca","ns.ca","nt.ca","nu.ca","on.ca","pe.ca","qc.ca","sk.ca","yk.ca","gc.ca","cat","cc","cd","gov.cd","cf","cg","ch","ci","org.ci","or.ci","com.ci","co.ci","edu.ci","ed.ci","ac.ci","net.ci","go.ci","asso.ci","aéroport.ci","int.ci","presse.ci","md.ci","gouv.ci","*.ck","!www.ck","cl","aprendemas.cl","co.cl","gob.cl","gov.cl","mil.cl","cm","co.cm","com.cm","gov.cm","net.cm","cn","ac.cn","com.cn","edu.cn","gov.cn","net.cn","org.cn","mil.cn","公司.cn","网络.cn","網絡.cn","ah.cn","bj.cn","cq.cn","fj.cn","gd.cn","gs.cn","gz.cn","gx.cn","ha.cn","hb.cn","he.cn","hi.cn","hl.cn","hn.cn","jl.cn","js.cn","jx.cn","ln.cn","nm.cn","nx.cn","qh.cn","sc.cn","sd.cn","sh.cn","sn.cn","sx.cn","tj.cn","xj.cn","xz.cn","yn.cn","zj.cn","hk.cn","mo.cn","tw.cn","co","arts.co","com.co","edu.co","firm.co","gov.co","info.co","int.co","mil.co","net.co","nom.co","org.co","rec.co","web.co","com","coop","cr","ac.cr","co.cr","ed.cr","fi.cr","go.cr","or.cr","sa.cr","cu","com.cu","edu.cu","org.cu","net.cu","gov.cu","inf.cu","cv","cw","com.cw","edu.cw","net.cw","org.cw","cx","gov.cx","cy","ac.cy","biz.cy","com.cy","ekloges.cy","gov.cy","ltd.cy","name.cy","net.cy","org.cy","parliament.cy","press.cy","pro.cy","tm.cy","cz","de","dj","dk","dm","com.dm","net.dm","org.dm","edu.dm","gov.dm","do","art.do","com.do","edu.do","gob.do","gov.do","mil.do","net.do","org.do","sld.do","web.do","dz","com.dz","org.dz","net.dz","gov.dz","edu.dz","asso.dz","pol.dz","art.dz","ec","com.ec","info.ec","net.ec","fin.ec","k12.ec","med.ec","pro.ec","org.ec","edu.ec","gov.ec","gob.ec","mil.ec","edu","ee","edu.ee","gov.ee","riik.ee","lib.ee","med.ee","com.ee","pri.ee","aip.ee","org.ee","fie.ee","eg","com.eg","edu.eg","eun.eg","gov.eg","mil.eg","name.eg","net.eg","org.eg","sci.eg","*.er","es","com.es","nom.es","org.es","gob.es","edu.es","et","com.et","gov.et","org.et","edu.et","biz.et","name.et","info.et","net.et","eu","fi","aland.fi","fj","ac.fj","biz.fj","com.fj","gov.fj","info.fj","mil.fj","name.fj","net.fj","org.fj","pro.fj","*.fk","fm","fo","fr","asso.fr","com.fr","gouv.fr","nom.fr","prd.fr","tm.fr","aeroport.fr","avocat.fr","avoues.fr","cci.fr","chambagri.fr","chirurgiens-dentistes.fr","experts-comptables.fr","geometre-expert.fr","greta.fr","huissier-justice.fr","medecin.fr","notaires.fr","pharmacien.fr","port.fr","veterinaire.fr","ga","gb","gd","ge","com.ge","edu.ge","gov.ge","org.ge","mil.ge","net.ge","pvt.ge","gf","gg","co.gg","net.gg","org.gg","gh","com.gh","edu.gh","gov.gh","org.gh","mil.gh","gi","com.gi","ltd.gi","gov.gi","mod.gi","edu.gi","org.gi","gl","co.gl","com.gl","edu.gl","net.gl","org.gl","gm","gn","ac.gn","com.gn","edu.gn","gov.gn","org.gn","net.gn","gov","gp","com.gp","net.gp","mobi.gp","edu.gp","org.gp","asso.gp","gq","gr","com.gr","edu.gr","net.gr","org.gr","gov.gr","gs","gt","com.gt","edu.gt","gob.gt","ind.gt","mil.gt","net.gt","org.gt","gu","com.gu","edu.gu","gov.gu","guam.gu","info.gu","net.gu","org.gu","web.gu","gw","gy","co.gy","com.gy","edu.gy","gov.gy","net.gy","org.gy","hk","com.hk","edu.hk","gov.hk","idv.hk","net.hk","org.hk","公司.hk","教育.hk","敎育.hk","政府.hk","個人.hk","个人.hk","箇人.hk","網络.hk","网络.hk","组織.hk","網絡.hk","网絡.hk","组织.hk","組織.hk","組织.hk","hm","hn","com.hn","edu.hn","org.hn","net.hn","mil.hn","gob.hn","hr","iz.hr","from.hr","name.hr","com.hr","ht","com.ht","shop.ht","firm.ht","info.ht","adult.ht","net.ht","pro.ht","org.ht","med.ht","art.ht","coop.ht","pol.ht","asso.ht","edu.ht","rel.ht","gouv.ht","perso.ht","hu","co.hu","info.hu","org.hu","priv.hu","sport.hu","tm.hu","2000.hu","agrar.hu","bolt.hu","casino.hu","city.hu","erotica.hu","erotika.hu","film.hu","forum.hu","games.hu","hotel.hu","ingatlan.hu","jogasz.hu","konyvelo.hu","lakas.hu","media.hu","news.hu","reklam.hu","sex.hu","shop.hu","suli.hu","szex.hu","tozsde.hu","utazas.hu","video.hu","id","ac.id","biz.id","co.id","desa.id","go.id","mil.id","my.id","net.id","or.id","ponpes.id","sch.id","web.id","ie","gov.ie","il","ac.il","co.il","gov.il","idf.il","k12.il","muni.il","net.il","org.il","im","ac.im","co.im","com.im","ltd.co.im","net.im","org.im","plc.co.im","tt.im","tv.im","in","co.in","firm.in","net.in","org.in","gen.in","ind.in","nic.in","ac.in","edu.in","res.in","gov.in","mil.in","info","int","eu.int","io","com.io","iq","gov.iq","edu.iq","mil.iq","com.iq","org.iq","net.iq","ir","ac.ir","co.ir","gov.ir","id.ir","net.ir","org.ir","sch.ir","ایران.ir","ايران.ir","is","net.is","com.is","edu.is","gov.is","org.is","int.is","it","gov.it","edu.it","abr.it","abruzzo.it","aosta-valley.it","aostavalley.it","bas.it","basilicata.it","cal.it","calabria.it","cam.it","campania.it","emilia-romagna.it","emiliaromagna.it","emr.it","friuli-v-giulia.it","friuli-ve-giulia.it","friuli-vegiulia.it","friuli-venezia-giulia.it","friuli-veneziagiulia.it","friuli-vgiulia.it","friuliv-giulia.it","friulive-giulia.it","friulivegiulia.it","friulivenezia-giulia.it","friuliveneziagiulia.it","friulivgiulia.it","fvg.it","laz.it","lazio.it","lig.it","liguria.it","lom.it","lombardia.it","lombardy.it","lucania.it","mar.it","marche.it","mol.it","molise.it","piedmont.it","piemonte.it","pmn.it","pug.it","puglia.it","sar.it","sardegna.it","sardinia.it","sic.it","sicilia.it","sicily.it","taa.it","tos.it","toscana.it","trentin-sud-tirol.it","trentin-süd-tirol.it","trentin-sudtirol.it","trentin-südtirol.it","trentin-sued-tirol.it","trentin-suedtirol.it","trentino-a-adige.it","trentino-aadige.it","trentino-alto-adige.it","trentino-altoadige.it","trentino-s-tirol.it","trentino-stirol.it","trentino-sud-tirol.it","trentino-süd-tirol.it","trentino-sudtirol.it","trentino-südtirol.it","trentino-sued-tirol.it","trentino-suedtirol.it","trentino.it","trentinoa-adige.it","trentinoaadige.it","trentinoalto-adige.it","trentinoaltoadige.it","trentinos-tirol.it","trentinostirol.it","trentinosud-tirol.it","trentinosüd-tirol.it","trentinosudtirol.it","trentinosüdtirol.it","trentinosued-tirol.it","trentinosuedtirol.it","trentinsud-tirol.it","trentinsüd-tirol.it","trentinsudtirol.it","trentinsüdtirol.it","trentinsued-tirol.it","trentinsuedtirol.it","tuscany.it","umb.it","umbria.it","val-d-aosta.it","val-daosta.it","vald-aosta.it","valdaosta.it","valle-aosta.it","valle-d-aosta.it","valle-daosta.it","valleaosta.it","valled-aosta.it","valledaosta.it","vallee-aoste.it","vallée-aoste.it","vallee-d-aoste.it","vallée-d-aoste.it","valleeaoste.it","valléeaoste.it","valleedaoste.it","valléedaoste.it","vao.it","vda.it","ven.it","veneto.it","ag.it","agrigento.it","al.it","alessandria.it","alto-adige.it","altoadige.it","an.it","ancona.it","andria-barletta-trani.it","andria-trani-barletta.it","andriabarlettatrani.it","andriatranibarletta.it","ao.it","aosta.it","aoste.it","ap.it","aq.it","aquila.it","ar.it","arezzo.it","ascoli-piceno.it","ascolipiceno.it","asti.it","at.it","av.it","avellino.it","ba.it","balsan-sudtirol.it","balsan-südtirol.it","balsan-suedtirol.it","balsan.it","bari.it","barletta-trani-andria.it","barlettatraniandria.it","belluno.it","benevento.it","bergamo.it","bg.it","bi.it","biella.it","bl.it","bn.it","bo.it","bologna.it","bolzano-altoadige.it","bolzano.it","bozen-sudtirol.it","bozen-südtirol.it","bozen-suedtirol.it","bozen.it","br.it","brescia.it","brindisi.it","bs.it","bt.it","bulsan-sudtirol.it","bulsan-südtirol.it","bulsan-suedtirol.it","bulsan.it","bz.it","ca.it","cagliari.it","caltanissetta.it","campidano-medio.it","campidanomedio.it","campobasso.it","carbonia-iglesias.it","carboniaiglesias.it","carrara-massa.it","carraramassa.it","caserta.it","catania.it","catanzaro.it","cb.it","ce.it","cesena-forli.it","cesena-forlì.it","cesenaforli.it","cesenaforlì.it","ch.it","chieti.it","ci.it","cl.it","cn.it","co.it","como.it","cosenza.it","cr.it","cremona.it","crotone.it","cs.it","ct.it","cuneo.it","cz.it","dell-ogliastra.it","dellogliastra.it","en.it","enna.it","fc.it","fe.it","fermo.it","ferrara.it","fg.it","fi.it","firenze.it","florence.it","fm.it","foggia.it","forli-cesena.it","forlì-cesena.it","forlicesena.it","forlìcesena.it","fr.it","frosinone.it","ge.it","genoa.it","genova.it","go.it","gorizia.it","gr.it","grosseto.it","iglesias-carbonia.it","iglesiascarbonia.it","im.it","imperia.it","is.it","isernia.it","kr.it","la-spezia.it","laquila.it","laspezia.it","latina.it","lc.it","le.it","lecce.it","lecco.it","li.it","livorno.it","lo.it","lodi.it","lt.it","lu.it","lucca.it","macerata.it","mantova.it","massa-carrara.it","massacarrara.it","matera.it","mb.it","mc.it","me.it","medio-campidano.it","mediocampidano.it","messina.it","mi.it","milan.it","milano.it","mn.it","mo.it","modena.it","monza-brianza.it","monza-e-della-brianza.it","monza.it","monzabrianza.it","monzaebrianza.it","monzaedellabrianza.it","ms.it","mt.it","na.it","naples.it","napoli.it","no.it","novara.it","nu.it","nuoro.it","og.it","ogliastra.it","olbia-tempio.it","olbiatempio.it","or.it","oristano.it","ot.it","pa.it","padova.it","padua.it","palermo.it","parma.it","pavia.it","pc.it","pd.it","pe.it","perugia.it","pesaro-urbino.it","pesarourbino.it","pescara.it","pg.it","pi.it","piacenza.it","pisa.it","pistoia.it","pn.it","po.it","pordenone.it","potenza.it","pr.it","prato.it","pt.it","pu.it","pv.it","pz.it","ra.it","ragusa.it","ravenna.it","rc.it","re.it","reggio-calabria.it","reggio-emilia.it","reggiocalabria.it","reggioemilia.it","rg.it","ri.it","rieti.it","rimini.it","rm.it","rn.it","ro.it","roma.it","rome.it","rovigo.it","sa.it","salerno.it","sassari.it","savona.it","si.it","siena.it","siracusa.it","so.it","sondrio.it","sp.it","sr.it","ss.it","suedtirol.it","südtirol.it","sv.it","ta.it","taranto.it","te.it","tempio-olbia.it","tempioolbia.it","teramo.it","terni.it","tn.it","to.it","torino.it","tp.it","tr.it","trani-andria-barletta.it","trani-barletta-andria.it","traniandriabarletta.it","tranibarlettaandria.it","trapani.it","trento.it","treviso.it","trieste.it","ts.it","turin.it","tv.it","ud.it","udine.it","urbino-pesaro.it","urbinopesaro.it","va.it","varese.it","vb.it","vc.it","ve.it","venezia.it","venice.it","verbania.it","vercelli.it","verona.it","vi.it","vibo-valentia.it","vibovalentia.it","vicenza.it","viterbo.it","vr.it","vs.it","vt.it","vv.it","je","co.je","net.je","org.je","*.jm","jo","com.jo","org.jo","net.jo","edu.jo","sch.jo","gov.jo","mil.jo","name.jo","jobs","jp","ac.jp","ad.jp","co.jp","ed.jp","go.jp","gr.jp","lg.jp","ne.jp","or.jp","aichi.jp","akita.jp","aomori.jp","chiba.jp","ehime.jp","fukui.jp","fukuoka.jp","fukushima.jp","gifu.jp","gunma.jp","hiroshima.jp","hokkaido.jp","hyogo.jp","ibaraki.jp","ishikawa.jp","iwate.jp","kagawa.jp","kagoshima.jp","kanagawa.jp","kochi.jp","kumamoto.jp","kyoto.jp","mie.jp","miyagi.jp","miyazaki.jp","nagano.jp","nagasaki.jp","nara.jp","niigata.jp","oita.jp","okayama.jp","okinawa.jp","osaka.jp","saga.jp","saitama.jp","shiga.jp","shimane.jp","shizuoka.jp","tochigi.jp","tokushima.jp","tokyo.jp","tottori.jp","toyama.jp","wakayama.jp","yamagata.jp","yamaguchi.jp","yamanashi.jp","栃木.jp","愛知.jp","愛媛.jp","兵庫.jp","熊本.jp","茨城.jp","北海道.jp","千葉.jp","和歌山.jp","長崎.jp","長野.jp","新潟.jp","青森.jp","静岡.jp","東京.jp","石川.jp","埼玉.jp","三重.jp","京都.jp","佐賀.jp","大分.jp","大阪.jp","奈良.jp","宮城.jp","宮崎.jp","富山.jp","山口.jp","山形.jp","山梨.jp","岩手.jp","岐阜.jp","岡山.jp","島根.jp","広島.jp","徳島.jp","沖縄.jp","滋賀.jp","神奈川.jp","福井.jp","福岡.jp","福島.jp","秋田.jp","群馬.jp","香川.jp","高知.jp","鳥取.jp","鹿児島.jp","*.kawasaki.jp","*.kitakyushu.jp","*.kobe.jp","*.nagoya.jp","*.sapporo.jp","*.sendai.jp","*.yokohama.jp","!city.kawasaki.jp","!city.kitakyushu.jp","!city.kobe.jp","!city.nagoya.jp","!city.sapporo.jp","!city.sendai.jp","!city.yokohama.jp","aisai.aichi.jp","ama.aichi.jp","anjo.aichi.jp","asuke.aichi.jp","chiryu.aichi.jp","chita.aichi.jp","fuso.aichi.jp","gamagori.aichi.jp","handa.aichi.jp","hazu.aichi.jp","hekinan.aichi.jp","higashiura.aichi.jp","ichinomiya.aichi.jp","inazawa.aichi.jp","inuyama.aichi.jp","isshiki.aichi.jp","iwakura.aichi.jp","kanie.aichi.jp","kariya.aichi.jp","kasugai.aichi.jp","kira.aichi.jp","kiyosu.aichi.jp","komaki.aichi.jp","konan.aichi.jp","kota.aichi.jp","mihama.aichi.jp","miyoshi.aichi.jp","nishio.aichi.jp","nisshin.aichi.jp","obu.aichi.jp","oguchi.aichi.jp","oharu.aichi.jp","okazaki.aichi.jp","owariasahi.aichi.jp","seto.aichi.jp","shikatsu.aichi.jp","shinshiro.aichi.jp","shitara.aichi.jp","tahara.aichi.jp","takahama.aichi.jp","tobishima.aichi.jp","toei.aichi.jp","togo.aichi.jp","tokai.aichi.jp","tokoname.aichi.jp","toyoake.aichi.jp","toyohashi.aichi.jp","toyokawa.aichi.jp","toyone.aichi.jp","toyota.aichi.jp","tsushima.aichi.jp","yatomi.aichi.jp","akita.akita.jp","daisen.akita.jp","fujisato.akita.jp","gojome.akita.jp","hachirogata.akita.jp","happou.akita.jp","higashinaruse.akita.jp","honjo.akita.jp","honjyo.akita.jp","ikawa.akita.jp","kamikoani.akita.jp","kamioka.akita.jp","katagami.akita.jp","kazuno.akita.jp","kitaakita.akita.jp","kosaka.akita.jp","kyowa.akita.jp","misato.akita.jp","mitane.akita.jp","moriyoshi.akita.jp","nikaho.akita.jp","noshiro.akita.jp","odate.akita.jp","oga.akita.jp","ogata.akita.jp","semboku.akita.jp","yokote.akita.jp","yurihonjo.akita.jp","aomori.aomori.jp","gonohe.aomori.jp","hachinohe.aomori.jp","hashikami.aomori.jp","hiranai.aomori.jp","hirosaki.aomori.jp","itayanagi.aomori.jp","kuroishi.aomori.jp","misawa.aomori.jp","mutsu.aomori.jp","nakadomari.aomori.jp","noheji.aomori.jp","oirase.aomori.jp","owani.aomori.jp","rokunohe.aomori.jp","sannohe.aomori.jp","shichinohe.aomori.jp","shingo.aomori.jp","takko.aomori.jp","towada.aomori.jp","tsugaru.aomori.jp","tsuruta.aomori.jp","abiko.chiba.jp","asahi.chiba.jp","chonan.chiba.jp","chosei.chiba.jp","choshi.chiba.jp","chuo.chiba.jp","funabashi.chiba.jp","futtsu.chiba.jp","hanamigawa.chiba.jp","ichihara.chiba.jp","ichikawa.chiba.jp","ichinomiya.chiba.jp","inzai.chiba.jp","isumi.chiba.jp","kamagaya.chiba.jp","kamogawa.chiba.jp","kashiwa.chiba.jp","katori.chiba.jp","katsuura.chiba.jp","kimitsu.chiba.jp","kisarazu.chiba.jp","kozaki.chiba.jp","kujukuri.chiba.jp","kyonan.chiba.jp","matsudo.chiba.jp","midori.chiba.jp","mihama.chiba.jp","minamiboso.chiba.jp","mobara.chiba.jp","mutsuzawa.chiba.jp","nagara.chiba.jp","nagareyama.chiba.jp","narashino.chiba.jp","narita.chiba.jp","noda.chiba.jp","oamishirasato.chiba.jp","omigawa.chiba.jp","onjuku.chiba.jp","otaki.chiba.jp","sakae.chiba.jp","sakura.chiba.jp","shimofusa.chiba.jp","shirako.chiba.jp","shiroi.chiba.jp","shisui.chiba.jp","sodegaura.chiba.jp","sosa.chiba.jp","tako.chiba.jp","tateyama.chiba.jp","togane.chiba.jp","tohnosho.chiba.jp","tomisato.chiba.jp","urayasu.chiba.jp","yachimata.chiba.jp","yachiyo.chiba.jp","yokaichiba.chiba.jp","yokoshibahikari.chiba.jp","yotsukaido.chiba.jp","ainan.ehime.jp","honai.ehime.jp","ikata.ehime.jp","imabari.ehime.jp","iyo.ehime.jp","kamijima.ehime.jp","kihoku.ehime.jp","kumakogen.ehime.jp","masaki.ehime.jp","matsuno.ehime.jp","matsuyama.ehime.jp","namikata.ehime.jp","niihama.ehime.jp","ozu.ehime.jp","saijo.ehime.jp","seiyo.ehime.jp","shikokuchuo.ehime.jp","tobe.ehime.jp","toon.ehime.jp","uchiko.ehime.jp","uwajima.ehime.jp","yawatahama.ehime.jp","echizen.fukui.jp","eiheiji.fukui.jp","fukui.fukui.jp","ikeda.fukui.jp","katsuyama.fukui.jp","mihama.fukui.jp","minamiechizen.fukui.jp","obama.fukui.jp","ohi.fukui.jp","ono.fukui.jp","sabae.fukui.jp","sakai.fukui.jp","takahama.fukui.jp","tsuruga.fukui.jp","wakasa.fukui.jp","ashiya.fukuoka.jp","buzen.fukuoka.jp","chikugo.fukuoka.jp","chikuho.fukuoka.jp","chikujo.fukuoka.jp","chikushino.fukuoka.jp","chikuzen.fukuoka.jp","chuo.fukuoka.jp","dazaifu.fukuoka.jp","fukuchi.fukuoka.jp","hakata.fukuoka.jp","higashi.fukuoka.jp","hirokawa.fukuoka.jp","hisayama.fukuoka.jp","iizuka.fukuoka.jp","inatsuki.fukuoka.jp","kaho.fukuoka.jp","kasuga.fukuoka.jp","kasuya.fukuoka.jp","kawara.fukuoka.jp","keisen.fukuoka.jp","koga.fukuoka.jp","kurate.fukuoka.jp","kurogi.fukuoka.jp","kurume.fukuoka.jp","minami.fukuoka.jp","miyako.fukuoka.jp","miyama.fukuoka.jp","miyawaka.fukuoka.jp","mizumaki.fukuoka.jp","munakata.fukuoka.jp","nakagawa.fukuoka.jp","nakama.fukuoka.jp","nishi.fukuoka.jp","nogata.fukuoka.jp","ogori.fukuoka.jp","okagaki.fukuoka.jp","okawa.fukuoka.jp","oki.fukuoka.jp","omuta.fukuoka.jp","onga.fukuoka.jp","onojo.fukuoka.jp","oto.fukuoka.jp","saigawa.fukuoka.jp","sasaguri.fukuoka.jp","shingu.fukuoka.jp","shinyoshitomi.fukuoka.jp","shonai.fukuoka.jp","soeda.fukuoka.jp","sue.fukuoka.jp","tachiarai.fukuoka.jp","tagawa.fukuoka.jp","takata.fukuoka.jp","toho.fukuoka.jp","toyotsu.fukuoka.jp","tsuiki.fukuoka.jp","ukiha.fukuoka.jp","umi.fukuoka.jp","usui.fukuoka.jp","yamada.fukuoka.jp","yame.fukuoka.jp","yanagawa.fukuoka.jp","yukuhashi.fukuoka.jp","aizubange.fukushima.jp","aizumisato.fukushima.jp","aizuwakamatsu.fukushima.jp","asakawa.fukushima.jp","bandai.fukushima.jp","date.fukushima.jp","fukushima.fukushima.jp","furudono.fukushima.jp","futaba.fukushima.jp","hanawa.fukushima.jp","higashi.fukushima.jp","hirata.fukushima.jp","hirono.fukushima.jp","iitate.fukushima.jp","inawashiro.fukushima.jp","ishikawa.fukushima.jp","iwaki.fukushima.jp","izumizaki.fukushima.jp","kagamiishi.fukushima.jp","kaneyama.fukushima.jp","kawamata.fukushima.jp","kitakata.fukushima.jp","kitashiobara.fukushima.jp","koori.fukushima.jp","koriyama.fukushima.jp","kunimi.fukushima.jp","miharu.fukushima.jp","mishima.fukushima.jp","namie.fukushima.jp","nango.fukushima.jp","nishiaizu.fukushima.jp","nishigo.fukushima.jp","okuma.fukushima.jp","omotego.fukushima.jp","ono.fukushima.jp","otama.fukushima.jp","samegawa.fukushima.jp","shimogo.fukushima.jp","shirakawa.fukushima.jp","showa.fukushima.jp","soma.fukushima.jp","sukagawa.fukushima.jp","taishin.fukushima.jp","tamakawa.fukushima.jp","tanagura.fukushima.jp","tenei.fukushima.jp","yabuki.fukushima.jp","yamato.fukushima.jp","yamatsuri.fukushima.jp","yanaizu.fukushima.jp","yugawa.fukushima.jp","anpachi.gifu.jp","ena.gifu.jp","gifu.gifu.jp","ginan.gifu.jp","godo.gifu.jp","gujo.gifu.jp","hashima.gifu.jp","hichiso.gifu.jp","hida.gifu.jp","higashishirakawa.gifu.jp","ibigawa.gifu.jp","ikeda.gifu.jp","kakamigahara.gifu.jp","kani.gifu.jp","kasahara.gifu.jp","kasamatsu.gifu.jp","kawaue.gifu.jp","kitagata.gifu.jp","mino.gifu.jp","minokamo.gifu.jp","mitake.gifu.jp","mizunami.gifu.jp","motosu.gifu.jp","nakatsugawa.gifu.jp","ogaki.gifu.jp","sakahogi.gifu.jp","seki.gifu.jp","sekigahara.gifu.jp","shirakawa.gifu.jp","tajimi.gifu.jp","takayama.gifu.jp","tarui.gifu.jp","toki.gifu.jp","tomika.gifu.jp","wanouchi.gifu.jp","yamagata.gifu.jp","yaotsu.gifu.jp","yoro.gifu.jp","annaka.gunma.jp","chiyoda.gunma.jp","fujioka.gunma.jp","higashiagatsuma.gunma.jp","isesaki.gunma.jp","itakura.gunma.jp","kanna.gunma.jp","kanra.gunma.jp","katashina.gunma.jp","kawaba.gunma.jp","kiryu.gunma.jp","kusatsu.gunma.jp","maebashi.gunma.jp","meiwa.gunma.jp","midori.gunma.jp","minakami.gunma.jp","naganohara.gunma.jp","nakanojo.gunma.jp","nanmoku.gunma.jp","numata.gunma.jp","oizumi.gunma.jp","ora.gunma.jp","ota.gunma.jp","shibukawa.gunma.jp","shimonita.gunma.jp","shinto.gunma.jp","showa.gunma.jp","takasaki.gunma.jp","takayama.gunma.jp","tamamura.gunma.jp","tatebayashi.gunma.jp","tomioka.gunma.jp","tsukiyono.gunma.jp","tsumagoi.gunma.jp","ueno.gunma.jp","yoshioka.gunma.jp","asaminami.hiroshima.jp","daiwa.hiroshima.jp","etajima.hiroshima.jp","fuchu.hiroshima.jp","fukuyama.hiroshima.jp","hatsukaichi.hiroshima.jp","higashihiroshima.hiroshima.jp","hongo.hiroshima.jp","jinsekikogen.hiroshima.jp","kaita.hiroshima.jp","kui.hiroshima.jp","kumano.hiroshima.jp","kure.hiroshima.jp","mihara.hiroshima.jp","miyoshi.hiroshima.jp","naka.hiroshima.jp","onomichi.hiroshima.jp","osakikamijima.hiroshima.jp","otake.hiroshima.jp","saka.hiroshima.jp","sera.hiroshima.jp","seranishi.hiroshima.jp","shinichi.hiroshima.jp","shobara.hiroshima.jp","takehara.hiroshima.jp","abashiri.hokkaido.jp","abira.hokkaido.jp","aibetsu.hokkaido.jp","akabira.hokkaido.jp","akkeshi.hokkaido.jp","asahikawa.hokkaido.jp","ashibetsu.hokkaido.jp","ashoro.hokkaido.jp","assabu.hokkaido.jp","atsuma.hokkaido.jp","bibai.hokkaido.jp","biei.hokkaido.jp","bifuka.hokkaido.jp","bihoro.hokkaido.jp","biratori.hokkaido.jp","chippubetsu.hokkaido.jp","chitose.hokkaido.jp","date.hokkaido.jp","ebetsu.hokkaido.jp","embetsu.hokkaido.jp","eniwa.hokkaido.jp","erimo.hokkaido.jp","esan.hokkaido.jp","esashi.hokkaido.jp","fukagawa.hokkaido.jp","fukushima.hokkaido.jp","furano.hokkaido.jp","furubira.hokkaido.jp","haboro.hokkaido.jp","hakodate.hokkaido.jp","hamatonbetsu.hokkaido.jp","hidaka.hokkaido.jp","higashikagura.hokkaido.jp","higashikawa.hokkaido.jp","hiroo.hokkaido.jp","hokuryu.hokkaido.jp","hokuto.hokkaido.jp","honbetsu.hokkaido.jp","horokanai.hokkaido.jp","horonobe.hokkaido.jp","ikeda.hokkaido.jp","imakane.hokkaido.jp","ishikari.hokkaido.jp","iwamizawa.hokkaido.jp","iwanai.hokkaido.jp","kamifurano.hokkaido.jp","kamikawa.hokkaido.jp","kamishihoro.hokkaido.jp","kamisunagawa.hokkaido.jp","kamoenai.hokkaido.jp","kayabe.hokkaido.jp","kembuchi.hokkaido.jp","kikonai.hokkaido.jp","kimobetsu.hokkaido.jp","kitahiroshima.hokkaido.jp","kitami.hokkaido.jp","kiyosato.hokkaido.jp","koshimizu.hokkaido.jp","kunneppu.hokkaido.jp","kuriyama.hokkaido.jp","kuromatsunai.hokkaido.jp","kushiro.hokkaido.jp","kutchan.hokkaido.jp","kyowa.hokkaido.jp","mashike.hokkaido.jp","matsumae.hokkaido.jp","mikasa.hokkaido.jp","minamifurano.hokkaido.jp","mombetsu.hokkaido.jp","moseushi.hokkaido.jp","mukawa.hokkaido.jp","muroran.hokkaido.jp","naie.hokkaido.jp","nakagawa.hokkaido.jp","nakasatsunai.hokkaido.jp","nakatombetsu.hokkaido.jp","nanae.hokkaido.jp","nanporo.hokkaido.jp","nayoro.hokkaido.jp","nemuro.hokkaido.jp","niikappu.hokkaido.jp","niki.hokkaido.jp","nishiokoppe.hokkaido.jp","noboribetsu.hokkaido.jp","numata.hokkaido.jp","obihiro.hokkaido.jp","obira.hokkaido.jp","oketo.hokkaido.jp","okoppe.hokkaido.jp","otaru.hokkaido.jp","otobe.hokkaido.jp","otofuke.hokkaido.jp","otoineppu.hokkaido.jp","oumu.hokkaido.jp","ozora.hokkaido.jp","pippu.hokkaido.jp","rankoshi.hokkaido.jp","rebun.hokkaido.jp","rikubetsu.hokkaido.jp","rishiri.hokkaido.jp","rishirifuji.hokkaido.jp","saroma.hokkaido.jp","sarufutsu.hokkaido.jp","shakotan.hokkaido.jp","shari.hokkaido.jp","shibecha.hokkaido.jp","shibetsu.hokkaido.jp","shikabe.hokkaido.jp","shikaoi.hokkaido.jp","shimamaki.hokkaido.jp","shimizu.hokkaido.jp","shimokawa.hokkaido.jp","shinshinotsu.hokkaido.jp","shintoku.hokkaido.jp","shiranuka.hokkaido.jp","shiraoi.hokkaido.jp","shiriuchi.hokkaido.jp","sobetsu.hokkaido.jp","sunagawa.hokkaido.jp","taiki.hokkaido.jp","takasu.hokkaido.jp","takikawa.hokkaido.jp","takinoue.hokkaido.jp","teshikaga.hokkaido.jp","tobetsu.hokkaido.jp","tohma.hokkaido.jp","tomakomai.hokkaido.jp","tomari.hokkaido.jp","toya.hokkaido.jp","toyako.hokkaido.jp","toyotomi.hokkaido.jp","toyoura.hokkaido.jp","tsubetsu.hokkaido.jp","tsukigata.hokkaido.jp","urakawa.hokkaido.jp","urausu.hokkaido.jp","uryu.hokkaido.jp","utashinai.hokkaido.jp","wakkanai.hokkaido.jp","wassamu.hokkaido.jp","yakumo.hokkaido.jp","yoichi.hokkaido.jp","aioi.hyogo.jp","akashi.hyogo.jp","ako.hyogo.jp","amagasaki.hyogo.jp","aogaki.hyogo.jp","asago.hyogo.jp","ashiya.hyogo.jp","awaji.hyogo.jp","fukusaki.hyogo.jp","goshiki.hyogo.jp","harima.hyogo.jp","himeji.hyogo.jp","ichikawa.hyogo.jp","inagawa.hyogo.jp","itami.hyogo.jp","kakogawa.hyogo.jp","kamigori.hyogo.jp","kamikawa.hyogo.jp","kasai.hyogo.jp","kasuga.hyogo.jp","kawanishi.hyogo.jp","miki.hyogo.jp","minamiawaji.hyogo.jp","nishinomiya.hyogo.jp","nishiwaki.hyogo.jp","ono.hyogo.jp","sanda.hyogo.jp","sannan.hyogo.jp","sasayama.hyogo.jp","sayo.hyogo.jp","shingu.hyogo.jp","shinonsen.hyogo.jp","shiso.hyogo.jp","sumoto.hyogo.jp","taishi.hyogo.jp","taka.hyogo.jp","takarazuka.hyogo.jp","takasago.hyogo.jp","takino.hyogo.jp","tamba.hyogo.jp","tatsuno.hyogo.jp","toyooka.hyogo.jp","yabu.hyogo.jp","yashiro.hyogo.jp","yoka.hyogo.jp","yokawa.hyogo.jp","ami.ibaraki.jp","asahi.ibaraki.jp","bando.ibaraki.jp","chikusei.ibaraki.jp","daigo.ibaraki.jp","fujishiro.ibaraki.jp","hitachi.ibaraki.jp","hitachinaka.ibaraki.jp","hitachiomiya.ibaraki.jp","hitachiota.ibaraki.jp","ibaraki.ibaraki.jp","ina.ibaraki.jp","inashiki.ibaraki.jp","itako.ibaraki.jp","iwama.ibaraki.jp","joso.ibaraki.jp","kamisu.ibaraki.jp","kasama.ibaraki.jp","kashima.ibaraki.jp","kasumigaura.ibaraki.jp","koga.ibaraki.jp","miho.ibaraki.jp","mito.ibaraki.jp","moriya.ibaraki.jp","naka.ibaraki.jp","namegata.ibaraki.jp","oarai.ibaraki.jp","ogawa.ibaraki.jp","omitama.ibaraki.jp","ryugasaki.ibaraki.jp","sakai.ibaraki.jp","sakuragawa.ibaraki.jp","shimodate.ibaraki.jp","shimotsuma.ibaraki.jp","shirosato.ibaraki.jp","sowa.ibaraki.jp","suifu.ibaraki.jp","takahagi.ibaraki.jp","tamatsukuri.ibaraki.jp","tokai.ibaraki.jp","tomobe.ibaraki.jp","tone.ibaraki.jp","toride.ibaraki.jp","tsuchiura.ibaraki.jp","tsukuba.ibaraki.jp","uchihara.ibaraki.jp","ushiku.ibaraki.jp","yachiyo.ibaraki.jp","yamagata.ibaraki.jp","yawara.ibaraki.jp","yuki.ibaraki.jp","anamizu.ishikawa.jp","hakui.ishikawa.jp","hakusan.ishikawa.jp","kaga.ishikawa.jp","kahoku.ishikawa.jp","kanazawa.ishikawa.jp","kawakita.ishikawa.jp","komatsu.ishikawa.jp","nakanoto.ishikawa.jp","nanao.ishikawa.jp","nomi.ishikawa.jp","nonoichi.ishikawa.jp","noto.ishikawa.jp","shika.ishikawa.jp","suzu.ishikawa.jp","tsubata.ishikawa.jp","tsurugi.ishikawa.jp","uchinada.ishikawa.jp","wajima.ishikawa.jp","fudai.iwate.jp","fujisawa.iwate.jp","hanamaki.iwate.jp","hiraizumi.iwate.jp","hirono.iwate.jp","ichinohe.iwate.jp","ichinoseki.iwate.jp","iwaizumi.iwate.jp","iwate.iwate.jp","joboji.iwate.jp","kamaishi.iwate.jp","kanegasaki.iwate.jp","karumai.iwate.jp","kawai.iwate.jp","kitakami.iwate.jp","kuji.iwate.jp","kunohe.iwate.jp","kuzumaki.iwate.jp","miyako.iwate.jp","mizusawa.iwate.jp","morioka.iwate.jp","ninohe.iwate.jp","noda.iwate.jp","ofunato.iwate.jp","oshu.iwate.jp","otsuchi.iwate.jp","rikuzentakata.iwate.jp","shiwa.iwate.jp","shizukuishi.iwate.jp","sumita.iwate.jp","tanohata.iwate.jp","tono.iwate.jp","yahaba.iwate.jp","yamada.iwate.jp","ayagawa.kagawa.jp","higashikagawa.kagawa.jp","kanonji.kagawa.jp","kotohira.kagawa.jp","manno.kagawa.jp","marugame.kagawa.jp","mitoyo.kagawa.jp","naoshima.kagawa.jp","sanuki.kagawa.jp","tadotsu.kagawa.jp","takamatsu.kagawa.jp","tonosho.kagawa.jp","uchinomi.kagawa.jp","utazu.kagawa.jp","zentsuji.kagawa.jp","akune.kagoshima.jp","amami.kagoshima.jp","hioki.kagoshima.jp","isa.kagoshima.jp","isen.kagoshima.jp","izumi.kagoshima.jp","kagoshima.kagoshima.jp","kanoya.kagoshima.jp","kawanabe.kagoshima.jp","kinko.kagoshima.jp","kouyama.kagoshima.jp","makurazaki.kagoshima.jp","matsumoto.kagoshima.jp","minamitane.kagoshima.jp","nakatane.kagoshima.jp","nishinoomote.kagoshima.jp","satsumasendai.kagoshima.jp","soo.kagoshima.jp","tarumizu.kagoshima.jp","yusui.kagoshima.jp","aikawa.kanagawa.jp","atsugi.kanagawa.jp","ayase.kanagawa.jp","chigasaki.kanagawa.jp","ebina.kanagawa.jp","fujisawa.kanagawa.jp","hadano.kanagawa.jp","hakone.kanagawa.jp","hiratsuka.kanagawa.jp","isehara.kanagawa.jp","kaisei.kanagawa.jp","kamakura.kanagawa.jp","kiyokawa.kanagawa.jp","matsuda.kanagawa.jp","minamiashigara.kanagawa.jp","miura.kanagawa.jp","nakai.kanagawa.jp","ninomiya.kanagawa.jp","odawara.kanagawa.jp","oi.kanagawa.jp","oiso.kanagawa.jp","sagamihara.kanagawa.jp","samukawa.kanagawa.jp","tsukui.kanagawa.jp","yamakita.kanagawa.jp","yamato.kanagawa.jp","yokosuka.kanagawa.jp","yugawara.kanagawa.jp","zama.kanagawa.jp","zushi.kanagawa.jp","aki.kochi.jp","geisei.kochi.jp","hidaka.kochi.jp","higashitsuno.kochi.jp","ino.kochi.jp","kagami.kochi.jp","kami.kochi.jp","kitagawa.kochi.jp","kochi.kochi.jp","mihara.kochi.jp","motoyama.kochi.jp","muroto.kochi.jp","nahari.kochi.jp","nakamura.kochi.jp","nankoku.kochi.jp","nishitosa.kochi.jp","niyodogawa.kochi.jp","ochi.kochi.jp","okawa.kochi.jp","otoyo.kochi.jp","otsuki.kochi.jp","sakawa.kochi.jp","sukumo.kochi.jp","susaki.kochi.jp","tosa.kochi.jp","tosashimizu.kochi.jp","toyo.kochi.jp","tsuno.kochi.jp","umaji.kochi.jp","yasuda.kochi.jp","yusuhara.kochi.jp","amakusa.kumamoto.jp","arao.kumamoto.jp","aso.kumamoto.jp","choyo.kumamoto.jp","gyokuto.kumamoto.jp","kamiamakusa.kumamoto.jp","kikuchi.kumamoto.jp","kumamoto.kumamoto.jp","mashiki.kumamoto.jp","mifune.kumamoto.jp","minamata.kumamoto.jp","minamioguni.kumamoto.jp","nagasu.kumamoto.jp","nishihara.kumamoto.jp","oguni.kumamoto.jp","ozu.kumamoto.jp","sumoto.kumamoto.jp","takamori.kumamoto.jp","uki.kumamoto.jp","uto.kumamoto.jp","yamaga.kumamoto.jp","yamato.kumamoto.jp","yatsushiro.kumamoto.jp","ayabe.kyoto.jp","fukuchiyama.kyoto.jp","higashiyama.kyoto.jp","ide.kyoto.jp","ine.kyoto.jp","joyo.kyoto.jp","kameoka.kyoto.jp","kamo.kyoto.jp","kita.kyoto.jp","kizu.kyoto.jp","kumiyama.kyoto.jp","kyotamba.kyoto.jp","kyotanabe.kyoto.jp","kyotango.kyoto.jp","maizuru.kyoto.jp","minami.kyoto.jp","minamiyamashiro.kyoto.jp","miyazu.kyoto.jp","muko.kyoto.jp","nagaokakyo.kyoto.jp","nakagyo.kyoto.jp","nantan.kyoto.jp","oyamazaki.kyoto.jp","sakyo.kyoto.jp","seika.kyoto.jp","tanabe.kyoto.jp","uji.kyoto.jp","ujitawara.kyoto.jp","wazuka.kyoto.jp","yamashina.kyoto.jp","yawata.kyoto.jp","asahi.mie.jp","inabe.mie.jp","ise.mie.jp","kameyama.mie.jp","kawagoe.mie.jp","kiho.mie.jp","kisosaki.mie.jp","kiwa.mie.jp","komono.mie.jp","kumano.mie.jp","kuwana.mie.jp","matsusaka.mie.jp","meiwa.mie.jp","mihama.mie.jp","minamiise.mie.jp","misugi.mie.jp","miyama.mie.jp","nabari.mie.jp","shima.mie.jp","suzuka.mie.jp","tado.mie.jp","taiki.mie.jp","taki.mie.jp","tamaki.mie.jp","toba.mie.jp","tsu.mie.jp","udono.mie.jp","ureshino.mie.jp","watarai.mie.jp","yokkaichi.mie.jp","furukawa.miyagi.jp","higashimatsushima.miyagi.jp","ishinomaki.miyagi.jp","iwanuma.miyagi.jp","kakuda.miyagi.jp","kami.miyagi.jp","kawasaki.miyagi.jp","marumori.miyagi.jp","matsushima.miyagi.jp","minamisanriku.miyagi.jp","misato.miyagi.jp","murata.miyagi.jp","natori.miyagi.jp","ogawara.miyagi.jp","ohira.miyagi.jp","onagawa.miyagi.jp","osaki.miyagi.jp","rifu.miyagi.jp","semine.miyagi.jp","shibata.miyagi.jp","shichikashuku.miyagi.jp","shikama.miyagi.jp","shiogama.miyagi.jp","shiroishi.miyagi.jp","tagajo.miyagi.jp","taiwa.miyagi.jp","tome.miyagi.jp","tomiya.miyagi.jp","wakuya.miyagi.jp","watari.miyagi.jp","yamamoto.miyagi.jp","zao.miyagi.jp","aya.miyazaki.jp","ebino.miyazaki.jp","gokase.miyazaki.jp","hyuga.miyazaki.jp","kadogawa.miyazaki.jp","kawaminami.miyazaki.jp","kijo.miyazaki.jp","kitagawa.miyazaki.jp","kitakata.miyazaki.jp","kitaura.miyazaki.jp","kobayashi.miyazaki.jp","kunitomi.miyazaki.jp","kushima.miyazaki.jp","mimata.miyazaki.jp","miyakonojo.miyazaki.jp","miyazaki.miyazaki.jp","morotsuka.miyazaki.jp","nichinan.miyazaki.jp","nishimera.miyazaki.jp","nobeoka.miyazaki.jp","saito.miyazaki.jp","shiiba.miyazaki.jp","shintomi.miyazaki.jp","takaharu.miyazaki.jp","takanabe.miyazaki.jp","takazaki.miyazaki.jp","tsuno.miyazaki.jp","achi.nagano.jp","agematsu.nagano.jp","anan.nagano.jp","aoki.nagano.jp","asahi.nagano.jp","azumino.nagano.jp","chikuhoku.nagano.jp","chikuma.nagano.jp","chino.nagano.jp","fujimi.nagano.jp","hakuba.nagano.jp","hara.nagano.jp","hiraya.nagano.jp","iida.nagano.jp","iijima.nagano.jp","iiyama.nagano.jp","iizuna.nagano.jp","ikeda.nagano.jp","ikusaka.nagano.jp","ina.nagano.jp","karuizawa.nagano.jp","kawakami.nagano.jp","kiso.nagano.jp","kisofukushima.nagano.jp","kitaaiki.nagano.jp","komagane.nagano.jp","komoro.nagano.jp","matsukawa.nagano.jp","matsumoto.nagano.jp","miasa.nagano.jp","minamiaiki.nagano.jp","minamimaki.nagano.jp","minamiminowa.nagano.jp","minowa.nagano.jp","miyada.nagano.jp","miyota.nagano.jp","mochizuki.nagano.jp","nagano.nagano.jp","nagawa.nagano.jp","nagiso.nagano.jp","nakagawa.nagano.jp","nakano.nagano.jp","nozawaonsen.nagano.jp","obuse.nagano.jp","ogawa.nagano.jp","okaya.nagano.jp","omachi.nagano.jp","omi.nagano.jp","ookuwa.nagano.jp","ooshika.nagano.jp","otaki.nagano.jp","otari.nagano.jp","sakae.nagano.jp","sakaki.nagano.jp","saku.nagano.jp","sakuho.nagano.jp","shimosuwa.nagano.jp","shinanomachi.nagano.jp","shiojiri.nagano.jp","suwa.nagano.jp","suzaka.nagano.jp","takagi.nagano.jp","takamori.nagano.jp","takayama.nagano.jp","tateshina.nagano.jp","tatsuno.nagano.jp","togakushi.nagano.jp","togura.nagano.jp","tomi.nagano.jp","ueda.nagano.jp","wada.nagano.jp","yamagata.nagano.jp","yamanouchi.nagano.jp","yasaka.nagano.jp","yasuoka.nagano.jp","chijiwa.nagasaki.jp","futsu.nagasaki.jp","goto.nagasaki.jp","hasami.nagasaki.jp","hirado.nagasaki.jp","iki.nagasaki.jp","isahaya.nagasaki.jp","kawatana.nagasaki.jp","kuchinotsu.nagasaki.jp","matsuura.nagasaki.jp","nagasaki.nagasaki.jp","obama.nagasaki.jp","omura.nagasaki.jp","oseto.nagasaki.jp","saikai.nagasaki.jp","sasebo.nagasaki.jp","seihi.nagasaki.jp","shimabara.nagasaki.jp","shinkamigoto.nagasaki.jp","togitsu.nagasaki.jp","tsushima.nagasaki.jp","unzen.nagasaki.jp","ando.nara.jp","gose.nara.jp","heguri.nara.jp","higashiyoshino.nara.jp","ikaruga.nara.jp","ikoma.nara.jp","kamikitayama.nara.jp","kanmaki.nara.jp","kashiba.nara.jp","kashihara.nara.jp","katsuragi.nara.jp","kawai.nara.jp","kawakami.nara.jp","kawanishi.nara.jp","koryo.nara.jp","kurotaki.nara.jp","mitsue.nara.jp","miyake.nara.jp","nara.nara.jp","nosegawa.nara.jp","oji.nara.jp","ouda.nara.jp","oyodo.nara.jp","sakurai.nara.jp","sango.nara.jp","shimoichi.nara.jp","shimokitayama.nara.jp","shinjo.nara.jp","soni.nara.jp","takatori.nara.jp","tawaramoto.nara.jp","tenkawa.nara.jp","tenri.nara.jp","uda.nara.jp","yamatokoriyama.nara.jp","yamatotakada.nara.jp","yamazoe.nara.jp","yoshino.nara.jp","aga.niigata.jp","agano.niigata.jp","gosen.niigata.jp","itoigawa.niigata.jp","izumozaki.niigata.jp","joetsu.niigata.jp","kamo.niigata.jp","kariwa.niigata.jp","kashiwazaki.niigata.jp","minamiuonuma.niigata.jp","mitsuke.niigata.jp","muika.niigata.jp","murakami.niigata.jp","myoko.niigata.jp","nagaoka.niigata.jp","niigata.niigata.jp","ojiya.niigata.jp","omi.niigata.jp","sado.niigata.jp","sanjo.niigata.jp","seiro.niigata.jp","seirou.niigata.jp","sekikawa.niigata.jp","shibata.niigata.jp","tagami.niigata.jp","tainai.niigata.jp","tochio.niigata.jp","tokamachi.niigata.jp","tsubame.niigata.jp","tsunan.niigata.jp","uonuma.niigata.jp","yahiko.niigata.jp","yoita.niigata.jp","yuzawa.niigata.jp","beppu.oita.jp","bungoono.oita.jp","bungotakada.oita.jp","hasama.oita.jp","hiji.oita.jp","himeshima.oita.jp","hita.oita.jp","kamitsue.oita.jp","kokonoe.oita.jp","kuju.oita.jp","kunisaki.oita.jp","kusu.oita.jp","oita.oita.jp","saiki.oita.jp","taketa.oita.jp","tsukumi.oita.jp","usa.oita.jp","usuki.oita.jp","yufu.oita.jp","akaiwa.okayama.jp","asakuchi.okayama.jp","bizen.okayama.jp","hayashima.okayama.jp","ibara.okayama.jp","kagamino.okayama.jp","kasaoka.okayama.jp","kibichuo.okayama.jp","kumenan.okayama.jp","kurashiki.okayama.jp","maniwa.okayama.jp","misaki.okayama.jp","nagi.okayama.jp","niimi.okayama.jp","nishiawakura.okayama.jp","okayama.okayama.jp","satosho.okayama.jp","setouchi.okayama.jp","shinjo.okayama.jp","shoo.okayama.jp","soja.okayama.jp","takahashi.okayama.jp","tamano.okayama.jp","tsuyama.okayama.jp","wake.okayama.jp","yakage.okayama.jp","aguni.okinawa.jp","ginowan.okinawa.jp","ginoza.okinawa.jp","gushikami.okinawa.jp","haebaru.okinawa.jp","higashi.okinawa.jp","hirara.okinawa.jp","iheya.okinawa.jp","ishigaki.okinawa.jp","ishikawa.okinawa.jp","itoman.okinawa.jp","izena.okinawa.jp","kadena.okinawa.jp","kin.okinawa.jp","kitadaito.okinawa.jp","kitanakagusuku.okinawa.jp","kumejima.okinawa.jp","kunigami.okinawa.jp","minamidaito.okinawa.jp","motobu.okinawa.jp","nago.okinawa.jp","naha.okinawa.jp","nakagusuku.okinawa.jp","nakijin.okinawa.jp","nanjo.okinawa.jp","nishihara.okinawa.jp","ogimi.okinawa.jp","okinawa.okinawa.jp","onna.okinawa.jp","shimoji.okinawa.jp","taketomi.okinawa.jp","tarama.okinawa.jp","tokashiki.okinawa.jp","tomigusuku.okinawa.jp","tonaki.okinawa.jp","urasoe.okinawa.jp","uruma.okinawa.jp","yaese.okinawa.jp","yomitan.okinawa.jp","yonabaru.okinawa.jp","yonaguni.okinawa.jp","zamami.okinawa.jp","abeno.osaka.jp","chihayaakasaka.osaka.jp","chuo.osaka.jp","daito.osaka.jp","fujiidera.osaka.jp","habikino.osaka.jp","hannan.osaka.jp","higashiosaka.osaka.jp","higashisumiyoshi.osaka.jp","higashiyodogawa.osaka.jp","hirakata.osaka.jp","ibaraki.osaka.jp","ikeda.osaka.jp","izumi.osaka.jp","izumiotsu.osaka.jp","izumisano.osaka.jp","kadoma.osaka.jp","kaizuka.osaka.jp","kanan.osaka.jp","kashiwara.osaka.jp","katano.osaka.jp","kawachinagano.osaka.jp","kishiwada.osaka.jp","kita.osaka.jp","kumatori.osaka.jp","matsubara.osaka.jp","minato.osaka.jp","minoh.osaka.jp","misaki.osaka.jp","moriguchi.osaka.jp","neyagawa.osaka.jp","nishi.osaka.jp","nose.osaka.jp","osakasayama.osaka.jp","sakai.osaka.jp","sayama.osaka.jp","sennan.osaka.jp","settsu.osaka.jp","shijonawate.osaka.jp","shimamoto.osaka.jp","suita.osaka.jp","tadaoka.osaka.jp","taishi.osaka.jp","tajiri.osaka.jp","takaishi.osaka.jp","takatsuki.osaka.jp","tondabayashi.osaka.jp","toyonaka.osaka.jp","toyono.osaka.jp","yao.osaka.jp","ariake.saga.jp","arita.saga.jp","fukudomi.saga.jp","genkai.saga.jp","hamatama.saga.jp","hizen.saga.jp","imari.saga.jp","kamimine.saga.jp","kanzaki.saga.jp","karatsu.saga.jp","kashima.saga.jp","kitagata.saga.jp","kitahata.saga.jp","kiyama.saga.jp","kouhoku.saga.jp","kyuragi.saga.jp","nishiarita.saga.jp","ogi.saga.jp","omachi.saga.jp","ouchi.saga.jp","saga.saga.jp","shiroishi.saga.jp","taku.saga.jp","tara.saga.jp","tosu.saga.jp","yoshinogari.saga.jp","arakawa.saitama.jp","asaka.saitama.jp","chichibu.saitama.jp","fujimi.saitama.jp","fujimino.saitama.jp","fukaya.saitama.jp","hanno.saitama.jp","hanyu.saitama.jp","hasuda.saitama.jp","hatogaya.saitama.jp","hatoyama.saitama.jp","hidaka.saitama.jp","higashichichibu.saitama.jp","higashimatsuyama.saitama.jp","honjo.saitama.jp","ina.saitama.jp","iruma.saitama.jp","iwatsuki.saitama.jp","kamiizumi.saitama.jp","kamikawa.saitama.jp","kamisato.saitama.jp","kasukabe.saitama.jp","kawagoe.saitama.jp","kawaguchi.saitama.jp","kawajima.saitama.jp","kazo.saitama.jp","kitamoto.saitama.jp","koshigaya.saitama.jp","kounosu.saitama.jp","kuki.saitama.jp","kumagaya.saitama.jp","matsubushi.saitama.jp","minano.saitama.jp","misato.saitama.jp","miyashiro.saitama.jp","miyoshi.saitama.jp","moroyama.saitama.jp","nagatoro.saitama.jp","namegawa.saitama.jp","niiza.saitama.jp","ogano.saitama.jp","ogawa.saitama.jp","ogose.saitama.jp","okegawa.saitama.jp","omiya.saitama.jp","otaki.saitama.jp","ranzan.saitama.jp","ryokami.saitama.jp","saitama.saitama.jp","sakado.saitama.jp","satte.saitama.jp","sayama.saitama.jp","shiki.saitama.jp","shiraoka.saitama.jp","soka.saitama.jp","sugito.saitama.jp","toda.saitama.jp","tokigawa.saitama.jp","tokorozawa.saitama.jp","tsurugashima.saitama.jp","urawa.saitama.jp","warabi.saitama.jp","yashio.saitama.jp","yokoze.saitama.jp","yono.saitama.jp","yorii.saitama.jp","yoshida.saitama.jp","yoshikawa.saitama.jp","yoshimi.saitama.jp","aisho.shiga.jp","gamo.shiga.jp","higashiomi.shiga.jp","hikone.shiga.jp","koka.shiga.jp","konan.shiga.jp","kosei.shiga.jp","koto.shiga.jp","kusatsu.shiga.jp","maibara.shiga.jp","moriyama.shiga.jp","nagahama.shiga.jp","nishiazai.shiga.jp","notogawa.shiga.jp","omihachiman.shiga.jp","otsu.shiga.jp","ritto.shiga.jp","ryuoh.shiga.jp","takashima.shiga.jp","takatsuki.shiga.jp","torahime.shiga.jp","toyosato.shiga.jp","yasu.shiga.jp","akagi.shimane.jp","ama.shimane.jp","gotsu.shimane.jp","hamada.shimane.jp","higashiizumo.shimane.jp","hikawa.shimane.jp","hikimi.shimane.jp","izumo.shimane.jp","kakinoki.shimane.jp","masuda.shimane.jp","matsue.shimane.jp","misato.shimane.jp","nishinoshima.shimane.jp","ohda.shimane.jp","okinoshima.shimane.jp","okuizumo.shimane.jp","shimane.shimane.jp","tamayu.shimane.jp","tsuwano.shimane.jp","unnan.shimane.jp","yakumo.shimane.jp","yasugi.shimane.jp","yatsuka.shimane.jp","arai.shizuoka.jp","atami.shizuoka.jp","fuji.shizuoka.jp","fujieda.shizuoka.jp","fujikawa.shizuoka.jp","fujinomiya.shizuoka.jp","fukuroi.shizuoka.jp","gotemba.shizuoka.jp","haibara.shizuoka.jp","hamamatsu.shizuoka.jp","higashiizu.shizuoka.jp","ito.shizuoka.jp","iwata.shizuoka.jp","izu.shizuoka.jp","izunokuni.shizuoka.jp","kakegawa.shizuoka.jp","kannami.shizuoka.jp","kawanehon.shizuoka.jp","kawazu.shizuoka.jp","kikugawa.shizuoka.jp","kosai.shizuoka.jp","makinohara.shizuoka.jp","matsuzaki.shizuoka.jp","minamiizu.shizuoka.jp","mishima.shizuoka.jp","morimachi.shizuoka.jp","nishiizu.shizuoka.jp","numazu.shizuoka.jp","omaezaki.shizuoka.jp","shimada.shizuoka.jp","shimizu.shizuoka.jp","shimoda.shizuoka.jp","shizuoka.shizuoka.jp","susono.shizuoka.jp","yaizu.shizuoka.jp","yoshida.shizuoka.jp","ashikaga.tochigi.jp","bato.tochigi.jp","haga.tochigi.jp","ichikai.tochigi.jp","iwafune.tochigi.jp","kaminokawa.tochigi.jp","kanuma.tochigi.jp","karasuyama.tochigi.jp","kuroiso.tochigi.jp","mashiko.tochigi.jp","mibu.tochigi.jp","moka.tochigi.jp","motegi.tochigi.jp","nasu.tochigi.jp","nasushiobara.tochigi.jp","nikko.tochigi.jp","nishikata.tochigi.jp","nogi.tochigi.jp","ohira.tochigi.jp","ohtawara.tochigi.jp","oyama.tochigi.jp","sakura.tochigi.jp","sano.tochigi.jp","shimotsuke.tochigi.jp","shioya.tochigi.jp","takanezawa.tochigi.jp","tochigi.tochigi.jp","tsuga.tochigi.jp","ujiie.tochigi.jp","utsunomiya.tochigi.jp","yaita.tochigi.jp","aizumi.tokushima.jp","anan.tokushima.jp","ichiba.tokushima.jp","itano.tokushima.jp","kainan.tokushima.jp","komatsushima.tokushima.jp","matsushige.tokushima.jp","mima.tokushima.jp","minami.tokushima.jp","miyoshi.tokushima.jp","mugi.tokushima.jp","nakagawa.tokushima.jp","naruto.tokushima.jp","sanagochi.tokushima.jp","shishikui.tokushima.jp","tokushima.tokushima.jp","wajiki.tokushima.jp","adachi.tokyo.jp","akiruno.tokyo.jp","akishima.tokyo.jp","aogashima.tokyo.jp","arakawa.tokyo.jp","bunkyo.tokyo.jp","chiyoda.tokyo.jp","chofu.tokyo.jp","chuo.tokyo.jp","edogawa.tokyo.jp","fuchu.tokyo.jp","fussa.tokyo.jp","hachijo.tokyo.jp","hachioji.tokyo.jp","hamura.tokyo.jp","higashikurume.tokyo.jp","higashimurayama.tokyo.jp","higashiyamato.tokyo.jp","hino.tokyo.jp","hinode.tokyo.jp","hinohara.tokyo.jp","inagi.tokyo.jp","itabashi.tokyo.jp","katsushika.tokyo.jp","kita.tokyo.jp","kiyose.tokyo.jp","kodaira.tokyo.jp","koganei.tokyo.jp","kokubunji.tokyo.jp","komae.tokyo.jp","koto.tokyo.jp","kouzushima.tokyo.jp","kunitachi.tokyo.jp","machida.tokyo.jp","meguro.tokyo.jp","minato.tokyo.jp","mitaka.tokyo.jp","mizuho.tokyo.jp","musashimurayama.tokyo.jp","musashino.tokyo.jp","nakano.tokyo.jp","nerima.tokyo.jp","ogasawara.tokyo.jp","okutama.tokyo.jp","ome.tokyo.jp","oshima.tokyo.jp","ota.tokyo.jp","setagaya.tokyo.jp","shibuya.tokyo.jp","shinagawa.tokyo.jp","shinjuku.tokyo.jp","suginami.tokyo.jp","sumida.tokyo.jp","tachikawa.tokyo.jp","taito.tokyo.jp","tama.tokyo.jp","toshima.tokyo.jp","chizu.tottori.jp","hino.tottori.jp","kawahara.tottori.jp","koge.tottori.jp","kotoura.tottori.jp","misasa.tottori.jp","nanbu.tottori.jp","nichinan.tottori.jp","sakaiminato.tottori.jp","tottori.tottori.jp","wakasa.tottori.jp","yazu.tottori.jp","yonago.tottori.jp","asahi.toyama.jp","fuchu.toyama.jp","fukumitsu.toyama.jp","funahashi.toyama.jp","himi.toyama.jp","imizu.toyama.jp","inami.toyama.jp","johana.toyama.jp","kamiichi.toyama.jp","kurobe.toyama.jp","nakaniikawa.toyama.jp","namerikawa.toyama.jp","nanto.toyama.jp","nyuzen.toyama.jp","oyabe.toyama.jp","taira.toyama.jp","takaoka.toyama.jp","tateyama.toyama.jp","toga.toyama.jp","tonami.toyama.jp","toyama.toyama.jp","unazuki.toyama.jp","uozu.toyama.jp","yamada.toyama.jp","arida.wakayama.jp","aridagawa.wakayama.jp","gobo.wakayama.jp","hashimoto.wakayama.jp","hidaka.wakayama.jp","hirogawa.wakayama.jp","inami.wakayama.jp","iwade.wakayama.jp","kainan.wakayama.jp","kamitonda.wakayama.jp","katsuragi.wakayama.jp","kimino.wakayama.jp","kinokawa.wakayama.jp","kitayama.wakayama.jp","koya.wakayama.jp","koza.wakayama.jp","kozagawa.wakayama.jp","kudoyama.wakayama.jp","kushimoto.wakayama.jp","mihama.wakayama.jp","misato.wakayama.jp","nachikatsuura.wakayama.jp","shingu.wakayama.jp","shirahama.wakayama.jp","taiji.wakayama.jp","tanabe.wakayama.jp","wakayama.wakayama.jp","yuasa.wakayama.jp","yura.wakayama.jp","asahi.yamagata.jp","funagata.yamagata.jp","higashine.yamagata.jp","iide.yamagata.jp","kahoku.yamagata.jp","kaminoyama.yamagata.jp","kaneyama.yamagata.jp","kawanishi.yamagata.jp","mamurogawa.yamagata.jp","mikawa.yamagata.jp","murayama.yamagata.jp","nagai.yamagata.jp","nakayama.yamagata.jp","nanyo.yamagata.jp","nishikawa.yamagata.jp","obanazawa.yamagata.jp","oe.yamagata.jp","oguni.yamagata.jp","ohkura.yamagata.jp","oishida.yamagata.jp","sagae.yamagata.jp","sakata.yamagata.jp","sakegawa.yamagata.jp","shinjo.yamagata.jp","shirataka.yamagata.jp","shonai.yamagata.jp","takahata.yamagata.jp","tendo.yamagata.jp","tozawa.yamagata.jp","tsuruoka.yamagata.jp","yamagata.yamagata.jp","yamanobe.yamagata.jp","yonezawa.yamagata.jp","yuza.yamagata.jp","abu.yamaguchi.jp","hagi.yamaguchi.jp","hikari.yamaguchi.jp","hofu.yamaguchi.jp","iwakuni.yamaguchi.jp","kudamatsu.yamaguchi.jp","mitou.yamaguchi.jp","nagato.yamaguchi.jp","oshima.yamaguchi.jp","shimonoseki.yamaguchi.jp","shunan.yamaguchi.jp","tabuse.yamaguchi.jp","tokuyama.yamaguchi.jp","toyota.yamaguchi.jp","ube.yamaguchi.jp","yuu.yamaguchi.jp","chuo.yamanashi.jp","doshi.yamanashi.jp","fuefuki.yamanashi.jp","fujikawa.yamanashi.jp","fujikawaguchiko.yamanashi.jp","fujiyoshida.yamanashi.jp","hayakawa.yamanashi.jp","hokuto.yamanashi.jp","ichikawamisato.yamanashi.jp","kai.yamanashi.jp","kofu.yamanashi.jp","koshu.yamanashi.jp","kosuge.yamanashi.jp","minami-alps.yamanashi.jp","minobu.yamanashi.jp","nakamichi.yamanashi.jp","nanbu.yamanashi.jp","narusawa.yamanashi.jp","nirasaki.yamanashi.jp","nishikatsura.yamanashi.jp","oshino.yamanashi.jp","otsuki.yamanashi.jp","showa.yamanashi.jp","tabayama.yamanashi.jp","tsuru.yamanashi.jp","uenohara.yamanashi.jp","yamanakako.yamanashi.jp","yamanashi.yamanashi.jp","ke","ac.ke","co.ke","go.ke","info.ke","me.ke","mobi.ke","ne.ke","or.ke","sc.ke","kg","org.kg","net.kg","com.kg","edu.kg","gov.kg","mil.kg","*.kh","ki","edu.ki","biz.ki","net.ki","org.ki","gov.ki","info.ki","com.ki","km","org.km","nom.km","gov.km","prd.km","tm.km","edu.km","mil.km","ass.km","com.km","coop.km","asso.km","presse.km","medecin.km","notaires.km","pharmaciens.km","veterinaire.km","gouv.km","kn","net.kn","org.kn","edu.kn","gov.kn","kp","com.kp","edu.kp","gov.kp","org.kp","rep.kp","tra.kp","kr","ac.kr","co.kr","es.kr","go.kr","hs.kr","kg.kr","mil.kr","ms.kr","ne.kr","or.kr","pe.kr","re.kr","sc.kr","busan.kr","chungbuk.kr","chungnam.kr","daegu.kr","daejeon.kr","gangwon.kr","gwangju.kr","gyeongbuk.kr","gyeonggi.kr","gyeongnam.kr","incheon.kr","jeju.kr","jeonbuk.kr","jeonnam.kr","seoul.kr","ulsan.kr","kw","com.kw","edu.kw","emb.kw","gov.kw","ind.kw","net.kw","org.kw","ky","edu.ky","gov.ky","com.ky","org.ky","net.ky","kz","org.kz","edu.kz","net.kz","gov.kz","mil.kz","com.kz","la","int.la","net.la","info.la","edu.la","gov.la","per.la","com.la","org.la","lb","com.lb","edu.lb","gov.lb","net.lb","org.lb","lc","com.lc","net.lc","co.lc","org.lc","edu.lc","gov.lc","li","lk","gov.lk","sch.lk","net.lk","int.lk","com.lk","org.lk","edu.lk","ngo.lk","soc.lk","web.lk","ltd.lk","assn.lk","grp.lk","hotel.lk","ac.lk","lr","com.lr","edu.lr","gov.lr","org.lr","net.lr","ls","ac.ls","biz.ls","co.ls","edu.ls","gov.ls","info.ls","net.ls","org.ls","sc.ls","lt","gov.lt","lu","lv","com.lv","edu.lv","gov.lv","org.lv","mil.lv","id.lv","net.lv","asn.lv","conf.lv","ly","com.ly","net.ly","gov.ly","plc.ly","edu.ly","sch.ly","med.ly","org.ly","id.ly","ma","co.ma","net.ma","gov.ma","org.ma","ac.ma","press.ma","mc","tm.mc","asso.mc","md","me","co.me","net.me","org.me","edu.me","ac.me","gov.me","its.me","priv.me","mg","org.mg","nom.mg","gov.mg","prd.mg","tm.mg","edu.mg","mil.mg","com.mg","co.mg","mh","mil","mk","com.mk","org.mk","net.mk","edu.mk","gov.mk","inf.mk","name.mk","ml","com.ml","edu.ml","gouv.ml","gov.ml","net.ml","org.ml","presse.ml","*.mm","mn","gov.mn","edu.mn","org.mn","mo","com.mo","net.mo","org.mo","edu.mo","gov.mo","mobi","mp","mq","mr","gov.mr","ms","com.ms","edu.ms","gov.ms","net.ms","org.ms","mt","com.mt","edu.mt","net.mt","org.mt","mu","com.mu","net.mu","org.mu","gov.mu","ac.mu","co.mu","or.mu","museum","academy.museum","agriculture.museum","air.museum","airguard.museum","alabama.museum","alaska.museum","amber.museum","ambulance.museum","american.museum","americana.museum","americanantiques.museum","americanart.museum","amsterdam.museum","and.museum","annefrank.museum","anthro.museum","anthropology.museum","antiques.museum","aquarium.museum","arboretum.museum","archaeological.museum","archaeology.museum","architecture.museum","art.museum","artanddesign.museum","artcenter.museum","artdeco.museum","arteducation.museum","artgallery.museum","arts.museum","artsandcrafts.museum","asmatart.museum","assassination.museum","assisi.museum","association.museum","astronomy.museum","atlanta.museum","austin.museum","australia.museum","automotive.museum","aviation.museum","axis.museum","badajoz.museum","baghdad.museum","bahn.museum","bale.museum","baltimore.museum","barcelona.museum","baseball.museum","basel.museum","baths.museum","bauern.museum","beauxarts.museum","beeldengeluid.museum","bellevue.museum","bergbau.museum","berkeley.museum","berlin.museum","bern.museum","bible.museum","bilbao.museum","bill.museum","birdart.museum","birthplace.museum","bonn.museum","boston.museum","botanical.museum","botanicalgarden.museum","botanicgarden.museum","botany.museum","brandywinevalley.museum","brasil.museum","bristol.museum","british.museum","britishcolumbia.museum","broadcast.museum","brunel.museum","brussel.museum","brussels.museum","bruxelles.museum","building.museum","burghof.museum","bus.museum","bushey.museum","cadaques.museum","california.museum","cambridge.museum","can.museum","canada.museum","capebreton.museum","carrier.museum","cartoonart.museum","casadelamoneda.museum","castle.museum","castres.museum","celtic.museum","center.museum","chattanooga.museum","cheltenham.museum","chesapeakebay.museum","chicago.museum","children.museum","childrens.museum","childrensgarden.museum","chiropractic.museum","chocolate.museum","christiansburg.museum","cincinnati.museum","cinema.museum","circus.museum","civilisation.museum","civilization.museum","civilwar.museum","clinton.museum","clock.museum","coal.museum","coastaldefence.museum","cody.museum","coldwar.museum","collection.museum","colonialwilliamsburg.museum","coloradoplateau.museum","columbia.museum","columbus.museum","communication.museum","communications.museum","community.museum","computer.museum","computerhistory.museum","comunicações.museum","contemporary.museum","contemporaryart.museum","convent.museum","copenhagen.museum","corporation.museum","correios-e-telecomunicações.museum","corvette.museum","costume.museum","countryestate.museum","county.museum","crafts.museum","cranbrook.museum","creation.museum","cultural.museum","culturalcenter.museum","culture.museum","cyber.museum","cymru.museum","dali.museum","dallas.museum","database.museum","ddr.museum","decorativearts.museum","delaware.museum","delmenhorst.museum","denmark.museum","depot.museum","design.museum","detroit.museum","dinosaur.museum","discovery.museum","dolls.museum","donostia.museum","durham.museum","eastafrica.museum","eastcoast.museum","education.museum","educational.museum","egyptian.museum","eisenbahn.museum","elburg.museum","elvendrell.museum","embroidery.museum","encyclopedic.museum","england.museum","entomology.museum","environment.museum","environmentalconservation.museum","epilepsy.museum","essex.museum","estate.museum","ethnology.museum","exeter.museum","exhibition.museum","family.museum","farm.museum","farmequipment.museum","farmers.museum","farmstead.museum","field.museum","figueres.museum","filatelia.museum","film.museum","fineart.museum","finearts.museum","finland.museum","flanders.museum","florida.museum","force.museum","fortmissoula.museum","fortworth.museum","foundation.museum","francaise.museum","frankfurt.museum","franziskaner.museum","freemasonry.museum","freiburg.museum","fribourg.museum","frog.museum","fundacio.museum","furniture.museum","gallery.museum","garden.museum","gateway.museum","geelvinck.museum","gemological.museum","geology.museum","georgia.museum","giessen.museum","glas.museum","glass.museum","gorge.museum","grandrapids.museum","graz.museum","guernsey.museum","halloffame.museum","hamburg.museum","handson.museum","harvestcelebration.museum","hawaii.museum","health.museum","heimatunduhren.museum","hellas.museum","helsinki.museum","hembygdsforbund.museum","heritage.museum","histoire.museum","historical.museum","historicalsociety.museum","historichouses.museum","historisch.museum","historisches.museum","history.museum","historyofscience.museum","horology.museum","house.museum","humanities.museum","illustration.museum","imageandsound.museum","indian.museum","indiana.museum","indianapolis.museum","indianmarket.museum","intelligence.museum","interactive.museum","iraq.museum","iron.museum","isleofman.museum","jamison.museum","jefferson.museum","jerusalem.museum","jewelry.museum","jewish.museum","jewishart.museum","jfk.museum","journalism.museum","judaica.museum","judygarland.museum","juedisches.museum","juif.museum","karate.museum","karikatur.museum","kids.museum","koebenhavn.museum","koeln.museum","kunst.museum","kunstsammlung.museum","kunstunddesign.museum","labor.museum","labour.museum","lajolla.museum","lancashire.museum","landes.museum","lans.museum","läns.museum","larsson.museum","lewismiller.museum","lincoln.museum","linz.museum","living.museum","livinghistory.museum","localhistory.museum","london.museum","losangeles.museum","louvre.museum","loyalist.museum","lucerne.museum","luxembourg.museum","luzern.museum","mad.museum","madrid.museum","mallorca.museum","manchester.museum","mansion.museum","mansions.museum","manx.museum","marburg.museum","maritime.museum","maritimo.museum","maryland.museum","marylhurst.museum","media.museum","medical.museum","medizinhistorisches.museum","meeres.museum","memorial.museum","mesaverde.museum","michigan.museum","midatlantic.museum","military.museum","mill.museum","miners.museum","mining.museum","minnesota.museum","missile.museum","missoula.museum","modern.museum","moma.museum","money.museum","monmouth.museum","monticello.museum","montreal.museum","moscow.museum","motorcycle.museum","muenchen.museum","muenster.museum","mulhouse.museum","muncie.museum","museet.museum","museumcenter.museum","museumvereniging.museum","music.museum","national.museum","nationalfirearms.museum","nationalheritage.museum","nativeamerican.museum","naturalhistory.museum","naturalhistorymuseum.museum","naturalsciences.museum","nature.museum","naturhistorisches.museum","natuurwetenschappen.museum","naumburg.museum","naval.museum","nebraska.museum","neues.museum","newhampshire.museum","newjersey.museum","newmexico.museum","newport.museum","newspaper.museum","newyork.museum","niepce.museum","norfolk.museum","north.museum","nrw.museum","nyc.museum","nyny.museum","oceanographic.museum","oceanographique.museum","omaha.museum","online.museum","ontario.museum","openair.museum","oregon.museum","oregontrail.museum","otago.museum","oxford.museum","pacific.museum","paderborn.museum","palace.museum","paleo.museum","palmsprings.museum","panama.museum","paris.museum","pasadena.museum","pharmacy.museum","philadelphia.museum","philadelphiaarea.museum","philately.museum","phoenix.museum","photography.museum","pilots.museum","pittsburgh.museum","planetarium.museum","plantation.museum","plants.museum","plaza.museum","portal.museum","portland.museum","portlligat.museum","posts-and-telecommunications.museum","preservation.museum","presidio.museum","press.museum","project.museum","public.museum","pubol.museum","quebec.museum","railroad.museum","railway.museum","research.museum","resistance.museum","riodejaneiro.museum","rochester.museum","rockart.museum","roma.museum","russia.museum","saintlouis.museum","salem.museum","salvadordali.museum","salzburg.museum","sandiego.museum","sanfrancisco.museum","santabarbara.museum","santacruz.museum","santafe.museum","saskatchewan.museum","satx.museum","savannahga.museum","schlesisches.museum","schoenbrunn.museum","schokoladen.museum","school.museum","schweiz.museum","science.museum","scienceandhistory.museum","scienceandindustry.museum","sciencecenter.museum","sciencecenters.museum","science-fiction.museum","sciencehistory.museum","sciences.museum","sciencesnaturelles.museum","scotland.museum","seaport.museum","settlement.museum","settlers.museum","shell.museum","sherbrooke.museum","sibenik.museum","silk.museum","ski.museum","skole.museum","society.museum","sologne.museum","soundandvision.museum","southcarolina.museum","southwest.museum","space.museum","spy.museum","square.museum","stadt.museum","stalbans.museum","starnberg.museum","state.museum","stateofdelaware.museum","station.museum","steam.museum","steiermark.museum","stjohn.museum","stockholm.museum","stpetersburg.museum","stuttgart.museum","suisse.museum","surgeonshall.museum","surrey.museum","svizzera.museum","sweden.museum","sydney.museum","tank.museum","tcm.museum","technology.museum","telekommunikation.museum","television.museum","texas.museum","textile.museum","theater.museum","time.museum","timekeeping.museum","topology.museum","torino.museum","touch.museum","town.museum","transport.museum","tree.museum","trolley.museum","trust.museum","trustee.museum","uhren.museum","ulm.museum","undersea.museum","university.museum","usa.museum","usantiques.museum","usarts.museum","uscountryestate.museum","usculture.museum","usdecorativearts.museum","usgarden.museum","ushistory.museum","ushuaia.museum","uslivinghistory.museum","utah.museum","uvic.museum","valley.museum","vantaa.museum","versailles.museum","viking.museum","village.museum","virginia.museum","virtual.museum","virtuel.museum","vlaanderen.museum","volkenkunde.museum","wales.museum","wallonie.museum","war.museum","washingtondc.museum","watchandclock.museum","watch-and-clock.museum","western.museum","westfalen.museum","whaling.museum","wildlife.museum","williamsburg.museum","windmill.museum","workshop.museum","york.museum","yorkshire.museum","yosemite.museum","youth.museum","zoological.museum","zoology.museum","ירושלים.museum","иком.museum","mv","aero.mv","biz.mv","com.mv","coop.mv","edu.mv","gov.mv","info.mv","int.mv","mil.mv","museum.mv","name.mv","net.mv","org.mv","pro.mv","mw","ac.mw","biz.mw","co.mw","com.mw","coop.mw","edu.mw","gov.mw","int.mw","museum.mw","net.mw","org.mw","mx","com.mx","org.mx","gob.mx","edu.mx","net.mx","my","com.my","net.my","org.my","gov.my","edu.my","mil.my","name.my","mz","ac.mz","adv.mz","co.mz","edu.mz","gov.mz","mil.mz","net.mz","org.mz","na","info.na","pro.na","name.na","school.na","or.na","dr.na","us.na","mx.na","ca.na","in.na","cc.na","tv.na","ws.na","mobi.na","co.na","com.na","org.na","name","nc","asso.nc","nom.nc","ne","net","nf","com.nf","net.nf","per.nf","rec.nf","web.nf","arts.nf","firm.nf","info.nf","other.nf","store.nf","ng","com.ng","edu.ng","gov.ng","i.ng","mil.ng","mobi.ng","name.ng","net.ng","org.ng","sch.ng","ni","ac.ni","biz.ni","co.ni","com.ni","edu.ni","gob.ni","in.ni","info.ni","int.ni","mil.ni","net.ni","nom.ni","org.ni","web.ni","nl","no","fhs.no","vgs.no","fylkesbibl.no","folkebibl.no","museum.no","idrett.no","priv.no","mil.no","stat.no","dep.no","kommune.no","herad.no","aa.no","ah.no","bu.no","fm.no","hl.no","hm.no","jan-mayen.no","mr.no","nl.no","nt.no","of.no","ol.no","oslo.no","rl.no","sf.no","st.no","svalbard.no","tm.no","tr.no","va.no","vf.no","gs.aa.no","gs.ah.no","gs.bu.no","gs.fm.no","gs.hl.no","gs.hm.no","gs.jan-mayen.no","gs.mr.no","gs.nl.no","gs.nt.no","gs.of.no","gs.ol.no","gs.oslo.no","gs.rl.no","gs.sf.no","gs.st.no","gs.svalbard.no","gs.tm.no","gs.tr.no","gs.va.no","gs.vf.no","akrehamn.no","åkrehamn.no","algard.no","ålgård.no","arna.no","brumunddal.no","bryne.no","bronnoysund.no","brønnøysund.no","drobak.no","drøbak.no","egersund.no","fetsund.no","floro.no","florø.no","fredrikstad.no","hokksund.no","honefoss.no","hønefoss.no","jessheim.no","jorpeland.no","jørpeland.no","kirkenes.no","kopervik.no","krokstadelva.no","langevag.no","langevåg.no","leirvik.no","mjondalen.no","mjøndalen.no","mo-i-rana.no","mosjoen.no","mosjøen.no","nesoddtangen.no","orkanger.no","osoyro.no","osøyro.no","raholt.no","råholt.no","sandnessjoen.no","sandnessjøen.no","skedsmokorset.no","slattum.no","spjelkavik.no","stathelle.no","stavern.no","stjordalshalsen.no","stjørdalshalsen.no","tananger.no","tranby.no","vossevangen.no","afjord.no","åfjord.no","agdenes.no","al.no","ål.no","alesund.no","ålesund.no","alstahaug.no","alta.no","áltá.no","alaheadju.no","álaheadju.no","alvdal.no","amli.no","åmli.no","amot.no","åmot.no","andebu.no","andoy.no","andøy.no","andasuolo.no","ardal.no","årdal.no","aremark.no","arendal.no","ås.no","aseral.no","åseral.no","asker.no","askim.no","askvoll.no","askoy.no","askøy.no","asnes.no","åsnes.no","audnedaln.no","aukra.no","aure.no","aurland.no","aurskog-holand.no","aurskog-høland.no","austevoll.no","austrheim.no","averoy.no","averøy.no","balestrand.no","ballangen.no","balat.no","bálát.no","balsfjord.no","bahccavuotna.no","báhccavuotna.no","bamble.no","bardu.no","beardu.no","beiarn.no","bajddar.no","bájddar.no","baidar.no","báidár.no","berg.no","bergen.no","berlevag.no","berlevåg.no","bearalvahki.no","bearalváhki.no","bindal.no","birkenes.no","bjarkoy.no","bjarkøy.no","bjerkreim.no","bjugn.no","bodo.no","bodø.no","badaddja.no","bådåddjå.no","budejju.no","bokn.no","bremanger.no","bronnoy.no","brønnøy.no","bygland.no","bykle.no","barum.no","bærum.no","bo.telemark.no","bø.telemark.no","bo.nordland.no","bø.nordland.no","bievat.no","bievát.no","bomlo.no","bømlo.no","batsfjord.no","båtsfjord.no","bahcavuotna.no","báhcavuotna.no","dovre.no","drammen.no","drangedal.no","dyroy.no","dyrøy.no","donna.no","dønna.no","eid.no","eidfjord.no","eidsberg.no","eidskog.no","eidsvoll.no","eigersund.no","elverum.no","enebakk.no","engerdal.no","etne.no","etnedal.no","evenes.no","evenassi.no","evenášši.no","evje-og-hornnes.no","farsund.no","fauske.no","fuossko.no","fuoisku.no","fedje.no","fet.no","finnoy.no","finnøy.no","fitjar.no","fjaler.no","fjell.no","flakstad.no","flatanger.no","flekkefjord.no","flesberg.no","flora.no","fla.no","flå.no","folldal.no","forsand.no","fosnes.no","frei.no","frogn.no","froland.no","frosta.no","frana.no","fræna.no","froya.no","frøya.no","fusa.no","fyresdal.no","forde.no","førde.no","gamvik.no","gangaviika.no","gáŋgaviika.no","gaular.no","gausdal.no","gildeskal.no","gildeskål.no","giske.no","gjemnes.no","gjerdrum.no","gjerstad.no","gjesdal.no","gjovik.no","gjøvik.no","gloppen.no","gol.no","gran.no","grane.no","granvin.no","gratangen.no","grimstad.no","grong.no","kraanghke.no","kråanghke.no","grue.no","gulen.no","hadsel.no","halden.no","halsa.no","hamar.no","hamaroy.no","habmer.no","hábmer.no","hapmir.no","hápmir.no","hammerfest.no","hammarfeasta.no","hámmárfeasta.no","haram.no","hareid.no","harstad.no","hasvik.no","aknoluokta.no","ákŋoluokta.no","hattfjelldal.no","aarborte.no","haugesund.no","hemne.no","hemnes.no","hemsedal.no","heroy.more-og-romsdal.no","herøy.møre-og-romsdal.no","heroy.nordland.no","herøy.nordland.no","hitra.no","hjartdal.no","hjelmeland.no","hobol.no","hobøl.no","hof.no","hol.no","hole.no","holmestrand.no","holtalen.no","holtålen.no","hornindal.no","horten.no","hurdal.no","hurum.no","hvaler.no","hyllestad.no","hagebostad.no","hægebostad.no","hoyanger.no","høyanger.no","hoylandet.no","høylandet.no","ha.no","hå.no","ibestad.no","inderoy.no","inderøy.no","iveland.no","jevnaker.no","jondal.no","jolster.no","jølster.no","karasjok.no","karasjohka.no","kárášjohka.no","karlsoy.no","galsa.no","gálsá.no","karmoy.no","karmøy.no","kautokeino.no","guovdageaidnu.no","klepp.no","klabu.no","klæbu.no","kongsberg.no","kongsvinger.no","kragero.no","kragerø.no","kristiansand.no","kristiansund.no","krodsherad.no","krødsherad.no","kvalsund.no","rahkkeravju.no","ráhkkerávju.no","kvam.no","kvinesdal.no","kvinnherad.no","kviteseid.no","kvitsoy.no","kvitsøy.no","kvafjord.no","kvæfjord.no","giehtavuoatna.no","kvanangen.no","kvænangen.no","navuotna.no","návuotna.no","kafjord.no","kåfjord.no","gaivuotna.no","gáivuotna.no","larvik.no","lavangen.no","lavagis.no","loabat.no","loabát.no","lebesby.no","davvesiida.no","leikanger.no","leirfjord.no","leka.no","leksvik.no","lenvik.no","leangaviika.no","leaŋgaviika.no","lesja.no","levanger.no","lier.no","lierne.no","lillehammer.no","lillesand.no","lindesnes.no","lindas.no","lindås.no","lom.no","loppa.no","lahppi.no","láhppi.no","lund.no","lunner.no","luroy.no","lurøy.no","luster.no","lyngdal.no","lyngen.no","ivgu.no","lardal.no","lerdal.no","lærdal.no","lodingen.no","lødingen.no","lorenskog.no","lørenskog.no","loten.no","løten.no","malvik.no","masoy.no","måsøy.no","muosat.no","muosát.no","mandal.no","marker.no","marnardal.no","masfjorden.no","meland.no","meldal.no","melhus.no","meloy.no","meløy.no","meraker.no","meråker.no","moareke.no","moåreke.no","midsund.no","midtre-gauldal.no","modalen.no","modum.no","molde.no","moskenes.no","moss.no","mosvik.no","malselv.no","målselv.no","malatvuopmi.no","málatvuopmi.no","namdalseid.no","aejrie.no","namsos.no","namsskogan.no","naamesjevuemie.no","nååmesjevuemie.no","laakesvuemie.no","nannestad.no","narvik.no","narviika.no","naustdal.no","nedre-eiker.no","nes.akershus.no","nes.buskerud.no","nesna.no","nesodden.no","nesseby.no","unjarga.no","unjárga.no","nesset.no","nissedal.no","nittedal.no","nord-aurdal.no","nord-fron.no","nord-odal.no","norddal.no","nordkapp.no","davvenjarga.no","davvenjárga.no","nordre-land.no","nordreisa.no","raisa.no","ráisa.no","nore-og-uvdal.no","notodden.no","naroy.no","nærøy.no","notteroy.no","nøtterøy.no","odda.no","oksnes.no","øksnes.no","oppdal.no","oppegard.no","oppegård.no","orkdal.no","orland.no","ørland.no","orskog.no","ørskog.no","orsta.no","ørsta.no","os.hedmark.no","os.hordaland.no","osen.no","osteroy.no","osterøy.no","ostre-toten.no","østre-toten.no","overhalla.no","ovre-eiker.no","øvre-eiker.no","oyer.no","øyer.no","oygarden.no","øygarden.no","oystre-slidre.no","øystre-slidre.no","porsanger.no","porsangu.no","porsáŋgu.no","porsgrunn.no","radoy.no","radøy.no","rakkestad.no","rana.no","ruovat.no","randaberg.no","rauma.no","rendalen.no","rennebu.no","rennesoy.no","rennesøy.no","rindal.no","ringebu.no","ringerike.no","ringsaker.no","rissa.no","risor.no","risør.no","roan.no","rollag.no","rygge.no","ralingen.no","rælingen.no","rodoy.no","rødøy.no","romskog.no","rømskog.no","roros.no","røros.no","rost.no","røst.no","royken.no","røyken.no","royrvik.no","røyrvik.no","rade.no","råde.no","salangen.no","siellak.no","saltdal.no","salat.no","sálát.no","sálat.no","samnanger.no","sande.more-og-romsdal.no","sande.møre-og-romsdal.no","sande.vestfold.no","sandefjord.no","sandnes.no","sandoy.no","sandøy.no","sarpsborg.no","sauda.no","sauherad.no","sel.no","selbu.no","selje.no","seljord.no","sigdal.no","siljan.no","sirdal.no","skaun.no","skedsmo.no","ski.no","skien.no","skiptvet.no","skjervoy.no","skjervøy.no","skierva.no","skiervá.no","skjak.no","skjåk.no","skodje.no","skanland.no","skånland.no","skanit.no","skánit.no","smola.no","smøla.no","snillfjord.no","snasa.no","snåsa.no","snoasa.no","snaase.no","snåase.no","sogndal.no","sokndal.no","sola.no","solund.no","songdalen.no","sortland.no","spydeberg.no","stange.no","stavanger.no","steigen.no","steinkjer.no","stjordal.no","stjørdal.no","stokke.no","stor-elvdal.no","stord.no","stordal.no","storfjord.no","omasvuotna.no","strand.no","stranda.no","stryn.no","sula.no","suldal.no","sund.no","sunndal.no","surnadal.no","sveio.no","svelvik.no","sykkylven.no","sogne.no","søgne.no","somna.no","sømna.no","sondre-land.no","søndre-land.no","sor-aurdal.no","sør-aurdal.no","sor-fron.no","sør-fron.no","sor-odal.no","sør-odal.no","sor-varanger.no","sør-varanger.no","matta-varjjat.no","mátta-várjjat.no","sorfold.no","sørfold.no","sorreisa.no","sørreisa.no","sorum.no","sørum.no","tana.no","deatnu.no","time.no","tingvoll.no","tinn.no","tjeldsund.no","dielddanuorri.no","tjome.no","tjøme.no","tokke.no","tolga.no","torsken.no","tranoy.no","tranøy.no","tromso.no","tromsø.no","tromsa.no","romsa.no","trondheim.no","troandin.no","trysil.no","trana.no","træna.no","trogstad.no","trøgstad.no","tvedestrand.no","tydal.no","tynset.no","tysfjord.no","divtasvuodna.no","divttasvuotna.no","tysnes.no","tysvar.no","tysvær.no","tonsberg.no","tønsberg.no","ullensaker.no","ullensvang.no","ulvik.no","utsira.no","vadso.no","vadsø.no","cahcesuolo.no","čáhcesuolo.no","vaksdal.no","valle.no","vang.no","vanylven.no","vardo.no","vardø.no","varggat.no","várggát.no","vefsn.no","vaapste.no","vega.no","vegarshei.no","vegårshei.no","vennesla.no","verdal.no","verran.no","vestby.no","vestnes.no","vestre-slidre.no","vestre-toten.no","vestvagoy.no","vestvågøy.no","vevelstad.no","vik.no","vikna.no","vindafjord.no","volda.no","voss.no","varoy.no","værøy.no","vagan.no","vågan.no","voagat.no","vagsoy.no","vågsøy.no","vaga.no","vågå.no","valer.ostfold.no","våler.østfold.no","valer.hedmark.no","våler.hedmark.no","*.np","nr","biz.nr","info.nr","gov.nr","edu.nr","org.nr","net.nr","com.nr","nu","nz","ac.nz","co.nz","cri.nz","geek.nz","gen.nz","govt.nz","health.nz","iwi.nz","kiwi.nz","maori.nz","mil.nz","māori.nz","net.nz","org.nz","parliament.nz","school.nz","om","co.om","com.om","edu.om","gov.om","med.om","museum.om","net.om","org.om","pro.om","onion","org","pa","ac.pa","gob.pa","com.pa","org.pa","sld.pa","edu.pa","net.pa","ing.pa","abo.pa","med.pa","nom.pa","pe","edu.pe","gob.pe","nom.pe","mil.pe","org.pe","com.pe","net.pe","pf","com.pf","org.pf","edu.pf","*.pg","ph","com.ph","net.ph","org.ph","gov.ph","edu.ph","ngo.ph","mil.ph","i.ph","pk","com.pk","net.pk","edu.pk","org.pk","fam.pk","biz.pk","web.pk","gov.pk","gob.pk","gok.pk","gon.pk","gop.pk","gos.pk","info.pk","pl","com.pl","net.pl","org.pl","aid.pl","agro.pl","atm.pl","auto.pl","biz.pl","edu.pl","gmina.pl","gsm.pl","info.pl","mail.pl","miasta.pl","media.pl","mil.pl","nieruchomosci.pl","nom.pl","pc.pl","powiat.pl","priv.pl","realestate.pl","rel.pl","sex.pl","shop.pl","sklep.pl","sos.pl","szkola.pl","targi.pl","tm.pl","tourism.pl","travel.pl","turystyka.pl","gov.pl","ap.gov.pl","ic.gov.pl","is.gov.pl","us.gov.pl","kmpsp.gov.pl","kppsp.gov.pl","kwpsp.gov.pl","psp.gov.pl","wskr.gov.pl","kwp.gov.pl","mw.gov.pl","ug.gov.pl","um.gov.pl","umig.gov.pl","ugim.gov.pl","upow.gov.pl","uw.gov.pl","starostwo.gov.pl","pa.gov.pl","po.gov.pl","psse.gov.pl","pup.gov.pl","rzgw.gov.pl","sa.gov.pl","so.gov.pl","sr.gov.pl","wsa.gov.pl","sko.gov.pl","uzs.gov.pl","wiih.gov.pl","winb.gov.pl","pinb.gov.pl","wios.gov.pl","witd.gov.pl","wzmiuw.gov.pl","piw.gov.pl","wiw.gov.pl","griw.gov.pl","wif.gov.pl","oum.gov.pl","sdn.gov.pl","zp.gov.pl","uppo.gov.pl","mup.gov.pl","wuoz.gov.pl","konsulat.gov.pl","oirm.gov.pl","augustow.pl","babia-gora.pl","bedzin.pl","beskidy.pl","bialowieza.pl","bialystok.pl","bielawa.pl","bieszczady.pl","boleslawiec.pl","bydgoszcz.pl","bytom.pl","cieszyn.pl","czeladz.pl","czest.pl","dlugoleka.pl","elblag.pl","elk.pl","glogow.pl","gniezno.pl","gorlice.pl","grajewo.pl","ilawa.pl","jaworzno.pl","jelenia-gora.pl","jgora.pl","kalisz.pl","kazimierz-dolny.pl","karpacz.pl","kartuzy.pl","kaszuby.pl","katowice.pl","kepno.pl","ketrzyn.pl","klodzko.pl","kobierzyce.pl","kolobrzeg.pl","konin.pl","konskowola.pl","kutno.pl","lapy.pl","lebork.pl","legnica.pl","lezajsk.pl","limanowa.pl","lomza.pl","lowicz.pl","lubin.pl","lukow.pl","malbork.pl","malopolska.pl","mazowsze.pl","mazury.pl","mielec.pl","mielno.pl","mragowo.pl","naklo.pl","nowaruda.pl","nysa.pl","olawa.pl","olecko.pl","olkusz.pl","olsztyn.pl","opoczno.pl","opole.pl","ostroda.pl","ostroleka.pl","ostrowiec.pl","ostrowwlkp.pl","pila.pl","pisz.pl","podhale.pl","podlasie.pl","polkowice.pl","pomorze.pl","pomorskie.pl","prochowice.pl","pruszkow.pl","przeworsk.pl","pulawy.pl","radom.pl","rawa-maz.pl","rybnik.pl","rzeszow.pl","sanok.pl","sejny.pl","slask.pl","slupsk.pl","sosnowiec.pl","stalowa-wola.pl","skoczow.pl","starachowice.pl","stargard.pl","suwalki.pl","swidnica.pl","swiebodzin.pl","swinoujscie.pl","szczecin.pl","szczytno.pl","tarnobrzeg.pl","tgory.pl","turek.pl","tychy.pl","ustka.pl","walbrzych.pl","warmia.pl","warszawa.pl","waw.pl","wegrow.pl","wielun.pl","wlocl.pl","wloclawek.pl","wodzislaw.pl","wolomin.pl","wroclaw.pl","zachpomor.pl","zagan.pl","zarow.pl","zgora.pl","zgorzelec.pl","pm","pn","gov.pn","co.pn","org.pn","edu.pn","net.pn","post","pr","com.pr","net.pr","org.pr","gov.pr","edu.pr","isla.pr","pro.pr","biz.pr","info.pr","name.pr","est.pr","prof.pr","ac.pr","pro","aaa.pro","aca.pro","acct.pro","avocat.pro","bar.pro","cpa.pro","eng.pro","jur.pro","law.pro","med.pro","recht.pro","ps","edu.ps","gov.ps","sec.ps","plo.ps","com.ps","org.ps","net.ps","pt","net.pt","gov.pt","org.pt","edu.pt","int.pt","publ.pt","com.pt","nome.pt","pw","co.pw","ne.pw","or.pw","ed.pw","go.pw","belau.pw","py","com.py","coop.py","edu.py","gov.py","mil.py","net.py","org.py","qa","com.qa","edu.qa","gov.qa","mil.qa","name.qa","net.qa","org.qa","sch.qa","re","asso.re","com.re","nom.re","ro","arts.ro","com.ro","firm.ro","info.ro","nom.ro","nt.ro","org.ro","rec.ro","store.ro","tm.ro","www.ro","rs","ac.rs","co.rs","edu.rs","gov.rs","in.rs","org.rs","ru","rw","ac.rw","co.rw","coop.rw","gov.rw","mil.rw","net.rw","org.rw","sa","com.sa","net.sa","org.sa","gov.sa","med.sa","pub.sa","edu.sa","sch.sa","sb","com.sb","edu.sb","gov.sb","net.sb","org.sb","sc","com.sc","gov.sc","net.sc","org.sc","edu.sc","sd","com.sd","net.sd","org.sd","edu.sd","med.sd","tv.sd","gov.sd","info.sd","se","a.se","ac.se","b.se","bd.se","brand.se","c.se","d.se","e.se","f.se","fh.se","fhsk.se","fhv.se","g.se","h.se","i.se","k.se","komforb.se","kommunalforbund.se","komvux.se","l.se","lanbib.se","m.se","n.se","naturbruksgymn.se","o.se","org.se","p.se","parti.se","pp.se","press.se","r.se","s.se","t.se","tm.se","u.se","w.se","x.se","y.se","z.se","sg","com.sg","net.sg","org.sg","gov.sg","edu.sg","per.sg","sh","com.sh","net.sh","gov.sh","org.sh","mil.sh","si","sj","sk","sl","com.sl","net.sl","edu.sl","gov.sl","org.sl","sm","sn","art.sn","com.sn","edu.sn","gouv.sn","org.sn","perso.sn","univ.sn","so","com.so","edu.so","gov.so","me.so","net.so","org.so","sr","ss","biz.ss","com.ss","edu.ss","gov.ss","net.ss","org.ss","st","co.st","com.st","consulado.st","edu.st","embaixada.st","gov.st","mil.st","net.st","org.st","principe.st","saotome.st","store.st","su","sv","com.sv","edu.sv","gob.sv","org.sv","red.sv","sx","gov.sx","sy","edu.sy","gov.sy","net.sy","mil.sy","com.sy","org.sy","sz","co.sz","ac.sz","org.sz","tc","td","tel","tf","tg","th","ac.th","co.th","go.th","in.th","mi.th","net.th","or.th","tj","ac.tj","biz.tj","co.tj","com.tj","edu.tj","go.tj","gov.tj","int.tj","mil.tj","name.tj","net.tj","nic.tj","org.tj","test.tj","web.tj","tk","tl","gov.tl","tm","com.tm","co.tm","org.tm","net.tm","nom.tm","gov.tm","mil.tm","edu.tm","tn","com.tn","ens.tn","fin.tn","gov.tn","ind.tn","intl.tn","nat.tn","net.tn","org.tn","info.tn","perso.tn","tourism.tn","edunet.tn","rnrt.tn","rns.tn","rnu.tn","mincom.tn","agrinet.tn","defense.tn","turen.tn","to","com.to","gov.to","net.to","org.to","edu.to","mil.to","tr","av.tr","bbs.tr","bel.tr","biz.tr","com.tr","dr.tr","edu.tr","gen.tr","gov.tr","info.tr","mil.tr","k12.tr","kep.tr","name.tr","net.tr","org.tr","pol.tr","tel.tr","tsk.tr","tv.tr","web.tr","nc.tr","gov.nc.tr","tt","co.tt","com.tt","org.tt","net.tt","biz.tt","info.tt","pro.tt","int.tt","coop.tt","jobs.tt","mobi.tt","travel.tt","museum.tt","aero.tt","name.tt","gov.tt","edu.tt","tv","tw","edu.tw","gov.tw","mil.tw","com.tw","net.tw","org.tw","idv.tw","game.tw","ebiz.tw","club.tw","網路.tw","組織.tw","商業.tw","tz","ac.tz","co.tz","go.tz","hotel.tz","info.tz","me.tz","mil.tz","mobi.tz","ne.tz","or.tz","sc.tz","tv.tz","ua","com.ua","edu.ua","gov.ua","in.ua","net.ua","org.ua","cherkassy.ua","cherkasy.ua","chernigov.ua","chernihiv.ua","chernivtsi.ua","chernovtsy.ua","ck.ua","cn.ua","cr.ua","crimea.ua","cv.ua","dn.ua","dnepropetrovsk.ua","dnipropetrovsk.ua","dominic.ua","donetsk.ua","dp.ua","if.ua","ivano-frankivsk.ua","kh.ua","kharkiv.ua","kharkov.ua","kherson.ua","khmelnitskiy.ua","khmelnytskyi.ua","kiev.ua","kirovograd.ua","km.ua","kr.ua","krym.ua","ks.ua","kv.ua","kyiv.ua","lg.ua","lt.ua","lugansk.ua","lutsk.ua","lv.ua","lviv.ua","mk.ua","mykolaiv.ua","nikolaev.ua","od.ua","odesa.ua","odessa.ua","pl.ua","poltava.ua","rivne.ua","rovno.ua","rv.ua","sb.ua","sebastopol.ua","sevastopol.ua","sm.ua","sumy.ua","te.ua","ternopil.ua","uz.ua","uzhgorod.ua","vinnica.ua","vinnytsia.ua","vn.ua","volyn.ua","yalta.ua","zaporizhzhe.ua","zaporizhzhia.ua","zhitomir.ua","zhytomyr.ua","zp.ua","zt.ua","ug","co.ug","or.ug","ac.ug","sc.ug","go.ug","ne.ug","com.ug","org.ug","uk","ac.uk","co.uk","gov.uk","ltd.uk","me.uk","net.uk","nhs.uk","org.uk","plc.uk","police.uk","*.sch.uk","us","dni.us","fed.us","isa.us","kids.us","nsn.us","ak.us","al.us","ar.us","as.us","az.us","ca.us","co.us","ct.us","dc.us","de.us","fl.us","ga.us","gu.us","hi.us","ia.us","id.us","il.us","in.us","ks.us","ky.us","la.us","ma.us","md.us","me.us","mi.us","mn.us","mo.us","ms.us","mt.us","nc.us","nd.us","ne.us","nh.us","nj.us","nm.us","nv.us","ny.us","oh.us","ok.us","or.us","pa.us","pr.us","ri.us","sc.us","sd.us","tn.us","tx.us","ut.us","vi.us","vt.us","va.us","wa.us","wi.us","wv.us","wy.us","k12.ak.us","k12.al.us","k12.ar.us","k12.as.us","k12.az.us","k12.ca.us","k12.co.us","k12.ct.us","k12.dc.us","k12.de.us","k12.fl.us","k12.ga.us","k12.gu.us","k12.ia.us","k12.id.us","k12.il.us","k12.in.us","k12.ks.us","k12.ky.us","k12.la.us","k12.ma.us","k12.md.us","k12.me.us","k12.mi.us","k12.mn.us","k12.mo.us","k12.ms.us","k12.mt.us","k12.nc.us","k12.ne.us","k12.nh.us","k12.nj.us","k12.nm.us","k12.nv.us","k12.ny.us","k12.oh.us","k12.ok.us","k12.or.us","k12.pa.us","k12.pr.us","k12.ri.us","k12.sc.us","k12.tn.us","k12.tx.us","k12.ut.us","k12.vi.us","k12.vt.us","k12.va.us","k12.wa.us","k12.wi.us","k12.wy.us","cc.ak.us","cc.al.us","cc.ar.us","cc.as.us","cc.az.us","cc.ca.us","cc.co.us","cc.ct.us","cc.dc.us","cc.de.us","cc.fl.us","cc.ga.us","cc.gu.us","cc.hi.us","cc.ia.us","cc.id.us","cc.il.us","cc.in.us","cc.ks.us","cc.ky.us","cc.la.us","cc.ma.us","cc.md.us","cc.me.us","cc.mi.us","cc.mn.us","cc.mo.us","cc.ms.us","cc.mt.us","cc.nc.us","cc.nd.us","cc.ne.us","cc.nh.us","cc.nj.us","cc.nm.us","cc.nv.us","cc.ny.us","cc.oh.us","cc.ok.us","cc.or.us","cc.pa.us","cc.pr.us","cc.ri.us","cc.sc.us","cc.sd.us","cc.tn.us","cc.tx.us","cc.ut.us","cc.vi.us","cc.vt.us","cc.va.us","cc.wa.us","cc.wi.us","cc.wv.us","cc.wy.us","lib.ak.us","lib.al.us","lib.ar.us","lib.as.us","lib.az.us","lib.ca.us","lib.co.us","lib.ct.us","lib.dc.us","lib.fl.us","lib.ga.us","lib.gu.us","lib.hi.us","lib.ia.us","lib.id.us","lib.il.us","lib.in.us","lib.ks.us","lib.ky.us","lib.la.us","lib.ma.us","lib.md.us","lib.me.us","lib.mi.us","lib.mn.us","lib.mo.us","lib.ms.us","lib.mt.us","lib.nc.us","lib.nd.us","lib.ne.us","lib.nh.us","lib.nj.us","lib.nm.us","lib.nv.us","lib.ny.us","lib.oh.us","lib.ok.us","lib.or.us","lib.pa.us","lib.pr.us","lib.ri.us","lib.sc.us","lib.sd.us","lib.tn.us","lib.tx.us","lib.ut.us","lib.vi.us","lib.vt.us","lib.va.us","lib.wa.us","lib.wi.us","lib.wy.us","pvt.k12.ma.us","chtr.k12.ma.us","paroch.k12.ma.us","ann-arbor.mi.us","cog.mi.us","dst.mi.us","eaton.mi.us","gen.mi.us","mus.mi.us","tec.mi.us","washtenaw.mi.us","uy","com.uy","edu.uy","gub.uy","mil.uy","net.uy","org.uy","uz","co.uz","com.uz","net.uz","org.uz","va","vc","com.vc","net.vc","org.vc","gov.vc","mil.vc","edu.vc","ve","arts.ve","co.ve","com.ve","e12.ve","edu.ve","firm.ve","gob.ve","gov.ve","info.ve","int.ve","mil.ve","net.ve","org.ve","rec.ve","store.ve","tec.ve","web.ve","vg","vi","co.vi","com.vi","k12.vi","net.vi","org.vi","vn","com.vn","net.vn","org.vn","edu.vn","gov.vn","int.vn","ac.vn","biz.vn","info.vn","name.vn","pro.vn","health.vn","vu","com.vu","edu.vu","net.vu","org.vu","wf","ws","com.ws","net.ws","org.ws","gov.ws","edu.ws","yt","امارات","հայ","বাংলা","бг","бел","中国","中國","الجزائر","مصر","ею","ευ","موريتانيا","გე","ελ","香港","公司.香港","教育.香港","政府.香港","個人.香港","網絡.香港","組織.香港","ಭಾರತ","ଭାରତ","ভাৰত","भारतम्","भारोत","ڀارت","ഭാരതം","भारत","بارت","بھارت","భారత్","ભારત","ਭਾਰਤ","ভারত","இந்தியா","ایران","ايران","عراق","الاردن","한국","қаз","ලංකා","இலங்கை","المغرب","мкд","мон","澳門","澳门","مليسيا","عمان","پاکستان","پاكستان","فلسطين","срб","пр.срб","орг.срб","обр.срб","од.срб","упр.срб","ак.срб","рф","قطر","السعودية","السعودیة","السعودیۃ","السعوديه","سودان","新加坡","சிங்கப்பூர்","سورية","سوريا","ไทย","ศึกษา.ไทย","ธุรกิจ.ไทย","รัฐบาล.ไทย","ทหาร.ไทย","เน็ต.ไทย","องค์กร.ไทย","تونس","台灣","台湾","臺灣","укр","اليمن","xxx","*.ye","ac.za","agric.za","alt.za","co.za","edu.za","gov.za","grondar.za","law.za","mil.za","net.za","ngo.za","nic.za","nis.za","nom.za","org.za","school.za","tm.za","web.za","zm","ac.zm","biz.zm","co.zm","com.zm","edu.zm","gov.zm","info.zm","mil.zm","net.zm","org.zm","sch.zm","zw","ac.zw","co.zw","gov.zw","mil.zw","org.zw","aaa","aarp","abarth","abb","abbott","abbvie","abc","able","abogado","abudhabi","academy","accenture","accountant","accountants","aco","actor","adac","ads","adult","aeg","aetna","afamilycompany","afl","africa","agakhan","agency","aig","aigo","airbus","airforce","airtel","akdn","alfaromeo","alibaba","alipay","allfinanz","allstate","ally","alsace","alstom","amazon","americanexpress","americanfamily","amex","amfam","amica","amsterdam","analytics","android","anquan","anz","aol","apartments","app","apple","aquarelle","arab","aramco","archi","army","art","arte","asda","associates","athleta","attorney","auction","audi","audible","audio","auspost","author","auto","autos","avianca","aws","axa","azure","baby","baidu","banamex","bananarepublic","band","bank","bar","barcelona","barclaycard","barclays","barefoot","bargains","baseball","basketball","bauhaus","bayern","bbc","bbt","bbva","bcg","bcn","beats","beauty","beer","bentley","berlin","best","bestbuy","bet","bharti","bible","bid","bike","bing","bingo","bio","black","blackfriday","blockbuster","blog","bloomberg","blue","bms","bmw","bnpparibas","boats","boehringer","bofa","bom","bond","boo","book","booking","bosch","bostik","boston","bot","boutique","box","bradesco","bridgestone","broadway","broker","brother","brussels","budapest","bugatti","build","builders","business","buy","buzz","bzh","cab","cafe","cal","call","calvinklein","cam","camera","camp","cancerresearch","canon","capetown","capital","capitalone","car","caravan","cards","care","career","careers","cars","casa","case","caseih","cash","casino","catering","catholic","cba","cbn","cbre","cbs","ceb","center","ceo","cern","cfa","cfd","chanel","channel","charity","chase","chat","cheap","chintai","christmas","chrome","church","cipriani","circle","cisco","citadel","citi","citic","city","cityeats","claims","cleaning","click","clinic","clinique","clothing","cloud","club","clubmed","coach","codes","coffee","college","cologne","comcast","commbank","community","company","compare","computer","comsec","condos","construction","consulting","contact","contractors","cooking","cookingchannel","cool","corsica","country","coupon","coupons","courses","cpa","credit","creditcard","creditunion","cricket","crown","crs","cruise","cruises","csc","cuisinella","cymru","cyou","dabur","dad","dance","data","date","dating","datsun","day","dclk","dds","deal","dealer","deals","degree","delivery","dell","deloitte","delta","democrat","dental","dentist","desi","design","dev","dhl","diamonds","diet","digital","direct","directory","discount","discover","dish","diy","dnp","docs","doctor","dog","domains","dot","download","drive","dtv","dubai","duck","dunlop","dupont","durban","dvag","dvr","earth","eat","eco","edeka","education","email","emerck","energy","engineer","engineering","enterprises","epson","equipment","ericsson","erni","esq","estate","esurance","etisalat","eurovision","eus","events","exchange","expert","exposed","express","extraspace","fage","fail","fairwinds","faith","family","fan","fans","farm","farmers","fashion","fast","fedex","feedback","ferrari","ferrero","fiat","fidelity","fido","film","final","finance","financial","fire","firestone","firmdale","fish","fishing","fit","fitness","flickr","flights","flir","florist","flowers","fly","foo","food","foodnetwork","football","ford","forex","forsale","forum","foundation","fox","free","fresenius","frl","frogans","frontdoor","frontier","ftr","fujitsu","fujixerox","fun","fund","furniture","futbol","fyi","gal","gallery","gallo","gallup","game","games","gap","garden","gay","gbiz","gdn","gea","gent","genting","george","ggee","gift","gifts","gives","giving","glade","glass","gle","global","globo","gmail","gmbh","gmo","gmx","godaddy","gold","goldpoint","golf","goo","goodyear","goog","google","gop","got","grainger","graphics","gratis","green","gripe","grocery","group","guardian","gucci","guge","guide","guitars","guru","hair","hamburg","hangout","haus","hbo","hdfc","hdfcbank","health","healthcare","help","helsinki","here","hermes","hgtv","hiphop","hisamitsu","hitachi","hiv","hkt","hockey","holdings","holiday","homedepot","homegoods","homes","homesense","honda","horse","hospital","host","hosting","hot","hoteles","hotels","hotmail","house","how","hsbc","hughes","hyatt","hyundai","ibm","icbc","ice","icu","ieee","ifm","ikano","imamat","imdb","immo","immobilien","inc","industries","infiniti","ing","ink","institute","insurance","insure","intel","international","intuit","investments","ipiranga","irish","ismaili","ist","istanbul","itau","itv","iveco","jaguar","java","jcb","jcp","jeep","jetzt","jewelry","jio","jll","jmp","jnj","joburg","jot","joy","jpmorgan","jprs","juegos","juniper","kaufen","kddi","kerryhotels","kerrylogistics","kerryproperties","kfh","kia","kim","kinder","kindle","kitchen","kiwi","koeln","komatsu","kosher","kpmg","kpn","krd","kred","kuokgroup","kyoto","lacaixa","lamborghini","lamer","lancaster","lancia","land","landrover","lanxess","lasalle","lat","latino","latrobe","law","lawyer","lds","lease","leclerc","lefrak","legal","lego","lexus","lgbt","lidl","life","lifeinsurance","lifestyle","lighting","like","lilly","limited","limo","lincoln","linde","link","lipsy","live","living","lixil","llc","llp","loan","loans","locker","locus","loft","lol","london","lotte","lotto","love","lpl","lplfinancial","ltd","ltda","lundbeck","lupin","luxe","luxury","macys","madrid","maif","maison","makeup","man","management","mango","map","market","marketing","markets","marriott","marshalls","maserati","mattel","mba","mckinsey","med","media","meet","melbourne","meme","memorial","men","menu","merckmsd","metlife","miami","microsoft","mini","mint","mit","mitsubishi","mlb","mls","mma","mobile","moda","moe","moi","mom","monash","money","monster","mormon","mortgage","moscow","moto","motorcycles","mov","movie","msd","mtn","mtr","mutual","nab","nadex","nagoya","nationwide","natura","navy","nba","nec","netbank","netflix","network","neustar","new","newholland","news","next","nextdirect","nexus","nfl","ngo","nhk","nico","nike","nikon","ninja","nissan","nissay","nokia","northwesternmutual","norton","now","nowruz","nowtv","nra","nrw","ntt","nyc","obi","observer","off","office","okinawa","olayan","olayangroup","oldnavy","ollo","omega","one","ong","onl","online","onyourside","ooo","open","oracle","orange","organic","origins","osaka","otsuka","ott","ovh","page","panasonic","paris","pars","partners","parts","party","passagens","pay","pccw","pet","pfizer","pharmacy","phd","philips","phone","photo","photography","photos","physio","pics","pictet","pictures","pid","pin","ping","pink","pioneer","pizza","place","play","playstation","plumbing","plus","pnc","pohl","poker","politie","porn","pramerica","praxi","press","prime","prod","productions","prof","progressive","promo","properties","property","protection","pru","prudential","pub","pwc","qpon","quebec","quest","qvc","racing","radio","raid","read","realestate","realtor","realty","recipes","red","redstone","redumbrella","rehab","reise","reisen","reit","reliance","ren","rent","rentals","repair","report","republican","rest","restaurant","review","reviews","rexroth","rich","richardli","ricoh","rightathome","ril","rio","rip","rmit","rocher","rocks","rodeo","rogers","room","rsvp","rugby","ruhr","run","rwe","ryukyu","saarland","safe","safety","sakura","sale","salon","samsclub","samsung","sandvik","sandvikcoromant","sanofi","sap","sarl","sas","save","saxo","sbi","sbs","sca","scb","schaeffler","schmidt","scholarships","school","schule","schwarz","science","scjohnson","scor","scot","search","seat","secure","security","seek","select","sener","services","ses","seven","sew","sex","sexy","sfr","shangrila","sharp","shaw","shell","shia","shiksha","shoes","shop","shopping","shouji","show","showtime","shriram","silk","sina","singles","site","ski","skin","sky","skype","sling","smart","smile","sncf","soccer","social","softbank","software","sohu","solar","solutions","song","sony","soy","spa","space","sport","spot","spreadbetting","srl","stada","staples","star","statebank","statefarm","stc","stcgroup","stockholm","storage","store","stream","studio","study","style","sucks","supplies","supply","support","surf","surgery","suzuki","swatch","swiftcover","swiss","sydney","symantec","systems","tab","taipei","talk","taobao","target","tatamotors","tatar","tattoo","tax","taxi","tci","tdk","team","tech","technology","temasek","tennis","teva","thd","theater","theatre","tiaa","tickets","tienda","tiffany","tips","tires","tirol","tjmaxx","tjx","tkmaxx","tmall","today","tokyo","tools","top","toray","toshiba","total","tours","town","toyota","toys","trade","trading","training","travel","travelchannel","travelers","travelersinsurance","trust","trv","tube","tui","tunes","tushu","tvs","ubank","ubs","unicom","university","uno","uol","ups","vacations","vana","vanguard","vegas","ventures","verisign","versicherung","vet","viajes","video","vig","viking","villas","vin","vip","virgin","visa","vision","viva","vivo","vlaanderen","vodka","volkswagen","volvo","vote","voting","voto","voyage","vuelos","wales","walmart","walter","wang","wanggou","watch","watches","weather","weatherchannel","webcam","weber","website","wed","wedding","weibo","weir","whoswho","wien","wiki","williamhill","win","windows","wine","winners","wme","wolterskluwer","woodside","work","works","world","wow","wtc","wtf","xbox","xerox","xfinity","xihuan","xin","कॉम","セール","佛山","慈善","集团","在线","大众汽车","点看","คอม","八卦","موقع","公益","公司","香格里拉","网站","移动","我爱你","москва","католик","онлайн","сайт","联通","קום","时尚","微博","淡马锡","ファッション","орг","नेट","ストア","アマゾン","삼성","商标","商店","商城","дети","ポイント","新闻","工行","家電","كوم","中文网","中信","娱乐","谷歌","電訊盈科","购物","クラウド","通販","网店","संगठन","餐厅","网络","ком","亚马逊","诺基亚","食品","飞利浦","手表","手机","ارامكو","العليان","اتصالات","بازار","ابوظبي","كاثوليك","همراه","닷컴","政府","شبكة","بيتك","عرب","机构","组织机构","健康","招聘","рус","珠宝","大拿","みんな","グーグル","世界","書籍","网址","닷넷","コム","天主教","游戏","vermögensberater","vermögensberatung","企业","信息","嘉里大酒店","嘉里","广东","政务","xyz","yachts","yahoo","yamaxun","yandex","yodobashi","yoga","yokohama","you","youtube","yun","zappos","zara","zero","zip","zone","zuerich","cc.ua","inf.ua","ltd.ua","adobeaemcloud.com","adobeaemcloud.net","*.dev.adobeaemcloud.com","beep.pl","barsy.ca","*.compute.estate","*.alces.network","altervista.org","alwaysdata.net","cloudfront.net","*.compute.amazonaws.com","*.compute-1.amazonaws.com","*.compute.amazonaws.com.cn","us-east-1.amazonaws.com","cn-north-1.eb.amazonaws.com.cn","cn-northwest-1.eb.amazonaws.com.cn","elasticbeanstalk.com","ap-northeast-1.elasticbeanstalk.com","ap-northeast-2.elasticbeanstalk.com","ap-northeast-3.elasticbeanstalk.com","ap-south-1.elasticbeanstalk.com","ap-southeast-1.elasticbeanstalk.com","ap-southeast-2.elasticbeanstalk.com","ca-central-1.elasticbeanstalk.com","eu-central-1.elasticbeanstalk.com","eu-west-1.elasticbeanstalk.com","eu-west-2.elasticbeanstalk.com","eu-west-3.elasticbeanstalk.com","sa-east-1.elasticbeanstalk.com","us-east-1.elasticbeanstalk.com","us-east-2.elasticbeanstalk.com","us-gov-west-1.elasticbeanstalk.com","us-west-1.elasticbeanstalk.com","us-west-2.elasticbeanstalk.com","*.elb.amazonaws.com","*.elb.amazonaws.com.cn","s3.amazonaws.com","s3-ap-northeast-1.amazonaws.com","s3-ap-northeast-2.amazonaws.com","s3-ap-south-1.amazonaws.com","s3-ap-southeast-1.amazonaws.com","s3-ap-southeast-2.amazonaws.com","s3-ca-central-1.amazonaws.com","s3-eu-central-1.amazonaws.com","s3-eu-west-1.amazonaws.com","s3-eu-west-2.amazonaws.com","s3-eu-west-3.amazonaws.com","s3-external-1.amazonaws.com","s3-fips-us-gov-west-1.amazonaws.com","s3-sa-east-1.amazonaws.com","s3-us-gov-west-1.amazonaws.com","s3-us-east-2.amazonaws.com","s3-us-west-1.amazonaws.com","s3-us-west-2.amazonaws.com","s3.ap-northeast-2.amazonaws.com","s3.ap-south-1.amazonaws.com","s3.cn-north-1.amazonaws.com.cn","s3.ca-central-1.amazonaws.com","s3.eu-central-1.amazonaws.com","s3.eu-west-2.amazonaws.com","s3.eu-west-3.amazonaws.com","s3.us-east-2.amazonaws.com","s3.dualstack.ap-northeast-1.amazonaws.com","s3.dualstack.ap-northeast-2.amazonaws.com","s3.dualstack.ap-south-1.amazonaws.com","s3.dualstack.ap-southeast-1.amazonaws.com","s3.dualstack.ap-southeast-2.amazonaws.com","s3.dualstack.ca-central-1.amazonaws.com","s3.dualstack.eu-central-1.amazonaws.com","s3.dualstack.eu-west-1.amazonaws.com","s3.dualstack.eu-west-2.amazonaws.com","s3.dualstack.eu-west-3.amazonaws.com","s3.dualstack.sa-east-1.amazonaws.com","s3.dualstack.us-east-1.amazonaws.com","s3.dualstack.us-east-2.amazonaws.com","s3-website-us-east-1.amazonaws.com","s3-website-us-west-1.amazonaws.com","s3-website-us-west-2.amazonaws.com","s3-website-ap-northeast-1.amazonaws.com","s3-website-ap-southeast-1.amazonaws.com","s3-website-ap-southeast-2.amazonaws.com","s3-website-eu-west-1.amazonaws.com","s3-website-sa-east-1.amazonaws.com","s3-website.ap-northeast-2.amazonaws.com","s3-website.ap-south-1.amazonaws.com","s3-website.ca-central-1.amazonaws.com","s3-website.eu-central-1.amazonaws.com","s3-website.eu-west-2.amazonaws.com","s3-website.eu-west-3.amazonaws.com","s3-website.us-east-2.amazonaws.com","amsw.nl","t3l3p0rt.net","tele.amune.org","apigee.io","on-aptible.com","user.aseinet.ne.jp","gv.vc","d.gv.vc","user.party.eus","pimienta.org","poivron.org","potager.org","sweetpepper.org","myasustor.com","myfritz.net","*.awdev.ca","*.advisor.ws","b-data.io","backplaneapp.io","balena-devices.com","app.banzaicloud.io","betainabox.com","bnr.la","blackbaudcdn.net","boomla.net","boxfuse.io","square7.ch","bplaced.com","bplaced.de","square7.de","bplaced.net","square7.net","browsersafetymark.io","uk0.bigv.io","dh.bytemark.co.uk","vm.bytemark.co.uk","mycd.eu","carrd.co","crd.co","uwu.ai","ae.org","ar.com","br.com","cn.com","com.de","com.se","de.com","eu.com","gb.com","gb.net","hu.com","hu.net","jp.net","jpn.com","kr.com","mex.com","no.com","qc.com","ru.com","sa.com","se.net","uk.com","uk.net","us.com","uy.com","za.bz","za.com","africa.com","gr.com","in.net","us.org","co.com","c.la","certmgr.org","xenapponazure.com","discourse.group","discourse.team","virtueeldomein.nl","cleverapps.io","*.lcl.dev","*.stg.dev","c66.me","cloud66.ws","cloud66.zone","jdevcloud.com","wpdevcloud.com","cloudaccess.host","freesite.host","cloudaccess.net","cloudcontrolled.com","cloudcontrolapp.com","cloudera.site","trycloudflare.com","workers.dev","wnext.app","co.ca","*.otap.co","co.cz","c.cdn77.org","cdn77-ssl.net","r.cdn77.net","rsc.cdn77.org","ssl.origin.cdn77-secure.org","cloudns.asia","cloudns.biz","cloudns.club","cloudns.cc","cloudns.eu","cloudns.in","cloudns.info","cloudns.org","cloudns.pro","cloudns.pw","cloudns.us","cloudeity.net","cnpy.gdn","co.nl","co.no","webhosting.be","hosting-cluster.nl","ac.ru","edu.ru","gov.ru","int.ru","mil.ru","test.ru","dyn.cosidns.de","dynamisches-dns.de","dnsupdater.de","internet-dns.de","l-o-g-i-n.de","dynamic-dns.info","feste-ip.net","knx-server.net","static-access.net","realm.cz","*.cryptonomic.net","cupcake.is","*.customer-oci.com","*.oci.customer-oci.com","*.ocp.customer-oci.com","*.ocs.customer-oci.com","cyon.link","cyon.site","daplie.me","localhost.daplie.me","dattolocal.com","dattorelay.com","dattoweb.com","mydatto.com","dattolocal.net","mydatto.net","biz.dk","co.dk","firm.dk","reg.dk","store.dk","*.dapps.earth","*.bzz.dapps.earth","builtwithdark.com","edgestack.me","debian.net","dedyn.io","dnshome.de","online.th","shop.th","drayddns.com","dreamhosters.com","mydrobo.com","drud.io","drud.us","duckdns.org","dy.fi","tunk.org","dyndns-at-home.com","dyndns-at-work.com","dyndns-blog.com","dyndns-free.com","dyndns-home.com","dyndns-ip.com","dyndns-mail.com","dyndns-office.com","dyndns-pics.com","dyndns-remote.com","dyndns-server.com","dyndns-web.com","dyndns-wiki.com","dyndns-work.com","dyndns.biz","dyndns.info","dyndns.org","dyndns.tv","at-band-camp.net","ath.cx","barrel-of-knowledge.info","barrell-of-knowledge.info","better-than.tv","blogdns.com","blogdns.net","blogdns.org","blogsite.org","boldlygoingnowhere.org","broke-it.net","buyshouses.net","cechire.com","dnsalias.com","dnsalias.net","dnsalias.org","dnsdojo.com","dnsdojo.net","dnsdojo.org","does-it.net","doesntexist.com","doesntexist.org","dontexist.com","dontexist.net","dontexist.org","doomdns.com","doomdns.org","dvrdns.org","dyn-o-saur.com","dynalias.com","dynalias.net","dynalias.org","dynathome.net","dyndns.ws","endofinternet.net","endofinternet.org","endoftheinternet.org","est-a-la-maison.com","est-a-la-masion.com","est-le-patron.com","est-mon-blogueur.com","for-better.biz","for-more.biz","for-our.info","for-some.biz","for-the.biz","forgot.her.name","forgot.his.name","from-ak.com","from-al.com","from-ar.com","from-az.net","from-ca.com","from-co.net","from-ct.com","from-dc.com","from-de.com","from-fl.com","from-ga.com","from-hi.com","from-ia.com","from-id.com","from-il.com","from-in.com","from-ks.com","from-ky.com","from-la.net","from-ma.com","from-md.com","from-me.org","from-mi.com","from-mn.com","from-mo.com","from-ms.com","from-mt.com","from-nc.com","from-nd.com","from-ne.com","from-nh.com","from-nj.com","from-nm.com","from-nv.com","from-ny.net","from-oh.com","from-ok.com","from-or.com","from-pa.com","from-pr.com","from-ri.com","from-sc.com","from-sd.com","from-tn.com","from-tx.com","from-ut.com","from-va.com","from-vt.com","from-wa.com","from-wi.com","from-wv.com","from-wy.com","ftpaccess.cc","fuettertdasnetz.de","game-host.org","game-server.cc","getmyip.com","gets-it.net","go.dyndns.org","gotdns.com","gotdns.org","groks-the.info","groks-this.info","ham-radio-op.net","here-for-more.info","hobby-site.com","hobby-site.org","home.dyndns.org","homedns.org","homeftp.net","homeftp.org","homeip.net","homelinux.com","homelinux.net","homelinux.org","homeunix.com","homeunix.net","homeunix.org","iamallama.com","in-the-band.net","is-a-anarchist.com","is-a-blogger.com","is-a-bookkeeper.com","is-a-bruinsfan.org","is-a-bulls-fan.com","is-a-candidate.org","is-a-caterer.com","is-a-celticsfan.org","is-a-chef.com","is-a-chef.net","is-a-chef.org","is-a-conservative.com","is-a-cpa.com","is-a-cubicle-slave.com","is-a-democrat.com","is-a-designer.com","is-a-doctor.com","is-a-financialadvisor.com","is-a-geek.com","is-a-geek.net","is-a-geek.org","is-a-green.com","is-a-guru.com","is-a-hard-worker.com","is-a-hunter.com","is-a-knight.org","is-a-landscaper.com","is-a-lawyer.com","is-a-liberal.com","is-a-libertarian.com","is-a-linux-user.org","is-a-llama.com","is-a-musician.com","is-a-nascarfan.com","is-a-nurse.com","is-a-painter.com","is-a-patsfan.org","is-a-personaltrainer.com","is-a-photographer.com","is-a-player.com","is-a-republican.com","is-a-rockstar.com","is-a-socialist.com","is-a-soxfan.org","is-a-student.com","is-a-teacher.com","is-a-techie.com","is-a-therapist.com","is-an-accountant.com","is-an-actor.com","is-an-actress.com","is-an-anarchist.com","is-an-artist.com","is-an-engineer.com","is-an-entertainer.com","is-by.us","is-certified.com","is-found.org","is-gone.com","is-into-anime.com","is-into-cars.com","is-into-cartoons.com","is-into-games.com","is-leet.com","is-lost.org","is-not-certified.com","is-saved.org","is-slick.com","is-uberleet.com","is-very-bad.org","is-very-evil.org","is-very-good.org","is-very-nice.org","is-very-sweet.org","is-with-theband.com","isa-geek.com","isa-geek.net","isa-geek.org","isa-hockeynut.com","issmarterthanyou.com","isteingeek.de","istmein.de","kicks-ass.net","kicks-ass.org","knowsitall.info","land-4-sale.us","lebtimnetz.de","leitungsen.de","likes-pie.com","likescandy.com","merseine.nu","mine.nu","misconfused.org","mypets.ws","myphotos.cc","neat-url.com","office-on-the.net","on-the-web.tv","podzone.net","podzone.org","readmyblog.org","saves-the-whales.com","scrapper-site.net","scrapping.cc","selfip.biz","selfip.com","selfip.info","selfip.net","selfip.org","sells-for-less.com","sells-for-u.com","sells-it.net","sellsyourhome.org","servebbs.com","servebbs.net","servebbs.org","serveftp.net","serveftp.org","servegame.org","shacknet.nu","simple-url.com","space-to-rent.com","stuff-4-sale.org","stuff-4-sale.us","teaches-yoga.com","thruhere.net","traeumtgerade.de","webhop.biz","webhop.info","webhop.net","webhop.org","worse-than.tv","writesthisblog.com","ddnss.de","dyn.ddnss.de","dyndns.ddnss.de","dyndns1.de","dyn-ip24.de","home-webserver.de","dyn.home-webserver.de","myhome-server.de","ddnss.org","definima.net","definima.io","bci.dnstrace.pro","ddnsfree.com","ddnsgeek.com","giize.com","gleeze.com","kozow.com","loseyourip.com","ooguy.com","theworkpc.com","casacam.net","dynu.net","accesscam.org","camdvr.org","freeddns.org","mywire.org","webredirect.org","myddns.rocks","blogsite.xyz","dynv6.net","e4.cz","en-root.fr","mytuleap.com","onred.one","staging.onred.one","enonic.io","customer.enonic.io","eu.org","al.eu.org","asso.eu.org","at.eu.org","au.eu.org","be.eu.org","bg.eu.org","ca.eu.org","cd.eu.org","ch.eu.org","cn.eu.org","cy.eu.org","cz.eu.org","de.eu.org","dk.eu.org","edu.eu.org","ee.eu.org","es.eu.org","fi.eu.org","fr.eu.org","gr.eu.org","hr.eu.org","hu.eu.org","ie.eu.org","il.eu.org","in.eu.org","int.eu.org","is.eu.org","it.eu.org","jp.eu.org","kr.eu.org","lt.eu.org","lu.eu.org","lv.eu.org","mc.eu.org","me.eu.org","mk.eu.org","mt.eu.org","my.eu.org","net.eu.org","ng.eu.org","nl.eu.org","no.eu.org","nz.eu.org","paris.eu.org","pl.eu.org","pt.eu.org","q-a.eu.org","ro.eu.org","ru.eu.org","se.eu.org","si.eu.org","sk.eu.org","tr.eu.org","uk.eu.org","us.eu.org","eu-1.evennode.com","eu-2.evennode.com","eu-3.evennode.com","eu-4.evennode.com","us-1.evennode.com","us-2.evennode.com","us-3.evennode.com","us-4.evennode.com","twmail.cc","twmail.net","twmail.org","mymailer.com.tw","url.tw","apps.fbsbx.com","ru.net","adygeya.ru","bashkiria.ru","bir.ru","cbg.ru","com.ru","dagestan.ru","grozny.ru","kalmykia.ru","kustanai.ru","marine.ru","mordovia.ru","msk.ru","mytis.ru","nalchik.ru","nov.ru","pyatigorsk.ru","spb.ru","vladikavkaz.ru","vladimir.ru","abkhazia.su","adygeya.su","aktyubinsk.su","arkhangelsk.su","armenia.su","ashgabad.su","azerbaijan.su","balashov.su","bashkiria.su","bryansk.su","bukhara.su","chimkent.su","dagestan.su","east-kazakhstan.su","exnet.su","georgia.su","grozny.su","ivanovo.su","jambyl.su","kalmykia.su","kaluga.su","karacol.su","karaganda.su","karelia.su","khakassia.su","krasnodar.su","kurgan.su","kustanai.su","lenug.su","mangyshlak.su","mordovia.su","msk.su","murmansk.su","nalchik.su","navoi.su","north-kazakhstan.su","nov.su","obninsk.su","penza.su","pokrovsk.su","sochi.su","spb.su","tashkent.su","termez.su","togliatti.su","troitsk.su","tselinograd.su","tula.su","tuva.su","vladikavkaz.su","vladimir.su","vologda.su","channelsdvr.net","u.channelsdvr.net","fastly-terrarium.com","fastlylb.net","map.fastlylb.net","freetls.fastly.net","map.fastly.net","a.prod.fastly.net","global.prod.fastly.net","a.ssl.fastly.net","b.ssl.fastly.net","global.ssl.fastly.net","fastpanel.direct","fastvps-server.com","fhapp.xyz","fedorainfracloud.org","fedorapeople.org","cloud.fedoraproject.org","app.os.fedoraproject.org","app.os.stg.fedoraproject.org","mydobiss.com","filegear.me","filegear-au.me","filegear-de.me","filegear-gb.me","filegear-ie.me","filegear-jp.me","filegear-sg.me","firebaseapp.com","flynnhub.com","flynnhosting.net","0e.vc","freebox-os.com","freeboxos.com","fbx-os.fr","fbxos.fr","freebox-os.fr","freeboxos.fr","freedesktop.org","*.futurecms.at","*.ex.futurecms.at","*.in.futurecms.at","futurehosting.at","futuremailing.at","*.ex.ortsinfo.at","*.kunden.ortsinfo.at","*.statics.cloud","service.gov.uk","gehirn.ne.jp","usercontent.jp","gentapps.com","lab.ms","github.io","githubusercontent.com","gitlab.io","glitch.me","lolipop.io","cloudapps.digital","london.cloudapps.digital","homeoffice.gov.uk","ro.im","shop.ro","goip.de","run.app","a.run.app","web.app","*.0emm.com","appspot.com","*.r.appspot.com","blogspot.ae","blogspot.al","blogspot.am","blogspot.ba","blogspot.be","blogspot.bg","blogspot.bj","blogspot.ca","blogspot.cf","blogspot.ch","blogspot.cl","blogspot.co.at","blogspot.co.id","blogspot.co.il","blogspot.co.ke","blogspot.co.nz","blogspot.co.uk","blogspot.co.za","blogspot.com","blogspot.com.ar","blogspot.com.au","blogspot.com.br","blogspot.com.by","blogspot.com.co","blogspot.com.cy","blogspot.com.ee","blogspot.com.eg","blogspot.com.es","blogspot.com.mt","blogspot.com.ng","blogspot.com.tr","blogspot.com.uy","blogspot.cv","blogspot.cz","blogspot.de","blogspot.dk","blogspot.fi","blogspot.fr","blogspot.gr","blogspot.hk","blogspot.hr","blogspot.hu","blogspot.ie","blogspot.in","blogspot.is","blogspot.it","blogspot.jp","blogspot.kr","blogspot.li","blogspot.lt","blogspot.lu","blogspot.md","blogspot.mk","blogspot.mr","blogspot.mx","blogspot.my","blogspot.nl","blogspot.no","blogspot.pe","blogspot.pt","blogspot.qa","blogspot.re","blogspot.ro","blogspot.rs","blogspot.ru","blogspot.se","blogspot.sg","blogspot.si","blogspot.sk","blogspot.sn","blogspot.td","blogspot.tw","blogspot.ug","blogspot.vn","cloudfunctions.net","cloud.goog","codespot.com","googleapis.com","googlecode.com","pagespeedmobilizer.com","publishproxy.com","withgoogle.com","withyoutube.com","awsmppl.com","fin.ci","free.hr","caa.li","ua.rs","conf.se","hs.zone","hs.run","hashbang.sh","hasura.app","hasura-app.io","hepforge.org","herokuapp.com","herokussl.com","myravendb.com","ravendb.community","ravendb.me","development.run","ravendb.run","bpl.biz","orx.biz","ng.city","biz.gl","ng.ink","col.ng","firm.ng","gen.ng","ltd.ng","ngo.ng","ng.school","sch.so","häkkinen.fi","*.moonscale.io","moonscale.net","iki.fi","dyn-berlin.de","in-berlin.de","in-brb.de","in-butter.de","in-dsl.de","in-dsl.net","in-dsl.org","in-vpn.de","in-vpn.net","in-vpn.org","biz.at","info.at","info.cx","ac.leg.br","al.leg.br","am.leg.br","ap.leg.br","ba.leg.br","ce.leg.br","df.leg.br","es.leg.br","go.leg.br","ma.leg.br","mg.leg.br","ms.leg.br","mt.leg.br","pa.leg.br","pb.leg.br","pe.leg.br","pi.leg.br","pr.leg.br","rj.leg.br","rn.leg.br","ro.leg.br","rr.leg.br","rs.leg.br","sc.leg.br","se.leg.br","sp.leg.br","to.leg.br","pixolino.com","ipifony.net","mein-iserv.de","test-iserv.de","iserv.dev","iobb.net","myjino.ru","*.hosting.myjino.ru","*.landing.myjino.ru","*.spectrum.myjino.ru","*.vps.myjino.ru","*.triton.zone","*.cns.joyent.com","js.org","kaas.gg","khplay.nl","keymachine.de","kinghost.net","uni5.net","knightpoint.systems","oya.to","co.krd","edu.krd","git-repos.de","lcube-server.de","svn-repos.de","leadpages.co","lpages.co","lpusercontent.com","lelux.site","co.business","co.education","co.events","co.financial","co.network","co.place","co.technology","app.lmpm.com","linkitools.space","linkyard.cloud","linkyard-cloud.ch","members.linode.com","nodebalancer.linode.com","we.bs","loginline.app","loginline.dev","loginline.io","loginline.services","loginline.site","krasnik.pl","leczna.pl","lubartow.pl","lublin.pl","poniatowa.pl","swidnik.pl","uklugs.org","glug.org.uk","lug.org.uk","lugs.org.uk","barsy.bg","barsy.co.uk","barsyonline.co.uk","barsycenter.com","barsyonline.com","barsy.club","barsy.de","barsy.eu","barsy.in","barsy.info","barsy.io","barsy.me","barsy.menu","barsy.mobi","barsy.net","barsy.online","barsy.org","barsy.pro","barsy.pub","barsy.shop","barsy.site","barsy.support","barsy.uk","*.magentosite.cloud","mayfirst.info","mayfirst.org","hb.cldmail.ru","miniserver.com","memset.net","cloud.metacentrum.cz","custom.metacentrum.cz","flt.cloud.muni.cz","usr.cloud.muni.cz","meteorapp.com","eu.meteorapp.com","co.pl","azurecontainer.io","azurewebsites.net","azure-mobile.net","cloudapp.net","mozilla-iot.org","bmoattachments.org","net.ru","org.ru","pp.ru","ui.nabu.casa","pony.club","of.fashion","on.fashion","of.football","in.london","of.london","for.men","and.mom","for.mom","for.one","for.sale","of.work","to.work","nctu.me","bitballoon.com","netlify.com","4u.com","ngrok.io","nh-serv.co.uk","nfshost.com","dnsking.ch","mypi.co","n4t.co","001www.com","ddnslive.com","myiphost.com","forumz.info","16-b.it","32-b.it","64-b.it","soundcast.me","tcp4.me","dnsup.net","hicam.net","now-dns.net","ownip.net","vpndns.net","dynserv.org","now-dns.org","x443.pw","now-dns.top","ntdll.top","freeddns.us","crafting.xyz","zapto.xyz","nsupdate.info","nerdpol.ovh","blogsyte.com","brasilia.me","cable-modem.org","ciscofreak.com","collegefan.org","couchpotatofries.org","damnserver.com","ddns.me","ditchyourip.com","dnsfor.me","dnsiskinky.com","dvrcam.info","dynns.com","eating-organic.net","fantasyleague.cc","geekgalaxy.com","golffan.us","health-carereform.com","homesecuritymac.com","homesecuritypc.com","hopto.me","ilovecollege.info","loginto.me","mlbfan.org","mmafan.biz","myactivedirectory.com","mydissent.net","myeffect.net","mymediapc.net","mypsx.net","mysecuritycamera.com","mysecuritycamera.net","mysecuritycamera.org","net-freaks.com","nflfan.org","nhlfan.net","no-ip.ca","no-ip.co.uk","no-ip.net","noip.us","onthewifi.com","pgafan.net","point2this.com","pointto.us","privatizehealthinsurance.net","quicksytes.com","read-books.org","securitytactics.com","serveexchange.com","servehumour.com","servep2p.com","servesarcasm.com","stufftoread.com","ufcfan.org","unusualperson.com","workisboring.com","3utilities.com","bounceme.net","ddns.net","ddnsking.com","gotdns.ch","hopto.org","myftp.biz","myftp.org","myvnc.com","no-ip.biz","no-ip.info","no-ip.org","noip.me","redirectme.net","servebeer.com","serveblog.net","servecounterstrike.com","serveftp.com","servegame.com","servehalflife.com","servehttp.com","serveirc.com","serveminecraft.net","servemp3.com","servepics.com","servequake.com","sytes.net","webhop.me","zapto.org","stage.nodeart.io","nodum.co","nodum.io","pcloud.host","nyc.mn","nom.ae","nom.af","nom.ai","nom.al","nym.by","nom.bz","nym.bz","nom.cl","nym.ec","nom.gd","nom.ge","nom.gl","nym.gr","nom.gt","nym.gy","nym.hk","nom.hn","nym.ie","nom.im","nom.ke","nym.kz","nym.la","nym.lc","nom.li","nym.li","nym.lt","nym.lu","nom.lv","nym.me","nom.mk","nym.mn","nym.mx","nom.nu","nym.nz","nym.pe","nym.pt","nom.pw","nom.qa","nym.ro","nom.rs","nom.si","nym.sk","nom.st","nym.su","nym.sx","nom.tj","nym.tw","nom.ug","nom.uy","nom.vc","nom.vg","static.observableusercontent.com","cya.gg","cloudycluster.net","nid.io","opencraft.hosting","operaunite.com","skygearapp.com","outsystemscloud.com","ownprovider.com","own.pm","ox.rs","oy.lc","pgfog.com","pagefrontapp.com","art.pl","gliwice.pl","krakow.pl","poznan.pl","wroc.pl","zakopane.pl","pantheonsite.io","gotpantheon.com","mypep.link","perspecta.cloud","on-web.fr","*.platform.sh","*.platformsh.site","dyn53.io","co.bn","xen.prgmr.com","priv.at","prvcy.page","*.dweb.link","protonet.io","chirurgiens-dentistes-en-france.fr","byen.site","pubtls.org","qualifioapp.com","qbuser.com","instantcloud.cn","ras.ru","qa2.com","qcx.io","*.sys.qcx.io","dev-myqnapcloud.com","alpha-myqnapcloud.com","myqnapcloud.com","*.quipelements.com","vapor.cloud","vaporcloud.io","rackmaze.com","rackmaze.net","*.on-k3s.io","*.on-rancher.cloud","*.on-rio.io","readthedocs.io","rhcloud.com","app.render.com","onrender.com","repl.co","repl.run","resindevice.io","devices.resinstaging.io","hzc.io","wellbeingzone.eu","ptplus.fit","wellbeingzone.co.uk","git-pages.rit.edu","sandcats.io","logoip.de","logoip.com","schokokeks.net","gov.scot","scrysec.com","firewall-gateway.com","firewall-gateway.de","my-gateway.de","my-router.de","spdns.de","spdns.eu","firewall-gateway.net","my-firewall.org","myfirewall.org","spdns.org","senseering.net","biz.ua","co.ua","pp.ua","shiftedit.io","myshopblocks.com","shopitsite.com","mo-siemens.io","1kapp.com","appchizi.com","applinzi.com","sinaapp.com","vipsinaapp.com","siteleaf.net","bounty-full.com","alpha.bounty-full.com","beta.bounty-full.com","stackhero-network.com","static.land","dev.static.land","sites.static.land","apps.lair.io","*.stolos.io","spacekit.io","customer.speedpartner.de","api.stdlib.com","storj.farm","utwente.io","soc.srcf.net","user.srcf.net","temp-dns.com","applicationcloud.io","scapp.io","*.s5y.io","*.sensiosite.cloud","syncloud.it","diskstation.me","dscloud.biz","dscloud.me","dscloud.mobi","dsmynas.com","dsmynas.net","dsmynas.org","familyds.com","familyds.net","familyds.org","i234.me","myds.me","synology.me","vpnplus.to","direct.quickconnect.to","taifun-dns.de","gda.pl","gdansk.pl","gdynia.pl","med.pl","sopot.pl","edugit.org","telebit.app","telebit.io","*.telebit.xyz","gwiddle.co.uk","thingdustdata.com","cust.dev.thingdust.io","cust.disrec.thingdust.io","cust.prod.thingdust.io","cust.testing.thingdust.io","arvo.network","azimuth.network","bloxcms.com","townnews-staging.com","12hp.at","2ix.at","4lima.at","lima-city.at","12hp.ch","2ix.ch","4lima.ch","lima-city.ch","trafficplex.cloud","de.cool","12hp.de","2ix.de","4lima.de","lima-city.de","1337.pictures","clan.rip","lima-city.rocks","webspace.rocks","lima.zone","*.transurl.be","*.transurl.eu","*.transurl.nl","tuxfamily.org","dd-dns.de","diskstation.eu","diskstation.org","dray-dns.de","draydns.de","dyn-vpn.de","dynvpn.de","mein-vigor.de","my-vigor.de","my-wan.de","syno-ds.de","synology-diskstation.de","synology-ds.de","uber.space","*.uberspace.de","hk.com","hk.org","ltd.hk","inc.hk","virtualuser.de","virtual-user.de","urown.cloud","dnsupdate.info","lib.de.us","2038.io","router.management","v-info.info","voorloper.cloud","v.ua","wafflecell.com","*.webhare.dev","wedeploy.io","wedeploy.me","wedeploy.sh","remotewd.com","wmflabs.org","myforum.community","community-pro.de","diskussionsbereich.de","community-pro.net","meinforum.net","half.host","xnbay.com","u2.xnbay.com","u2-local.xnbay.com","cistron.nl","demon.nl","xs4all.space","yandexcloud.net","storage.yandexcloud.net","website.yandexcloud.net","official.academy","yolasite.com","ybo.faith","yombo.me","homelink.one","ybo.party","ybo.review","ybo.science","ybo.trade","nohost.me","noho.st","za.net","za.org","now.sh","bss.design","basicserver.io","virtualserver.io","enterprisecloud.nu"]')},13:function(e,t,a){e.exports=a(14)},14:function(e,t,a){"use strict";var n=a(15),o=n.ip2long,i=n.long2ip,r=Math,s=a(17),u=function e(){if(!(this instanceof e))return new e},c=function(e,t,a){e||(e={}),e[t]||(e[t]=[]),e[t].push(a)};u.prototype.range=function(e){if(!(e.indexOf("/")>-1))return null;var t={},a=e.split("/");return a[1]>32?null:(t.start=i(o(a[0])&-1<<32-+a[1]),t.end=i(o(t.start)+r.pow(2,32-+a[1])-1),t)},u.prototype.list=function(e){if(void 0===e)return null;var t=this.range(e);if(!t)return null;var a=o,n=i,r=[],s=0,u=a(t.start),c=a(t.end);for(r[s++]=t.start;u++<c;)r[s++]=n(u);return r};u.prototype.filter=function(e){if(!(e instanceof Array)||e.length<=0)return null;var t=0,a=!0,n=(e=function(e){for(var t=e.length,a=o,n=null,i=0;i<t;i++)(n=e[i])&&(e[i]=a(n));return e=e.sort()}(e)).length,r=null,s=null,u=null,l={};if(1===e.length)return c(l,0,i(e[0]));for(var p=0;p<n;p++)r=s=e[p],a||(t+=1),(u=e[p+1])?u-s==1?(c(l,t,i(s)),a=!0):(c(l,t,i(s)),a=!1):r&&(s-r==1?(c(l,t,i(s)),a=!0):c(l,++t,i(s)));return l},u.prototype.getBlocks=function(e){var t=this.filter(e),a=[];for(var n in t){var o=t[n];if(1!==o.length){var i=o.shift(),r=o.pop(),u=s.calculate(i,r);for(var c in u)a.push(u[c].ipLowStr+"/"+u[c].prefixSize)}else a.push(o[0])}return a},e.exports=u},15:function(e,t,a){phpjs=a(16),phpjs.registerGlobals=function(){for(var e in this)window[e]=this[e]},e.exports=phpjs},16:function(module,exports,__webpack_require__){exports.XMLHttpRequest={},exports.window={window:{},document:{lastModified:1388954399,getElementsByTagName:function(){return[]}},location:{href:""}},exports.window.window=exports.window,exports.array=function(){try{this.php_js=this.php_js||{}}catch(e){this.php_js={}}var arrInst,e,__,that=this,PHPJS_Array=function(){};return mainArgs=arguments,p=this.php_js,_indexOf=function(e,t,a){for(var n=t||0,o=!a,i=this.length;n<i;){if(this[n]===e||o&&this[n]==e)return n;n++}return-1},p.Relator||(p.Relator=function(){function e(e){for(var t=0,a=this.length;t<a;){if(this[t]===e)return t;t++}return-1}return function t(){var a=[],n=[];return a.indexOf||(a.indexOf=e),{$:function(){return t()},constructor:function(e){var t=a.indexOf(e);return~t?n[t]:n[a.push(e)-1]={},this.method(e).that=e,this.method(e)},method:function(e){return n[a.indexOf(e)]}}}()}()),p&&p.ini&&"on"===p.ini["phpjs.return_phpjs_arrays"].local_value.toLowerCase()?(p.PHPJS_Array||(__=p.ArrayRelator=p.ArrayRelator||p.Relator.$(),p.PHPJS_Array=function(){var e,t,a=__.constructor(this),n=arguments,o=0;for(n=1===n.length&&n[0]&&"object"==typeof n[0]&&n[0].length&&!n[0].propertyIsEnumerable("length")?n[0]:n,a.objectChain||(a.objectChain=n,a.object={},a.keys=[],a.values=[]),e=n.length;o<e;o++)for(t in n[o]){this[t]=a.object[t]=n[o][t],a.keys[a.keys.length]=t,a.values[a.values.length]=n[o][t];break}},e=p.PHPJS_Array.prototype,e.change_key_case=function(e){for(var t,a,n=__.method(this),o=0,i=n.keys.length,r=e&&"CASE_LOWER"!==e?"toUpperCase":"toLowerCase";o<i;)(t=n.keys[o])!==(a=n.keys[o]=n.keys[o][r]())&&(this[t]=n.object[t]=n.objectChain[o][t]=null,delete this[t],delete n.object[t],delete n.objectChain[o][t],this[a]=n.object[a]=n.objectChain[o][a]=n.values[o]),o++;return this},e.flip=function(){for(var e=__.method(this),t=0,a=e.keys.length;t<a;)oldkey=e.keys[t],newkey=e.values[t],oldkey!==newkey&&(this[oldkey]=e.object[oldkey]=e.objectChain[t][oldkey]=null,delete this[oldkey],delete e.object[oldkey],delete e.objectChain[t][oldkey],this[newkey]=e.object[newkey]=e.objectChain[t][newkey]=oldkey,e.keys[t]=newkey),t++;return this},e.walk=function(funcname,userdata){var _=__.method(this),obj,func,ini,i=0,kl=0;try{if("function"==typeof funcname)for(i=0,kl=_.keys.length;i<kl;i++)arguments.length>1?funcname(_.values[i],_.keys[i],userdata):funcname(_.values[i],_.keys[i]);else if("string"==typeof funcname)if(this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},ini=this.php_js.ini["phpjs.no-eval"],!ini||0===parseInt(ini.local_value,10)||ini.local_value.toLowerCase&&"off"===ini.local_value.toLowerCase())if(arguments.length>1)for(i=0,kl=_.keys.length;i<kl;i++)eval(funcname+"(_.values[i], _.keys[i], userdata)");else for(i=0,kl=_.keys.length;i<kl;i++)eval(funcname+"(_.values[i], _.keys[i])");else if(arguments.length>1)for(i=0,kl=_.keys.length;i<kl;i++)this.window[funcname](_.values[i],_.keys[i],userdata);else for(i=0,kl=_.keys.length;i<kl;i++)this.window[funcname](_.values[i],_.keys[i]);else{if(!funcname||"object"!=typeof funcname||2!==funcname.length)return!1;if(obj=funcname[0],func=funcname[1],arguments.length>1)for(i=0,kl=_.keys.length;i<kl;i++)obj[func](_.values[i],_.keys[i],userdata);else for(i=0,kl=_.keys.length;i<kl;i++)obj[func](_.values[i],_.keys[i])}}catch(e){return!1}return this},e.keys=function(e,t){var a,n=__.method(this),o=[],i=!!t;if(!(void 0!==e))return n.keys;for(;-1!==(a=_indexOf(n.values,a,i));)o[o.length]=n.keys[a];return o},e.values=function(){return __.method(this).values},e.search=function(e,t){var a,n,o,i,r=__.method(this),s=!!t,u=r.values;if("object"==typeof e&&e.exec){for(s||(i="i"+(e.global?"g":"")+(e.multiline?"m":"")+(e.sticky?"y":""),e=new RegExp(e.source,i)),a=0,n=u.length;a<n;a++)if(o=u[a],e.test(o))return r.keys[a];return!1}for(a=0,n=u.length;a<n;a++)if(o=u[a],s&&o===e||!s&&o==e)return r.keys[a];return!1},e.sum=function(){for(var e=__.method(this),t=0,a=0,n=e.keys.length;a<n;)isNaN(parseFloat(e.values[a]))||(t+=parseFloat(e.values[a])),a++;return t},e.foreach=function(e){for(var t=__.method(this),a=0,n=t.keys.length;a<n;)1===e.length?e(t.values[a]):e(t.keys[a],t.values[a]),a++;return this},e.list=function(){for(var e,t=__.method(this),a=0,n=arguments.length;a<n;)(e=t.keys[a])&&e.length===parseInt(e,10).toString().length&&parseInt(e,10)<n&&(that.window[arguments[e]]=t.values[e]),a++;return this},e.forEach=function(e){for(var t=__.method(this),a=0,n=t.keys.length;a<n;)e(t.values[a],t.keys[a],this),a++;return this},e.$object=function(){return __.method(this).object},e.$objectChain=function(){return __.method(this).objectChain}),PHPJS_Array.prototype=p.PHPJS_Array.prototype,arrInst=new PHPJS_Array,p.PHPJS_Array.apply(arrInst,mainArgs),arrInst):Array.prototype.slice.call(mainArgs)},exports.array_change_key_case=function(e,t){var a,n,o={};if("[object Array]"===Object.prototype.toString.call(e))return e;if(e&&"object"==typeof e&&e.change_key_case)return e.change_key_case(t);if(e&&"object"==typeof e){for(n in a=t&&"CASE_LOWER"!==t?"toUpperCase":"toLowerCase",e)o[n[a]()]=e[n];return o}return!1},exports.array_chunk=function(e,t,a){var n,o="",i=0,r=-1,s=e.length||0,u=[];if(t<1)return null;if("[object Array]"===Object.prototype.toString.call(e))if(a)for(;i<s;)(n=i%t)?u[r][i]=e[i]:u[++r]={},u[r][i]=e[i],i++;else for(;i<s;)(n=i%t)?u[r][n]=e[i]:u[++r]=[e[i]],i++;else if(a)for(o in e)e.hasOwnProperty(o)&&((n=i%t)?u[r][o]=e[o]:u[++r]={},u[r][o]=e[o],i++);else for(o in e)e.hasOwnProperty(o)&&((n=i%t)?u[r][n]=e[o]:u[++r]=[e[o]],i++);return u},exports.array_combine=function(e,t){var a={},n=e&&e.length,o=0;if("object"!=typeof e||"object"!=typeof t||"number"!=typeof n||"number"!=typeof t.length||!n)return!1;if(n!=t.length)return!1;for(o=0;o<n;o++)a[e[o]]=t[o];return a},exports.array_count_values=function(e){var t={},a="",n=function(e){switch(typeof e){case"number":if(Math.floor(e)!==e)return;case"string":e in this&&this.hasOwnProperty(e)?++this[e]:this[e]=1}};if("array"===function(e){var t=typeof e;return"object"===(t=t.toLowerCase())&&(t="array"),t}(e))for(a in e)e.hasOwnProperty(a)&&n.call(t,e[a]);return t},exports.array_diff=function(e){var t={},a=arguments.length,n="",o=1,i="",r={};e:for(n in e)for(o=1;o<a;o++){for(i in r=arguments[o])if(r[i]===e[n])continue e;t[n]=e[n]}return t},exports.array_diff_assoc=function(e){var t={},a=arguments.length,n="",o=1,i="",r={};e:for(n in e)for(o=1;o<a;o++){for(i in r=arguments[o])if(r[i]===e[n]&&i===n)continue e;t[n]=e[n]}return t},exports.array_diff_key=function(e){var t=arguments.length,a={},n="",o=1,i="";e:for(n in e)for(o=1;o<t;o++){for(i in arguments[o])if(i===n)continue e;a[n]=e[n]}return a},exports.array_diff_uassoc=function(e){var t={},a=arguments.length-1,n=arguments[a],o={},i=1,r="",s="";n="string"==typeof n?this.window[n]:"[object Array]"===Object.prototype.toString.call(n)?this.window[n[0]][n[1]]:n;e:for(r in e)for(i=1;i<a;i++){for(s in o=arguments[i])if(o[s]===e[r]&&0===n(s,r))continue e;t[r]=e[r]}return t},exports.array_diff_ukey=function(e){var t={},a=arguments.length-1,n=arguments[a],o=1,i="",r="";n="string"==typeof n?this.window[n]:"[object Array]"===Object.prototype.toString.call(n)?this.window[n[0]][n[1]]:n;e:for(i in e)for(o=1;o<a;o++){for(r in arguments[o])if(0===n(r,i))continue e;t[i]=e[i]}return t},exports.array_fill=function(e,t,a){var n,o={};if(!isNaN(e)&&!isNaN(t))for(n=0;n<t;n++)o[n+e]=a;return o},exports.array_fill_keys=function(e,t){var a={},n="";for(n in e)a[e[n]]=t;return a},exports.array_filter=function(e,t){var a,n={};for(a in t=t||function(e){return e},"[object Array]"===Object.prototype.toString.call(e)&&(n=[]),e)t(e[a])&&(n[a]=e[a]);return n},exports.array_intersect=function(e){var t={},a=arguments.length,n=a-1,o="",i={},r=0,s="";e:for(o in e)t:for(r=1;r<a;r++){for(s in i=arguments[r])if(i[s]===e[o]){r===n&&(t[o]=e[o]);continue t}continue e}return t},exports.array_intersect_assoc=function(e){var t={},a=arguments.length,n=a-1,o="",i={},r=0,s="";e:for(o in e)t:for(r=1;r<a;r++){for(s in i=arguments[r])if(i[s]===e[o]&&s===o){r===n&&(t[o]=e[o]);continue t}continue e}return t},exports.array_intersect_key=function(e){var t={},a=arguments.length,n=a-1,o="",i=0,r="";e:for(o in e)t:for(i=1;i<a;i++){for(r in arguments[i])if(r===o){i===n&&(t[o]=e[o]);continue t}continue e}return t},exports.array_intersect_uassoc=function(e){var t={},a=arguments.length-1,n=a-1,o=arguments[a],i="",r=1,s={},u="";o="string"==typeof o?this.window[o]:"[object Array]"===Object.prototype.toString.call(o)?this.window[o[0]][o[1]]:o;e:for(i in e)t:for(r=1;r<a;r++){for(u in s=arguments[r])if(s[u]===e[i]&&0===o(u,i)){r===n&&(t[i]=e[i]);continue t}continue e}return t},exports.array_intersect_ukey=function(e){var t={},a=arguments.length-1,n=a-1,o=arguments[a],i="",r=1,s="";o="string"==typeof o?this.window[o]:"[object Array]"===Object.prototype.toString.call(o)?this.window[o[0]][o[1]]:o;e:for(i in e)t:for(r=1;r<a;r++){for(s in arguments[r])if(0===o(s,i)){r===n&&(t[i]=e[i]);continue t}continue e}return t},exports.array_key_exists=function(e,t){return!(!t||t.constructor!==Array&&t.constructor!==Object)&&e in t},exports.array_keys=function(e,t,a){var n=void 0!==t,o=[],i=!!a,r=!0,s="";if(e&&"object"==typeof e&&e.change_key_case)return e.keys(t,a);for(s in e)e.hasOwnProperty(s)&&(r=!0,n&&(i&&e[s]!==t||e[s]!=t)&&(r=!1),r&&(o[o.length]=s));return o},exports.array_map=function(e){for(var t=arguments.length,a=arguments,n=this.window,o=null,i=e,r=a[1].length,s=0,u=1,c=0,l=[],p=[];s<r;){for(;u<t;)l[c++]=a[u++][s];if(c=0,u=1,e){if("string"==typeof e)i=n[e];else if("object"==typeof e&&e.length){if(void 0===(o="string"==typeof e[0]?n[e[0]]:e[0]))throw"Object not found: "+e[0];i="string"==typeof e[1]?o[e[1]]:e[1]}p[s++]=i.apply(o,l)}else p[s++]=l;l=[]}return p},exports.array_merge=function(){var e,t=Array.prototype.slice.call(arguments),a=t.length,n={},o="",i=0,r=0,s=0,u=0,c=Object.prototype.toString,l=!0;for(s=0;s<a;s++)if("[object Array]"!==c.call(t[s])){l=!1;break}if(l){for(l=[],s=0;s<a;s++)l=l.concat(t[s]);return l}for(s=0,u=0;s<a;s++)if(e=t[s],"[object Array]"===c.call(e))for(r=0,i=e.length;r<i;r++)n[u++]=e[r];else for(o in e)e.hasOwnProperty(o)&&(parseInt(o,10)+""===o?n[u++]=e[o]:n[o]=e[o]);return n},exports.array_multisort=function(e){var t,a,n,o,i,r,s,u,c,l,p,m=[0],f=[],h=[],d=[],g=arguments,b={SORT_REGULAR:16,SORT_NUMERIC:17,SORT_STRING:18,SORT_ASC:32,SORT_DESC:40},k=function(e,t){return h.shift()},v=[[function(e,t){return d.push(e>t?1:e<t?-1:0),e>t?1:e<t?-1:0},function(e,t){return d.push(t>e?1:t<e?-1:0),t>e?1:t<e?-1:0}],[function(e,t){return d.push(e-t),e-t},function(e,t){return d.push(t-e),t-e}],[function(e,t){return d.push(e+"">t+""?1:e+""<t+""?-1:0),e+"">t+""?1:e+""<t+""?-1:0},function(e,t){return d.push(t+"">e+""?1:t+""<e+""?-1:0),t+"">e+""?1:t+""<e+""?-1:0}]],y=[[]],j=[[]];if("[object Array]"===Object.prototype.toString.call(e))y[0]=e;else{if(!e||"object"!=typeof e)return!1;for(a in e)e.hasOwnProperty(a)&&(j[0].push(a),y[0].push(e[a]))}var w=y[0].length,_=[0,w],A=arguments.length;for(n=1;n<A;n++)if("[object Array]"===Object.prototype.toString.call(arguments[n])){if(y[n]=arguments[n],m[n]=0,arguments[n].length!==w)return!1}else if(arguments[n]&&"object"==typeof arguments[n]){for(a in j[n]=[],y[n]=[],m[n]=0,arguments[n])arguments[n].hasOwnProperty(a)&&(j[n].push(a),y[n].push(arguments[n][a]));if(y[n].length!==w)return!1}else{if("string"!=typeof arguments[n])return!1;var x=m.pop();if(void 0===b[arguments[n]]||(b[arguments[n]]>>>4&x>>>4)>0)return!1;m.push(x+b[arguments[n]])}for(a=0;a!==w;a++)f.push(!0);for(a in y)if(y.hasOwnProperty(a)){if(c=[],l=[],u=0,h=[],d=[],0===_.length){if("[object Array]"===Object.prototype.toString.call(arguments[a]))g[a]=y[a];else{for(o in arguments[a])arguments[a].hasOwnProperty(o)&&delete arguments[a][o];for(r=y[a].length,n=0,s=0;n<r;n++)s=j[a][n],g[a][s]=y[a][n]}delete y[a],delete j[a];continue}var z=v[3&m[a]][(8&m[a])>0?1:0];for(i=0;i!==_.length;i+=2)for(t in(l=y[a].slice(_[i],_[i+1]+1)).sort(z),c[i]=[].concat(d),u=_[i],l)l.hasOwnProperty(t)&&(y[a][u]=l[t],u++);for(n in z=k,y)if(y.hasOwnProperty(n)){if(y[n]===y[a])continue;for(i=0;i!==_.length;i+=2)for(t in l=y[n].slice(_[i],_[i+1]+1),h=[].concat(c[i]),l.sort(z),u=_[i],l)l.hasOwnProperty(t)&&(y[n][u]=l[t],u++)}for(n in j)if(j.hasOwnProperty(n))for(i=0;i!==_.length;i+=2)for(t in l=j[n].slice(_[i],_[i+1]+1),h=[].concat(c[i]),l.sort(z),u=_[i],l)l.hasOwnProperty(t)&&(j[n][u]=l[t],u++);for(n in p=null,_=[],y[a])if(y[a].hasOwnProperty(n)){if(!f[n]){1&_.length&&_.push(n-1),p=null;continue}1&_.length?y[a][n]!==p&&(_.push(n-1),p=y[a][n]):(null!==p&&(y[a][n]===p?_.push(n-1):f[n]=!1),p=y[a][n])}if(1&_.length&&_.push(n),"[object Array]"===Object.prototype.toString.call(arguments[a]))g[a]=y[a];else{for(n in arguments[a])arguments[a].hasOwnProperty(n)&&delete arguments[a][n];for(r=y[a].length,n=0,s=0;n<r;n++)s=j[a][n],g[a][s]=y[a][n]}delete y[a],delete j[a]}return!0},exports.array_pad=function(e,t,a){var n=[],o=[],i=0,r=0;if("[object Array]"===Object.prototype.toString.call(e)&&!isNaN(t))if((i=(t<0?-1*t:t)-e.length)>0){for(r=0;r<i;r++)o[r]=a;n=t<0?o.concat(e):e.concat(o)}else n=e;return n},exports.array_pop=function(e){var t="",a="";if(e.hasOwnProperty("length"))return e.length?e.pop():null;for(t in e)e.hasOwnProperty(t)&&(a=t);if(a){var n=e[a];return delete e[a],n}return null},exports.array_product=function(e){var t,a=0,n=1;if("[object Array]"!==Object.prototype.toString.call(e))return null;for(t=e.length;a<t;)n*=isNaN(e[a])?0:e[a],a++;return n},exports.array_push=function(e){var t=0,a="",n=arguments,o=n.length,i=/^\d$/,r=0,s=0,u=0;if(e.hasOwnProperty("length")){for(t=1;t<o;t++)e[e.length]=n[t];return e.length}for(a in e)e.hasOwnProperty(a)&&(++u,-1!==a.search(i)&&(s=(r=parseInt(a,10))>s?r:s));for(t=1;t<o;t++)e[++s]=n[t];return u+t-1},exports.array_rand=function(e,t){var a=[],n=t||1,o=function(e,t){for(var a=!1,n=0,o=e.length;n<o;){if(e[n]===t){a=!0;break}n++}return a};if("[object Array]"===Object.prototype.toString.call(e)&&n<=e.length)for(;;){var i=Math.floor(Math.random()*e.length);if(a.length===n)break;o(a,i)||a.push(i)}else a=null;return 1==n?a.join():a},exports.array_reduce=function(e,t){var a=e.length,n=0,o=0,i=[];for(o=0;o<a;o+=2)i[0]=e[o],e[o+1]?i[1]=e[o+1]:i[1]=0,n+=t.apply(null,i),i=[];return n},exports.array_replace=function(e){var t={},a=0,n="",o=arguments.length;if(o<2)throw new Error("There should be at least 2 arguments passed to array_replace()");for(n in e)t[n]=e[n];for(a=1;a<o;a++)for(n in arguments[a])t[n]=arguments[a][n];return t},exports.array_replace_recursive=function(e){var t={},a=0,n="",o=arguments.length;if(o<2)throw new Error("There should be at least 2 arguments passed to array_replace_recursive()");for(n in e)t[n]=e[n];for(a=1;a<o;a++)for(n in arguments[a])t[n]&&"object"==typeof t[n]?t[n]=this.array_replace_recursive(t[n],arguments[a][n]):t[n]=arguments[a][n];return t},exports.array_reverse=function(e,t){var a,n=t?{}:[];if("[object Array]"===Object.prototype.toString.call(e)&&!t)return e.slice(0).reverse();if(t){var o=[];for(a in e)o.push(a);for(var i=o.length;i--;)n[a=o[i]]=e[a]}else for(a in e)n.unshift(e[a]);return n},exports.array_shift=function(e){return 0===e.length?null:e.length>0?e.shift():void 0},exports.array_sum=function(e){var t,a=0;if(e&&"object"==typeof e&&e.change_key_case)return e.sum.apply(e,Array.prototype.slice.call(arguments,0));if("object"!=typeof e)return null;for(t in e)isNaN(parseFloat(e[t]))||(a+=parseFloat(e[t]));return a},exports.array_udiff=function(e){var t={},a=arguments.length-1,n=arguments[a],o="",i=1,r="",s="";n="string"==typeof n?this.window[n]:"[object Array]"===Object.prototype.toString.call(n)?this.window[n[0]][n[1]]:n;e:for(r in e)for(i=1;i<a;i++){for(s in o=arguments[i])if(0===n(o[s],e[r]))continue e;t[r]=e[r]}return t},exports.array_udiff_assoc=function(e){var t={},a=arguments.length-1,n=arguments[a],o={},i=1,r="",s="";n="string"==typeof n?this.window[n]:"[object Array]"===Object.prototype.toString.call(n)?this.window[n[0]][n[1]]:n;e:for(r in e)for(i=1;i<a;i++){for(s in o=arguments[i])if(0===n(o[s],e[r])&&s===r)continue e;t[r]=e[r]}return t},exports.array_udiff_uassoc=function(e){var t={},a=arguments.length-1,n=a-1,o=arguments[a],i=arguments[n],r="",s=1,u="",c={};o="string"==typeof o?this.window[o]:"[object Array]"===Object.prototype.toString.call(o)?this.window[o[0]][o[1]]:o,i="string"==typeof i?this.window[i]:"[object Array]"===Object.prototype.toString.call(i)?this.window[i[0]][i[1]]:i;e:for(r in e)for(s=1;s<n;s++){for(u in c=arguments[s])if(0===i(c[u],e[r])&&0===o(u,r))continue e;t[r]=e[r]}return t},exports.array_uintersect=function(e){var t={},a=arguments.length-1,n=a-1,o=arguments[a],i="",r=1,s={},u="";o="string"==typeof o?this.window[o]:"[object Array]"===Object.prototype.toString.call(o)?this.window[o[0]][o[1]]:o;e:for(i in e)t:for(r=1;r<a;r++){for(u in s=arguments[r])if(0===o(s[u],e[i])){r===n&&(t[i]=e[i]);continue t}continue e}return t},exports.array_uintersect_assoc=function(e){var t={},a=arguments.length-1,n=a-2,o=arguments[a],i="",r=1,s={},u="";o="string"==typeof o?this.window[o]:"[object Array]"===Object.prototype.toString.call(o)?this.window[o[0]][o[1]]:o;e:for(i in e)t:for(r=1;r<a;r++){for(u in s=arguments[r])if(u===i&&0===o(s[u],e[i])){r===n&&(t[i]=e[i]);continue t}continue e}return t},exports.array_uintersect_uassoc=function(e){var t={},a=arguments.length-1,n=a-1,o=arguments[a],i=arguments[n],r="",s=1,u="",c={};o="string"==typeof o?this.window[o]:"[object Array]"===Object.prototype.toString.call(o)?this.window[o[0]][o[1]]:o,i="string"==typeof i?this.window[i]:"[object Array]"===Object.prototype.toString.call(i)?this.window[i[0]][i[1]]:i;e:for(r in e)t:for(s=1;s<n;s++){for(u in c=arguments[s])if(0===i(c[u],e[r])&&0===o(u,r)){s===arguments.length-3&&(t[r]=e[r]);continue t}continue e}return t},exports.array_unique=function(e){var t="",a={},n="",o=function(e,t){var a="";for(a in t)if(t.hasOwnProperty(a)&&t[a]+""==e+"")return a;return!1};for(t in e)e.hasOwnProperty(t)&&!1===o(n=e[t],a)&&(a[t]=n);return a},exports.array_unshift=function(e){for(var t=arguments.length;0!=--t;)arguments[0].unshift(arguments[t]);return arguments[0].length},exports.array_values=function(e){var t=[],a="";if(e&&"object"==typeof e&&e.change_key_case)return e.values();for(a in e)t[t.length]=e[a];return t},exports.array_walk_recursive=function(array,funcname,userdata){var key;if("object"!=typeof array)return!1;for(key in array){if("object"==typeof array[key])return this.array_walk_recursive(array[key],funcname,userdata);void 0!==userdata?eval(funcname+"( array [key] , key , userdata  )"):eval(funcname+"(  userdata ) ")}return!0},exports.compact=function(){var e={},t=this,a=function(n){var o=0,i=n.length,r="";for(o=0;o<i;o++)r=n[o],"[object Array]"===Object.prototype.toString.call(r)?a(r):void 0!==t.window[r]&&(e[r]=t.window[r]);return!0};return a(arguments),e},exports.count=function(e,t){var a,n=0;if(null==e)return 0;if(e.constructor!==Array&&e.constructor!==Object)return 1;for(a in"COUNT_RECURSIVE"===t&&(t=1),1!=t&&(t=0),e)e.hasOwnProperty(a)&&(n++,1!=t||!e[a]||e[a].constructor!==Array&&e[a].constructor!==Object||(n+=this.count(e[a],1)));return n},exports.current=function(e){this.php_js=this.php_js||{},this.php_js.pointers=this.php_js.pointers||[];var t=this.php_js.pointers;t.indexOf||(t.indexOf=function(e){for(var t=0,a=this.length;t<a;t++)if(this[t]===e)return t;return-1}),-1===t.indexOf(e)&&t.push(e,0);var a=t.indexOf(e),n=t[a+1];if("[object Array]"===Object.prototype.toString.call(e))return e[n]||!1;var o=0;for(var i in e){if(o===n)return e[i];o++}return!1},exports.each=function(e){this.php_js=this.php_js||{},this.php_js.pointers=this.php_js.pointers||[];var t=this.php_js.pointers;t.indexOf||(t.indexOf=function(e){for(var t=0,a=this.length;t<a;t++)if(this[t]===e)return t;return-1}),-1===t.indexOf(e)&&t.push(e,0);var a,n=t.indexOf(e),o=t[n+1];if("[object Array]"!==Object.prototype.toString.call(e)){var i=0;for(var r in e){if(i===o)return t[n+1]+=1,each.returnArrayOnly?[r,e[r]]:{1:e[r],value:e[r],0:r,key:r};i++}return!1}return 0!==e.length&&o!==e.length&&(a=o,t[n+1]+=1,each.returnArrayOnly?[a,e[a]]:{1:e[a],value:e[a],0:a,key:a})},exports.end=function(e){this.php_js=this.php_js||{},this.php_js.pointers=this.php_js.pointers||[];var t=this.php_js.pointers;t.indexOf||(t.indexOf=function(e){for(var t=0,a=this.length;t<a;t++)if(this[t]===e)return t;return-1}),-1===t.indexOf(e)&&t.push(e,0);var a=t.indexOf(e);if("[object Array]"!==Object.prototype.toString.call(e)){var n,o=0;for(var i in e)o++,n=e[i];return 0!==o&&(t[a+1]=o-1,n)}return 0!==e.length&&(t[a+1]=e.length-1,e[t[a+1]])},exports.in_array=function(e,t,a){var n="";if(!!a){for(n in t)if(t[n]===e)return!0}else for(n in t)if(t[n]==e)return!0;return!1},exports.key=function(e){this.php_js=this.php_js||{},this.php_js.pointers=this.php_js.pointers||[];var t=this.php_js.pointers;t.indexOf||(t.indexOf=function(e){for(var t=0,a=this.length;t<a;t++)if(this[t]===e)return t;return-1}),-1===t.indexOf(e)&&t.push(e,0);var a=t[t.indexOf(e)+1];if("[object Array]"!==Object.prototype.toString.call(e)){var n=0;for(var o in e){if(n===a)return o;n++}return!1}return 0!==e.length&&a},exports.next=function(e){this.php_js=this.php_js||{},this.php_js.pointers=this.php_js.pointers||[];var t=this.php_js.pointers;t.indexOf||(t.indexOf=function(e){for(var t=0,a=this.length;t<a;t++)if(this[t]===e)return t;return-1}),-1===t.indexOf(e)&&t.push(e,0);var a=t.indexOf(e),n=t[a+1];if("[object Array]"!==Object.prototype.toString.call(e)){var o=0;for(var i in e){if(o===n+1)return t[a+1]+=1,e[i];o++}return!1}return 0!==e.length&&n!==e.length-1&&(t[a+1]+=1,e[t[a+1]])},exports.prev=function(e){this.php_js=this.php_js||{},this.php_js.pointers=this.php_js.pointers||[];var t=this.php_js.pointers;t.indexOf||(t.indexOf=function(e){for(var t=0,a=this.length;t<a;t++)if(this[t]===e)return t;return-1});var a=t.indexOf(e),n=t[a+1];if(-1===t.indexOf(e)||0===n)return!1;if("[object Array]"!==Object.prototype.toString.call(e)){var o=0;for(var i in e){if(o===n-1)return t[a+1]-=1,e[i];o++}}return 0!==e.length&&(t[a+1]-=1,e[t[a+1]])},exports.range=function(e,t,a){var n,o,i=[],r=a||1,s=!1;if(isNaN(e)||isNaN(t)?isNaN(e)&&isNaN(t)?(s=!0,n=e.charCodeAt(0),o=t.charCodeAt(0)):(n=isNaN(e)?0:e,o=isNaN(t)?0:t):(n=e,o=t),!(n>o))for(;n<=o;)i.push(s?String.fromCharCode(n):n),n+=r;else for(;n>=o;)i.push(s?String.fromCharCode(n):n),n-=r;return i},exports.reset=function(e){this.php_js=this.php_js||{},this.php_js.pointers=this.php_js.pointers||[];var t=this.php_js.pointers;t.indexOf||(t.indexOf=function(e){for(var t=0,a=this.length;t<a;t++)if(this[t]===e)return t;return-1}),-1===t.indexOf(e)&&t.push(e,0);var a=t.indexOf(e);if("[object Array]"!==Object.prototype.toString.call(e)){for(var n in e)return-1===t.indexOf(e)?t.push(e,0):t[a+1]=0,e[n];return!1}return 0!==e.length&&(t[a+1]=0,e[t[a+1]])},exports.shuffle=function(e){var t=[],a="",n=0,o=!1,i=[];for(a in e)e.hasOwnProperty(a)&&(t.push(e[a]),o&&delete e[a]);for(t.sort((function(){return.5-Math.random()})),this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},i=(o=this.php_js.ini["phpjs.strictForIn"]&&this.php_js.ini["phpjs.strictForIn"].local_value&&"off"!==this.php_js.ini["phpjs.strictForIn"].local_value)?e:i,n=0;n<t.length;n++)i[n]=t[n];return o||i},exports.uasort=function(e,t){var a,n=[],o="",i=0,r={};for(o in"string"==typeof t?t=this[t]:"[object Array]"===Object.prototype.toString.call(t)&&(t=this[t[0]][t[1]]),this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},r=(a=this.php_js.ini["phpjs.strictForIn"]&&this.php_js.ini["phpjs.strictForIn"].local_value&&"off"!==this.php_js.ini["phpjs.strictForIn"].local_value)?e:r,e)e.hasOwnProperty(o)&&(n.push([o,e[o]]),a&&delete e[o]);for(n.sort((function(e,a){return t(e[1],a[1])})),i=0;i<n.length;i++)r[n[i][0]]=n[i][1];return a||r},exports.uksort=function(e,t){var a,n={},o=[],i=0,r="",s={};for(r in"string"==typeof t&&(t=this.window[t]),e)e.hasOwnProperty(r)&&o.push(r);try{t?o.sort(t):o.sort()}catch(e){return!1}for(this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},s=(a=this.php_js.ini["phpjs.strictForIn"]&&this.php_js.ini["phpjs.strictForIn"].local_value&&"off"!==this.php_js.ini["phpjs.strictForIn"].local_value)?e:s,i=0;i<o.length;i++)n[r=o[i]]=e[r],a&&delete e[r];for(i in n)n.hasOwnProperty(i)&&(s[i]=n[i]);return a||s},exports.usort=function(e,t){var a,n=[],o="",i=0,r={};for(o in"string"==typeof t?t=this[t]:"[object Array]"===Object.prototype.toString.call(t)&&(t=this[t[0]][t[1]]),this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},r=(a=this.php_js.ini["phpjs.strictForIn"]&&this.php_js.ini["phpjs.strictForIn"].local_value&&"off"!==this.php_js.ini["phpjs.strictForIn"].local_value)?e:r,e)e.hasOwnProperty(o)&&(n.push(e[o]),a&&delete e[o]);try{n.sort(t)}catch(e){return!1}for(i=0;i<n.length;i++)r[i]=n[i];return a||r},exports.checkdate=function(e,t,a){return e>0&&e<13&&a>0&&a<32768&&t>0&&t<=new Date(a,e,0).getDate()},exports.date=function(e,t){var a,n,o=["Sun","Mon","Tues","Wednes","Thurs","Fri","Satur","January","February","March","April","May","June","July","August","September","October","November","December"],i=/\\?(.?)/gi,r=function(e,t){return n[e]?n[e]():t},s=function(e,t){for(e=String(e);e.length<t;)e="0"+e;return e};return n={d:function(){return s(n.j(),2)},D:function(){return n.l().slice(0,3)},j:function(){return a.getDate()},l:function(){return o[n.w()]+"day"},N:function(){return n.w()||7},S:function(){var e=n.j(),t=e%10;return t<=3&&1==parseInt(e%100/10,10)&&(t=0),["st","nd","rd"][t-1]||"th"},w:function(){return a.getDay()},z:function(){var e=new Date(n.Y(),n.n()-1,n.j()),t=new Date(n.Y(),0,1);return Math.round((e-t)/864e5)},W:function(){var e=new Date(n.Y(),n.n()-1,n.j()-n.N()+3),t=new Date(e.getFullYear(),0,4);return s(1+Math.round((e-t)/864e5/7),2)},F:function(){return o[6+n.n()]},m:function(){return s(n.n(),2)},M:function(){return n.F().slice(0,3)},n:function(){return a.getMonth()+1},t:function(){return new Date(n.Y(),n.n(),0).getDate()},L:function(){var e=n.Y();return e%4==0&e%100!=0|e%400==0},o:function(){var e=n.n(),t=n.W();return n.Y()+(12===e&&t<9?1:1===e&&t>9?-1:0)},Y:function(){return a.getFullYear()},y:function(){return n.Y().toString().slice(-2)},a:function(){return a.getHours()>11?"pm":"am"},A:function(){return n.a().toUpperCase()},B:function(){var e=3600*a.getUTCHours(),t=60*a.getUTCMinutes(),n=a.getUTCSeconds();return s(Math.floor((e+t+n+3600)/86.4)%1e3,3)},g:function(){return n.G()%12||12},G:function(){return a.getHours()},h:function(){return s(n.g(),2)},H:function(){return s(n.G(),2)},i:function(){return s(a.getMinutes(),2)},s:function(){return s(a.getSeconds(),2)},u:function(){return s(1e3*a.getMilliseconds(),6)},e:function(){throw"Not supported (see source code of date() for timezone on how to add support)"},I:function(){return new Date(n.Y(),0)-Date.UTC(n.Y(),0)!=new Date(n.Y(),6)-Date.UTC(n.Y(),6)?1:0},O:function(){var e=a.getTimezoneOffset(),t=Math.abs(e);return(e>0?"-":"+")+s(100*Math.floor(t/60)+t%60,4)},P:function(){var e=n.O();return e.substr(0,3)+":"+e.substr(3,2)},T:function(){return"UTC"},Z:function(){return 60*-a.getTimezoneOffset()},c:function(){return"Y-m-d\\TH:i:sP".replace(i,r)},r:function(){return"D, d M Y H:i:s O".replace(i,r)},U:function(){return a/1e3|0}},this.date=function(e,t){return this,a=void 0===t?new Date:t instanceof Date?new Date(t):new Date(1e3*t),e.replace(i,r)},this.date(e,t)},exports.getdate=function(e){var t=void 0===e?new Date:"object"==typeof e?new Date(e):new Date(1e3*e),a=t.getDay(),n=t.getMonth(),o=t.getFullYear(),i={};return i.seconds=t.getSeconds(),i.minutes=t.getMinutes(),i.hours=t.getHours(),i.mday=t.getDate(),i.wday=a,i.mon=n+1,i.year=o,i.yday=Math.floor((t-new Date(o,0,1))/864e5),i.weekday=["Sun","Mon","Tues","Wednes","Thurs","Fri","Satur"][a]+"day",i.month=["January","February","March","April","May","June","July","August","September","October","November","December"][n],i[0]=parseInt(t.getTime()/1e3,10),i},exports.gettimeofday=function(e){var t,a=new Date;return e?a.getTime()/1e3:(t=a.getFullYear(),{sec:a.getUTCSeconds(),usec:1e3*a.getUTCMilliseconds(),minuteswest:a.getTimezoneOffset(),dsttime:0+(new Date(t,0)-Date.UTC(t,0)!=new Date(t,6)-Date.UTC(t,6))})},exports.gmmktime=function(){var e=new Date,t=arguments,a=0,n=["Hours","Minutes","Seconds","Month","Date","FullYear"];for(a=0;a<n.length;a++)if(void 0===t[a])t[a]=e["getUTC"+n[a]](),t[a]+=3===a;else if(t[a]=parseInt(t[a],10),isNaN(t[a]))return!1;return t[5]+=t[5]>=0?t[5]<=69?2e3:t[5]<=100?1900:0:0,e.setUTCFullYear(t[5],t[3]-1,t[4]),e.setUTCHours(t[0],t[1],t[2]),(e.getTime()/1e3>>0)-(e.getTime()<0)},exports.idate=function(e,t){if(void 0===e)throw"idate() expects at least 1 parameter, 0 given";if(!e.length||e.length>1)throw"idate format is one char";var a,n=void 0===t?new Date:t instanceof Date?new Date(t):new Date(1e3*t);switch(e){case"B":return Math.floor((3600*n.getUTCHours()+60*n.getUTCMinutes()+n.getUTCSeconds()+3600)/86.4)%1e3;case"d":return n.getDate();case"h":return n.getHours()%12||12;case"H":return n.getHours();case"i":return n.getMinutes();case"I":return a=n.getFullYear(),0+(new Date(a,0)-Date.UTC(a,0)!=new Date(a,6)-Date.UTC(a,6));case"L":return 3&(a=n.getFullYear())||!(a%100)&&a%400?0:1;case"m":return n.getMonth()+1;case"s":return n.getSeconds();case"t":return new Date(n.getFullYear(),n.getMonth()+1,0).getDate();case"U":return Math.round(n.getTime()/1e3);case"w":return n.getDay();case"W":return a=new Date(n.getFullYear(),n.getMonth(),n.getDate()-(n.getDay()||7)+3),1+Math.round((a-new Date(a.getFullYear(),0,4))/864e5/7);case"y":return parseInt((n.getFullYear()+"").slice(2),10);case"Y":return n.getFullYear();case"z":return Math.floor((n-new Date(n.getFullYear(),0,1))/864e5);case"Z":return 60*-n.getTimezoneOffset();default:throw"Unrecognized date format token"}},exports.microtime=function(e){var t=(new Date).getTime()/1e3,a=parseInt(t,10);return e?t:Math.round(1e3*(t-a))/1e3+" "+a},exports.mktime=function(){var e=new Date,t=arguments,a=0,n=["Hours","Minutes","Seconds","Month","Date","FullYear"];for(a=0;a<n.length;a++)if(void 0===t[a])t[a]=e["get"+n[a]](),t[a]+=3===a;else if(t[a]=parseInt(t[a],10),isNaN(t[a]))return!1;return t[5]+=t[5]>=0?t[5]<=69?2e3:t[5]<=100?1900:0:0,e.setFullYear(t[5],t[3]-1,t[4]),e.setHours(t[0],t[1],t[2]),(e.getTime()/1e3>>0)-(e.getTime()<0)},exports.strtotime=function(e,t){var a,n,o,i,r,s,u,c,l,p;if(!e)return!1;if((n=(e=e.replace(/^\s+|\s+$/g,"").replace(/\s{2,}/g," ").replace(/[\t\r\n]/g,"").toLowerCase()).match(/^(\d{1,4})([\-\.\/\:])(\d{1,2})([\-\.\/\:])(\d{1,4})(?:\s(\d{1,2}):(\d{2})?:?(\d{2})?)?(?:\s([A-Z]+)?)?$/))&&n[2]===n[4])if(n[1]>1901)switch(n[2]){case"-":return!(n[3]>12||n[5]>31)&&new Date(n[1],parseInt(n[3],10)-1,n[5],n[6]||0,n[7]||0,n[8]||0,n[9]||0)/1e3;case".":return!1;case"/":return!(n[3]>12||n[5]>31)&&new Date(n[1],parseInt(n[3],10)-1,n[5],n[6]||0,n[7]||0,n[8]||0,n[9]||0)/1e3}else if(n[5]>1901)switch(n[2]){case"-":case".":return!(n[3]>12||n[1]>31)&&new Date(n[5],parseInt(n[3],10)-1,n[1],n[6]||0,n[7]||0,n[8]||0,n[9]||0)/1e3;case"/":return!(n[1]>12||n[3]>31)&&new Date(n[5],parseInt(n[1],10)-1,n[3],n[6]||0,n[7]||0,n[8]||0,n[9]||0)/1e3}else switch(n[2]){case"-":return!(n[3]>12||n[5]>31||n[1]<70&&n[1]>38)&&(i=n[1]>=0&&n[1]<=38?+n[1]+2e3:n[1],new Date(i,parseInt(n[3],10)-1,n[5],n[6]||0,n[7]||0,n[8]||0,n[9]||0)/1e3);case".":return n[5]>=70?!(n[3]>12||n[1]>31)&&new Date(n[5],parseInt(n[3],10)-1,n[1],n[6]||0,n[7]||0,n[8]||0,n[9]||0)/1e3:n[5]<60&&!n[6]&&(!(n[1]>23||n[3]>59)&&(o=new Date,new Date(o.getFullYear(),o.getMonth(),o.getDate(),n[1]||0,n[3]||0,n[5]||0,n[9]||0)/1e3));case"/":return!(n[1]>12||n[3]>31||n[5]<70&&n[5]>38)&&(i=n[5]>=0&&n[5]<=38?+n[5]+2e3:n[5],new Date(i,parseInt(n[1],10)-1,n[3],n[6]||0,n[7]||0,n[8]||0,n[9]||0)/1e3);case":":return!(n[1]>23||n[3]>59||n[5]>59)&&(o=new Date,new Date(o.getFullYear(),o.getMonth(),o.getDate(),n[1]||0,n[3]||0,n[5]||0)/1e3)}if("now"===e)return null===t||isNaN(t)?(new Date).getTime()/1e3|0:0|t;if(!isNaN(a=Date.parse(e)))return a/1e3|0;function m(e){var t=e.split(" "),a=t[0],n=t[1].substring(0,3),o=/\d+/.test(a),i=("last"===a?-1:1)*("ago"===t[2]?-1:1);if(o&&(i*=parseInt(a,10)),u.hasOwnProperty(n)&&!t[1].match(/^mon(day|\.)?$/i))return r["set"+u[n]](r["get"+u[n]]()+i);if("wee"===n)return r.setDate(r.getDate()+7*i);if("next"===a||"last"===a)!function(e,t,a){var n,o=s[t];void 0!==o&&(0===(n=o-r.getDay())?n=7*a:n>0&&"last"===e?n-=7:n<0&&"next"===e&&(n+=7),r.setDate(r.getDate()+n))}(a,n,i);else if(!o)return!1;return!0}if(r=t?new Date(1e3*t):new Date,s={sun:0,mon:1,tue:2,wed:3,thu:4,fri:5,sat:6},u={yea:"FullYear",mon:"Month",day:"Date",hou:"Hours",min:"Minutes",sec:"Seconds"},"([+-]?\\d+\\s"+(l="(years?|months?|weeks?|days?|hours?|minutes?|min|seconds?|sec|sunday|sun\\.?|monday|mon\\.?|tuesday|tue\\.?|wednesday|wed\\.?|thursday|thu\\.?|friday|fri\\.?|saturday|sat\\.?)")+"|(last|next)\\s"+l+")(\\sago)?",!(n=e.match(new RegExp("([+-]?\\d+\\s(years?|months?|weeks?|days?|hours?|minutes?|min|seconds?|sec|sunday|sun\\.?|monday|mon\\.?|tuesday|tue\\.?|wednesday|wed\\.?|thursday|thu\\.?|friday|fri\\.?|saturday|sat\\.?)|(last|next)\\s(years?|months?|weeks?|days?|hours?|minutes?|min|seconds?|sec|sunday|sun\\.?|monday|mon\\.?|tuesday|tue\\.?|wednesday|wed\\.?|thursday|thu\\.?|friday|fri\\.?|saturday|sat\\.?))(\\sago)?","gi"))))return!1;for(p=0,c=n.length;p<c;p++)if(!m(n[p]))return!1;return r.getTime()/1e3},exports.time=function(){return Math.floor((new Date).getTime()/1e3)},exports.escapeshellarg=function(e){return"'"+e.replace(/[^\\]'/g,(function(e,t,a){return e.slice(0,1)+"\\'"}))+"'"},exports.basename=function(e,t){var a=e,n=a.charAt(a.length-1);return"/"!==n&&"\\"!==n||(a=a.slice(0,-1)),a=a.replace(/^.*[\/\\]/g,""),"string"==typeof t&&a.substr(a.length-t.length)==t&&(a=a.substr(0,a.length-t.length)),a},exports.dirname=function(e){return e.replace(/\\/g,"/").replace(/\/[^\/]*\/?$/,"")},exports.file_get_contents=function(e,t,a,n,o){var i,r=[],s=[],u=0,c=0,l="",p=-1,m=0,f=null,h=!1;this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{};var d=this.php_js.ini;a=a||this.php_js.default_streams_context||null,t||(t=0);var g,b={FILE_USE_INCLUDE_PATH:1,FILE_TEXT:32,FILE_BINARY:64};if("number"==typeof t)m=t;else for(t=[].concat(t),c=0;c<t.length;c++)b[t[c]]&&(m|=b[t[c]]);if(m&b.FILE_BINARY&&m&b.FILE_TEXT)throw"You cannot pass both FILE_BINARY and FILE_TEXT to file_get_contents()";if(m&b.FILE_USE_INCLUDE_PATH&&d.include_path&&d.include_path.local_value){var k=-1!==d.include_path.local_value.indexOf("/")?"/":"\\";e=d.include_path.local_value+k+e}else/^(https?|file):/.test(e)||(l=this.window.location.href,p=0===e.indexOf("/")?l.indexOf("/",8)-1:l.lastIndexOf("/"),e=l.slice(0,p+1)+e);if(a&&(h=!!(g=a.stream_options&&a.stream_options.http)),!a||h){var v=this.window.ActiveXObject?new ActiveXObject("Microsoft.XMLHTTP"):new XMLHttpRequest;if(!v)throw new Error("XMLHttpRequest not supported");var y=h?g.method:"GET",j=!!(a&&a.stream_params&&a.stream_params["phpjs.async"]);if(d["phpjs.ajaxBypassCache"]&&d["phpjs.ajaxBypassCache"].local_value&&(e+=(null==e.match(/\?/)?"?":"&")+(new Date).getTime()),v.open(y,e,j),j){var w=a.stream_params.notification;"function"==typeof w&&(v.onreadystatechange=function(e){var t,a={responseText:v.responseText,responseXML:v.responseXML,status:v.status,statusText:v.statusText,readyState:v.readyState,evt:e};switch(v.readyState){case 0:case 1:case 2:w.call(a,0,0,"",0,0,0);break;case 3:t=2*v.responseText.length,w.call(a,7,0,"",0,t,0);break;case 4:v.status>=200&&v.status<400?(t=2*v.responseText.length,w.call(a,8,0,"",v.status,t,0)):403===v.status?w.call(a,10,2,"",v.status,0,0):w.call(a,9,2,"",v.status,0,0);break;default:throw"Unrecognized ready state for file_get_contents()"}})}if(h){var _=g.header&&g.header.split(/\r?\n/),A=!1;for(c=0;c<_.length;c++){var x=_[c],z=x.search(/:\s*/),S=x.substring(0,z);v.setRequestHeader(S,x.substring(z+1)),"User-Agent"===S&&(A=!0)}if(!A){var C=g.user_agent||d.user_agent&&d.user_agent.local_value;C&&v.setRequestHeader("User-Agent",C)}f=g.content||null}if(m&b.FILE_TEXT){var E="text/html";if(g&&g["phpjs.override"])E=g["phpjs.override"];else{var T=d["unicode.stream_encoding"]&&d["unicode.stream_encoding"].local_value||"UTF-8";g&&g.header&&/^content-type:/im.test(g.header)&&(E=g.header.match(/^content-type:\s*(.*)$/im)[1]),/;\s*charset=/.test(E)||(E+="; charset="+T)}v.overrideMimeType(E)}else m&b.FILE_BINARY&&v.overrideMimeType("text/plain; charset=x-user-defined");try{g&&g["phpjs.sendAsBinary"]?v.sendAsBinary(f):v.send(f)}catch(e){return!1}if(i=v.getAllResponseHeaders()){for(i=i.split("\n"),u=0;u<i.length;u++)""!==i[u].substring(1)&&s.push(i[u]);for(i=s,c=0;c<i.length;c++)r[c]=i[c];this.$http_response_header=r}return n||o?o?v.responseText.substr(n||0,o):v.responseText.substr(n):v.responseText}return!1},exports.realpath=function(e){var t,a=0,n=this.window.location.href;for(var o in-1!==(e=(e+"").replace("\\","/")).indexOf("://")&&(a=1),a||(e=n.substring(0,n.lastIndexOf("/")+1)+e),t=e.split("/"),e=[],t)"."!=t[o]&&(".."==t[o]?e.length>3&&e.pop():(e.length<2||""!==t[o])&&e.push(t[o]));return e.join("/")},exports.call_user_func=function(cb){var func;if("string"==typeof cb?func="function"==typeof this[cb]?this[cb]:func=new Function(null,"return "+cb)():"[object Array]"===Object.prototype.toString.call(cb)?func="string"==typeof cb[0]?eval(cb[0]+"['"+cb[1]+"']"):func=cb[0][cb[1]]:"function"==typeof cb&&(func=cb),"function"!=typeof func)throw new Error(func+" is not a valid function");var parameters=Array.prototype.slice.call(arguments,1);return"string"==typeof cb[0]?func.apply(eval(cb[0]),parameters):"object"!=typeof cb[0]?func.apply(null,parameters):func.apply(cb[0],parameters)},exports.call_user_func_array=function(cb,parameters){var func;if("string"==typeof cb?func="function"==typeof this[cb]?this[cb]:func=new Function(null,"return "+cb)():"[object Array]"===Object.prototype.toString.call(cb)?func="string"==typeof cb[0]?eval(cb[0]+"['"+cb[1]+"']"):func=cb[0][cb[1]]:"function"==typeof cb&&(func=cb),"function"!=typeof func)throw new Error(func+" is not a valid function");return"string"==typeof cb[0]?func.apply(eval(cb[0]),parameters):"object"!=typeof cb[0]?func.apply(null,parameters):func.apply(cb[0],parameters)},exports.create_function=function(e,t){try{return Function.apply(null,e.split(",").concat(t))}catch(e){return!1}},exports.function_exists=function(e){return"string"==typeof e&&(e=this.window[e]),"function"==typeof e},exports.get_defined_functions=function(){var e="",t=[],a={};for(e in this.window)try{if("function"==typeof this.window[e])a[e]||(a[e]=1,t.push(e));else if("object"==typeof this.window[e])for(var n in this.window[e])"function"==typeof this.window[n]&&this.window[n]&&!a[n]&&(a[n]=1,t.push(n))}catch(e){}return t},exports.i18n_loc_set_default=function(e){return this.php_js=this.php_js||{},this.php_js.i18nLocales={en_US_POSIX:{sorting:function(e,t){return e==t?0:e>t?1:-1}}},this.php_js.i18nLocale=e,!0},exports.assert_options=function(e,t){var a,n;switch(this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},this.php_js.assert_values=this.php_js.assert_values||{},e){case"ASSERT_ACTIVE":a="assert.active",n=1;break;case"ASSERT_WARNING":throw a="assert.warning",n=1,"We have not yet implemented warnings for us to throw in JavaScript (assert_options())";case"ASSERT_BAIL":a="assert.bail",n=0;break;case"ASSERT_QUIET_EVAL":a="assert.quiet_eval",n=0;break;case"ASSERT_CALLBACK":a="assert.callback",n=null;break;default:throw"Improper type for assert_options()"}var o=this.php_js.assert_values[a]||this.php_js.ini[a]&&this.php_js.ini[a].local_value||n;return t&&(this.php_js.assert_values[a]=t),o},exports.getenv=function(e){return!!(this.php_js&&this.php_js.ENV&&this.php_js.ENV[e])&&this.php_js.ENV[e]},exports.getlastmod=function(){return new Date(this.window.document.lastModified).getTime()/1e3},exports.ini_get=function(e){return this.php_js&&this.php_js.ini&&this.php_js.ini[e]&&void 0!==this.php_js.ini[e].local_value?null===this.php_js.ini[e].local_value?"":this.php_js.ini[e].local_value:""},exports.ini_set=function(e,t){var a,n=this;try{this.php_js=this.php_js||{}}catch(e){this.php_js={}}this.php_js.ini=this.php_js.ini||{},this.php_js.ini[e]=this.php_js.ini[e]||{},a=this.php_js.ini[e].local_value;switch(e){case"extension":"function"==typeof this.dl&&this.dl(t),function(a){void 0===a&&(n.php_js.ini[e].local_value=[]),n.php_js.ini[e].local_value.push(t)}(a);break;default:this.php_js.ini[e].local_value=t}return a},exports.set_time_limit=function(e){this.php_js=this.php_js||{},this.window.setTimeout((function(){throw this.php_js.timeoutStatus||(this.php_js.timeoutStatus=!0),"Maximum execution time exceeded"}),1e3*e)},exports.version_compare=function(e,t,a){this.php_js=this.php_js||{},this.php_js.ENV=this.php_js.ENV||{};var n,o=0,i=0,r={dev:-6,alpha:-5,a:-5,beta:-4,b:-4,RC:-3,rc:-3,"#":-2,p:1,pl:1},s=function(e){return(e=(e=(""+e).replace(/[_\-+]/g,".")).replace(/([^.\d]+)/g,".$1.").replace(/\.{2,}/g,".")).length?e.split("."):[-8]};for(numVersion=function(e){return e?isNaN(e)?r[e]||-7:parseInt(e,10):0},e=s(e),t=s(t),n=Math.max(e.length,t.length),o=0;o<n;o++)if(e[o]!=t[o]){if(e[o]=numVersion(e[o]),t[o]=numVersion(t[o]),e[o]<t[o]){i=-1;break}if(e[o]>t[o]){i=1;break}}if(!a)return i;switch(a){case">":case"gt":return i>0;case">=":case"ge":return i>=0;case"<=":case"le":return i<=0;case"==":case"=":case"eq":return 0===i;case"<>":case"!=":case"ne":return 0!==i;case"":case"<":case"lt":return i<0;default:return null}},exports.json_decode=function(str_json){var json=this.window.JSON;if("object"==typeof json&&"function"==typeof json.parse)try{return json.parse(str_json)}catch(e){if(!(e instanceof SyntaxError))throw new Error("Unexpected error type in json_decode()");return this.php_js=this.php_js||{},this.php_js.last_error_json=4,null}var cx=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,j,text=str_json;return cx.lastIndex=0,cx.test(text)&&(text=text.replace(cx,(function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)}))),/^[\],:{}\s]*$/.test(text.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,""))?(j=eval("("+text+")"),j):(this.php_js=this.php_js||{},this.php_js.last_error_json=4,null)},exports.json_encode=function(e){var t,a=this.window.JSON;try{if("object"==typeof a&&"function"==typeof a.stringify){if(void 0===(t=a.stringify(e)))throw new SyntaxError("json_encode");return t}var n=function(e){var t=/[\\\"\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,a={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};return t.lastIndex=0,t.test(e)?'"'+e.replace(t,(function(e){var t=a[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)}))+'"':'"'+e+'"'},o=function(e,t){var a="",i=0,r="",s="",u=0,c=a,l=[],p=t[e];switch(p&&"object"==typeof p&&"function"==typeof p.toJSON&&(p=p.toJSON(e)),typeof p){case"string":return n(p);case"number":return isFinite(p)?String(p):"null";case"boolean":case"null":return String(p);case"object":if(!p)return"null";if(this.PHPJS_Resource&&p instanceof this.PHPJS_Resource||window.PHPJS_Resource&&p instanceof window.PHPJS_Resource)throw new SyntaxError("json_encode");if(a+="    ",l=[],"[object Array]"===Object.prototype.toString.apply(p)){for(u=p.length,i=0;i<u;i+=1)l[i]=o(i,p)||"null";return s=0===l.length?"[]":a?"[\n"+a+l.join(",\n"+a)+"\n"+c+"]":"["+l.join(",")+"]",a=c,s}for(r in p)Object.hasOwnProperty.call(p,r)&&(s=o(r,p))&&l.push(n(r)+(a?": ":":")+s);return s=0===l.length?"{}":a?"{\n"+a+l.join(",\n"+a)+"\n"+c+"}":"{"+l.join(",")+"}",a=c,s;case"undefined":case"function":default:throw new SyntaxError("json_encode")}};return o("",{"":e})}catch(e){if(!(e instanceof SyntaxError))throw new Error("Unexpected error type in json_encode()");return this.php_js=this.php_js||{},this.php_js.last_error_json=4,null}},exports.json_last_error=function(){return this.php_js&&this.php_js.last_error_json?this.php_js.last_error_json:0},exports.abs=function(e){return Math.abs(e)||0},exports.acos=function(e){return Math.acos(e)},exports.acosh=function(e){return Math.log(e+Math.sqrt(e*e-1))},exports.asin=function(e){return Math.asin(e)},exports.asinh=function(e){return Math.log(e+Math.sqrt(e*e+1))},exports.atan=function(e){return Math.atan(e)},exports.atan2=function(e,t){return Math.atan2(e,t)},exports.atanh=function(e){return.5*Math.log((1+e)/(1-e))},exports.base_convert=function(e,t,a){return parseInt(e+"",0|t).toString(0|a)},exports.bindec=function(e){return e=(e+"").replace(/[^01]/gi,""),parseInt(e,2)},exports.ceil=function(e){return Math.ceil(e)},exports.cos=function(e){return Math.cos(e)},exports.cosh=function(e){return(Math.exp(e)+Math.exp(-e))/2},exports.decbin=function(e){return e<0&&(e=4294967295+e+1),parseInt(e,10).toString(2)},exports.dechex=function(e){return e<0&&(e=4294967295+e+1),parseInt(e,10).toString(16)},exports.decoct=function(e){return e<0&&(e=4294967295+e+1),parseInt(e,10).toString(8)},exports.deg2rad=function(e){return.017453292519943295*e},exports.exp=function(e){return Math.exp(e)},exports.expm1=function(e){for(var t=0,a=function e(t){return 0===t||1===t?1:t*e(t-1)},n=1;n<50;n++)t+=Math.pow(e,n)/a(n);return t},exports.floor=function(e){return Math.floor(e)},exports.fmod=function(e,t){var a,n,o,i=0,r=0,s=0;return a=e.toExponential().match(/^.\.?(.*)e(.+)$/),i=parseInt(a[2],10)-(a[1]+"").length,a=t.toExponential().match(/^.\.?(.*)e(.+)$/),(o=parseInt(a[2],10)-(a[1]+"").length)>i&&(i=o),n=e%t,i<-100||i>20?(r=Math.round(Math.log(n)/Math.log(10)),(n/(s=Math.pow(10,r))).toFixed(r-i)*s):parseFloat(n.toFixed(-i))},exports.getrandmax=function(){return 2147483647},exports.hexdec=function(e){return e=(e+"").replace(/[^a-f0-9]/gi,""),parseInt(e,16)},exports.hypot=function(e,t){return Math.sqrt(e*e+t*t)||0},exports.is_finite=function(e){var t="";if(e===1/0||e===-1/0)return!1;if("object"==typeof e?t="[object Array]"===Object.prototype.toString.call(e)?"array":"object":"string"!=typeof e||e.match(/^[\+\-]?\d/)||(t="string"),t)throw new Error("Warning: is_finite() expects parameter 1 to be double, "+t+" given");return!0},exports.is_infinite=function(e){var t="";if(e===1/0||e===-1/0)return!0;if("object"==typeof e?t="[object Array]"===Object.prototype.toString.call(e)?"array":"object":"string"!=typeof e||e.match(/^[\+\-]?\d/)||(t="string"),t)throw new Error("Warning: is_infinite() expects parameter 1 to be double, "+t+" given");return!1},exports.is_nan=function(e){var t="";if("number"==typeof e&&isNaN(e))return!0;if("object"==typeof e?t="[object Array]"===Object.prototype.toString.call(e)?"array":"object":"string"!=typeof e||e.match(/^[\+\-]?\d/)||(t="string"),t)throw new Error("Warning: is_nan() expects parameter 1 to be double, "+t+" given");return!1},exports.lcg_value=function(){return Math.random()},exports.log=function(e,t){return void 0===t?Math.log(e):Math.log(e)/Math.log(t)},exports.log10=function(e){return Math.log(e)/2.302585092994046},exports.log1p=function(e){var t=0;if(e<=-1)return"-INF";if(e<0||e>1)return Math.log(1+e);for(var a=1;a<50;a++)a%2==0?t-=Math.pow(e,a)/a:t+=Math.pow(e,a)/a;return t},exports.max=function(){var e,t,a=0,n=0,o=arguments,i=o.length,r=function(e){if("[object Array]"===Object.prototype.toString.call(e))return e;var t=[];for(var a in e)e.hasOwnProperty(a)&&t.push(e[a]);return t};if(_compare=function(e,t){var a=0,n=0,o=0,i=0,s=0;if(e===t)return 0;if("object"==typeof e){if("object"==typeof t){if(e=r(e),t=r(t),s=e.length,(i=t.length)>s)return 1;if(i<s)return-1;for(a=0,n=s;a<n;++a){if(1==(o=_compare(e[a],t[a])))return 1;if(-1==o)return-1}return 0}return-1}return"object"==typeof t?1:isNaN(t)&&!isNaN(e)?0==e?0:e<0?1:-1:isNaN(e)&&!isNaN(t)?0==t?0:t>0?1:-1:t==e?0:t>e?1:-1},0===i)throw new Error("At least one value should be passed to max()");if(1===i){if("object"!=typeof o[0])throw new Error("Wrong parameter count for max()");if(0===(e=r(o[0])).length)throw new Error("Array must contain at least one element for max()")}else e=o;for(t=e[0],a=1,n=e.length;a<n;++a)1==_compare(t,e[a])&&(t=e[a]);return t},exports.min=function(){var e,t,a=0,n=0,o=arguments,i=o.length,r=function(e){if("[object Array]"===Object.prototype.toString.call(e))return e;var t=[];for(var a in e)e.hasOwnProperty(a)&&t.push(e[a]);return t};if(_compare=function(e,t){var a=0,n=0,o=0,i=0,s=0;if(e===t)return 0;if("object"==typeof e){if("object"==typeof t){if(e=r(e),t=r(t),s=e.length,(i=t.length)>s)return 1;if(i<s)return-1;for(a=0,n=s;a<n;++a){if(1==(o=_compare(e[a],t[a])))return 1;if(-1==o)return-1}return 0}return-1}return"object"==typeof t?1:isNaN(t)&&!isNaN(e)?0==e?0:e<0?1:-1:isNaN(e)&&!isNaN(t)?0==t?0:t>0?1:-1:t==e?0:t>e?1:-1},0===i)throw new Error("At least one value should be passed to min()");if(1===i){if("object"!=typeof o[0])throw new Error("Wrong parameter count for min()");if(0===(e=r(o[0])).length)throw new Error("Array must contain at least one element for min()")}else e=o;for(t=e[0],a=1,n=e.length;a<n;++a)-1==_compare(t,e[a])&&(t=e[a]);return t},exports.mt_getrandmax=function(){return 2147483647},exports.mt_rand=function(e,t){var a=arguments.length;if(0===a)e=0,t=2147483647;else{if(1===a)throw new Error("Warning: mt_rand() expects exactly 2 parameters, 1 given");e=parseInt(e,10),t=parseInt(t,10)}return Math.floor(Math.random()*(t-e+1))+e},exports.octdec=function(e){return e=(e+"").replace(/[^0-7]/gi,""),parseInt(e,8)},exports.pi=function(){return 3.141592653589793},exports.pow=function(e,t){return Math.pow(e,t)},exports.rad2deg=function(e){return 57.29577951308232*e},exports.rand=function(e,t){var a=arguments.length;if(0===a)e=0,t=2147483647;else if(1===a)throw new Error("Warning: rand() expects exactly 2 parameters, 1 given");return Math.floor(Math.random()*(t-e+1))+e},exports.round=function(e,t,a){var n,o,i,r;if(t|=0,i=(e*=n=Math.pow(10,t))%1==.5*(r=e>0|-(e<0)),o=Math.floor(e),i)switch(a){case"PHP_ROUND_HALF_DOWN":e=o+(r<0);break;case"PHP_ROUND_HALF_EVEN":e=o+o%2*r;break;case"PHP_ROUND_HALF_ODD":e=o+!(o%2);break;default:e=o+(r>0)}return(i?e:Math.round(e))/n},exports.sin=function(e){return Math.sin(e)},exports.sinh=function(e){return(Math.exp(e)-Math.exp(-e))/2},exports.sqrt=function(e){return Math.sqrt(e)},exports.tan=function(e){return Math.tan(e)},exports.tanh=function(e){return(Math.exp(e)-Math.exp(-e))/(Math.exp(e)+Math.exp(-e))},exports.pack=function(e){for(var t,a,n,o,i,r,s,u,c,l,p,m,f,h,d,g,b,k,v,y,j,w,_=0,A=1,x="",z="",S=0,C=[];_<e.length;){for(t=e.charAt(_),a="",_++;_<e.length&&null!==e.charAt(_).match(/[\d\*]/);)a+=e.charAt(_),_++;switch(""===a&&(a="1"),t){case"a":case"A":if(void 0===arguments[A])throw new Error("Warning:  pack() Type "+t+": not enough arguments");for(z=String(arguments[A]),"*"===a&&(a=z.length),S=0;S<a;S++)void 0===z[S]?x+="a"===t?String.fromCharCode(0):" ":x+=z[S];A++;break;case"h":case"H":if(void 0===arguments[A])throw new Error("Warning: pack() Type "+t+": not enough arguments");if(z=arguments[A],"*"===a&&(a=z.length),a>z.length)throw new Error("Warning: pack() Type "+t+": not enough characters in string");for(S=0;S<a;S+=2)n=z[S],S+1>=a||void 0===z[S+1]?n+="0":n+=z[S+1],"h"===t&&(n=n[1]+n[0]),x+=String.fromCharCode(parseInt(n,16));A++;break;case"c":case"C":if("*"===a&&(a=arguments.length-A),a>arguments.length-A)throw new Error("Warning:  pack() Type "+t+": too few arguments");for(S=0;S<a;S++)x+=String.fromCharCode(arguments[A]),A++;break;case"s":case"S":case"v":if("*"===a&&(a=arguments.length-A),a>arguments.length-A)throw new Error("Warning:  pack() Type "+t+": too few arguments");for(S=0;S<a;S++)x+=String.fromCharCode(255&arguments[A]),x+=String.fromCharCode(arguments[A]>>8&255),A++;break;case"n":if("*"===a&&(a=arguments.length-A),a>arguments.length-A)throw new Error("Warning: pack() Type "+t+": too few arguments");for(S=0;S<a;S++)x+=String.fromCharCode(255&arguments[A]),A++;break;case"i":case"I":case"l":case"L":case"V":if("*"===a&&(a=arguments.length-A),a>arguments.length-A)throw new Error("Warning:  pack() Type "+t+": too few arguments");for(S=0;S<a;S++)x+=String.fromCharCode(255&arguments[A]),x+=String.fromCharCode(arguments[A]>>8&255),x+=String.fromCharCode(arguments[A]>>16&255),x+=String.fromCharCode(arguments[A]>>24&255),A++;break;case"N":if("*"===a&&(a=arguments.length-A),a>arguments.length-A)throw new Error("Warning:  pack() Type "+t+": too few arguments");for(S=0;S<a;S++)x+=String.fromCharCode(arguments[A]>>24&255),x+=String.fromCharCode(arguments[A]>>16&255),x+=String.fromCharCode(arguments[A]>>8&255),x+=String.fromCharCode(255&arguments[A]),A++;break;case"f":case"d":if(o=23,i=8,"d"===t&&(o=52,i=11),"*"===a&&(a=arguments.length-A),a>arguments.length-A)throw new Error("Warning:  pack() Type "+t+": too few arguments");for(S=0;S<a;S++){for(z=arguments[A],c=s=Math.pow(2,i-1)-1,(u=1-s)-o,l=isNaN(d=parseFloat(z))||d===-1/0||d===1/0?d:0,p=0,m=2*s+1+o+3,f=new Array(m),h=(d=0!==l?0:d)<0,b=(d=Math.abs(d))-(g=Math.floor(d)),j=m;j;)f[--j]=0;for(j=s+2;g&&j;)f[--j]=g%2,g=Math.floor(g/2);for(j=s+1;b>0&&j;--b)f[++j]=((b*=2)>=1)-0;for(j=-1;++j<m&&!f[j];);if(f[(k=o-1+(j=(p=s+1-j)>=u&&p<=c?j+1:s+1-(p=u-1)))+1]){if(!(v=f[k]))for(y=k+2;!v&&y<m;v=f[y++]);for(y=k+1;v&&--y>=0;(f[y]=!f[y]-0)&&(v=0));}for(j=j-2<0?-1:j-3;++j<m&&!f[j];);for((p=s+1-j)>=u&&p<=c?++j:p<u&&(j=s+1-(p=u-1)),(g||0!==l)&&(p=c+1,j=s+2,l===-1/0?h=1:isNaN(l)&&(f[j]=1)),d=Math.abs(p+s),w="",y=i+1;--y;)w=d%2+w,d=d>>=1;for(d=0,y=0,j=(w=(h?"1":"0")+w+f.slice(j,j+o).join("")).length,C=[];j;)d+=(1<<y)*w.charAt(--j),7===y&&(C[C.length]=String.fromCharCode(d),d=0),y=(y+1)%8;C[C.length]=d?String.fromCharCode(d):"",x+=C.join(""),A++}break;case"x":if("*"===a)throw new Error("Warning: pack(): Type x: '*' ignored");for(S=0;S<a;S++)x+=String.fromCharCode(0);break;case"X":if("*"===a)throw new Error("Warning: pack(): Type X: '*' ignored");for(S=0;S<a;S++){if(0===x.length)throw new Error("Warning: pack(): Type X: outside of string");x=x.substring(0,x.length-1)}break;case"@":if("*"===a)throw new Error("Warning: pack(): Type X: '*' ignored");if(a>x.length)for(r=a-x.length,S=0;S<r;S++)x+=String.fromCharCode(0);a<x.length&&(x=x.substring(0,a));break;default:throw new Error("Warning:  pack() Type "+t+": unknown format code")}}if(A<arguments.length)throw new Error("Warning: pack(): "+(arguments.length-A)+" arguments unused");return x},exports.time_sleep_until=function(e){for(;new Date<1e3*e;);return!0},exports.uniqid=function(e,t){var a;void 0===e&&(e="");var n=function(e,t){return t<(e=parseInt(e,10).toString(16)).length?e.slice(e.length-t):t>e.length?Array(t-e.length+1).join("0")+e:e};return this.php_js||(this.php_js={}),this.php_js.uniqidSeed||(this.php_js.uniqidSeed=Math.floor(123456789*Math.random())),this.php_js.uniqidSeed++,a=e,a+=n(parseInt((new Date).getTime()/1e3,10),8),a+=n(this.php_js.uniqidSeed,5),t&&(a+=(10*Math.random()).toFixed(8).toString()),a},exports.gopher_parsedir=function(e){var t=e.match(/^(.)(.*?)\t(.*?)\t(.*?)\t(.*?)\u000d\u000a$/);if(null===t)throw"Could not parse the directory entry";var a=t[1];switch(a){case"i":a=255;break;case"1":a=1;break;case"0":a=0;break;case"4":a=4;break;case"5":a=5;break;case"6":a=6;break;case"9":a=9;break;case"h":a=254;break;default:return{type:-1,data:e}}return{type:a,title:t[2],path:t[3],host:t[4],port:t[5]}},exports.inet_ntop=function(e){var t=0,a="",n=[];if(4===(e+="").length)return[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)].join(".");if(16===e.length){for(t=0;t<16;t++)n.push(((e.charCodeAt(t++)<<8)+e.charCodeAt(t)).toString(16));return n.join(":").replace(/((^|:)0(?=:|$))+:?/g,(function(e){return a=e.length>a.length?e:a,e})).replace(a||" ","::")}return!1},exports.inet_pton=function(e){var t,a,n,o,i,r=String.fromCharCode;if(a=e.match(/^(?:\d{1,3}(?:\.|$)){4}/))return 4===(a=r((a=a[0].split("."))[0])+r(a[1])+r(a[2])+r(a[3])).length&&a;if(t=/^((?:[\da-f]{1,4}(?::|)){0,8})(::)?((?:[\da-f]{1,4}(?::|)){0,8})$/,a=e.match(t)){for(i=1;i<4;i++)if(2!==i&&0!==a[i].length){for(a[i]=a[i].split(":"),o=0;o<a[i].length;o++){if(a[i][o]=parseInt(a[i][o],16),isNaN(a[i][o]))return!1;a[i][o]=r(a[i][o]>>8)+r(255&a[i][o])}a[i]=a[i].join("")}if(16===(n=a[1].length+a[3].length))return a[1]+a[3];if(n<16&&a[2].length>0)return a[1]+new Array(16-n+1).join("\0")+a[3]}return!1},exports.ip2long=function(e){var t=0;if(!(e=e.match(/^([1-9]\d*|0[0-7]*|0x[\da-f]+)(?:\.([1-9]\d*|0[0-7]*|0x[\da-f]+))?(?:\.([1-9]\d*|0[0-7]*|0x[\da-f]+))?(?:\.([1-9]\d*|0[0-7]*|0x[\da-f]+))?$/i)))return!1;for(e[0]=0,t=1;t<5;t+=1)e[0]+=!!(e[t]||"").length,e[t]=parseInt(e[t])||0;return e.push(256,256,256,256),e[4+e[0]]*=Math.pow(256,4-e[0]),!(e[1]>=e[5]||e[2]>=e[6]||e[3]>=e[7]||e[4]>=e[8])&&e[1]*(1===e[0]||16777216)+e[2]*(e[0]<=2||65536)+e[3]*(e[0]<=3||256)+1*e[4]},exports.long2ip=function(e){return!!isFinite(e)&&[e>>>24,e>>>16&255,e>>>8&255,255&e].join(".")},exports.setrawcookie=function(e,t,a,n,o,i){"string"==typeof a&&/^\d+$/.test(a)&&(a=parseInt(a,10)),a instanceof Date?a=a.toGMTString():"number"==typeof a&&(a=new Date(1e3*a).toGMTString());var r=[e+"="+t],s={},u="";for(u in s={expires:a,path:n,domain:o})s.hasOwnProperty(u)&&s[u]&&r.push(u+"="+s[u]);return i&&r.push("secure"),this.window.document.cookie=r.join(";"),!0},exports.preg_grep=function(pattern,input,flags){var p="",retObj={},invert=1===flags||"PREG_GREP_INVERT"===flags;if("string"==typeof pattern&&(pattern=eval(pattern)),invert)for(p in input)-1===(input[p]+"").search(pattern)&&(retObj[p]=input[p]);else for(p in input)-1!==(input[p]+"").search(pattern)&&(retObj[p]=input[p]);return retObj},exports.preg_quote=function(e,t){return String(e).replace(new RegExp("[.\\\\+*?\\[\\^\\]$(){}=!<>|:\\"+(t||"")+"-]","g"),"\\$&")},exports.addcslashes=function(e,t){var a="",n=[],o=0,i=0,r="",s="",u="",c="",l="",p=0,m=0,f=0,h=0,d=0,g=[],b="",k=/%([\dA-Fa-f]+)/g,v=function(e,t){return(e+="").length<t?new Array(++t-e.length).join("0")+e:e};for(o=0;o<t.length;o++)if(r=t.charAt(o),s=t.charAt(o+1),"\\"===r&&s&&/\d/.test(s)){if(h=o+(f=(u=t.slice(o+1).match(/^\d+/)[0]).length)+1,t.charAt(h)+t.charAt(h+1)===".."){if(p=u.charCodeAt(0),/\\\d/.test(t.charAt(h+2)+t.charAt(h+3)))c=t.slice(h+3).match(/^\d+/)[0],o+=1;else{if(!t.charAt(h+2))throw"Range with no end point";c=t.charAt(h+2)}if((m=c.charCodeAt(0))>p)for(i=p;i<=m;i++)n.push(String.fromCharCode(i));else n.push(".",u,c);o+=c.length+2}else l=String.fromCharCode(parseInt(u,8)),n.push(l);o+=f}else if(s+t.charAt(o+2)===".."){if(p=(u=r).charCodeAt(0),/\\\d/.test(t.charAt(o+3)+t.charAt(o+4)))c=t.slice(o+4).match(/^\d+/)[0],o+=1;else{if(!t.charAt(o+3))throw"Range with no end point";c=t.charAt(o+3)}if((m=c.charCodeAt(0))>p)for(i=p;i<=m;i++)n.push(String.fromCharCode(i));else n.push(".",u,c);o+=c.length+2}else n.push(r);for(o=0;o<e.length;o++)if(r=e.charAt(o),-1!==n.indexOf(r))if(a+="\\",(d=r.charCodeAt(0))<32||d>126)switch(r){case"\n":a+="n";break;case"\t":a+="t";break;case"\r":a+="r";break;case"":a+="a";break;case"\v":a+="v";break;case"\b":a+="b";break;case"\f":a+="f";break;default:for(b=encodeURIComponent(r),null!==(g=k.exec(b))&&(a+=v(parseInt(g[1],16).toString(8),3));null!==(g=k.exec(b));)a+="\\"+v(parseInt(g[1],16).toString(8),3)}else a+=r;else a+=r;return a},exports.addslashes=function(e){return(e+"").replace(/[\\"']/g,"\\$&").replace(/\u0000/g,"\\0")},exports.bin2hex=function(e){var t,a,n,o="";for(t=0,a=(e+="").length;t<a;t++)o+=(n=e.charCodeAt(t).toString(16)).length<2?"0"+n:n;return o},exports.chr=function(e){return e>65535?(e-=65536,String.fromCharCode(55296+(e>>10),56320+(1023&e))):String.fromCharCode(e)},exports.chunk_split=function(e,t,a){return a=a||"\r\n",!((t=parseInt(t,10)||76)<1)&&e.match(new RegExp(".{0,"+t+"}","g")).join(a)},exports.convert_cyr_string=function(e,t,a){var n,o=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,46,154,174,190,46,159,189,46,46,179,191,180,157,46,46,156,183,46,46,182,166,173,46,46,158,163,152,164,155,46,46,46,167,225,226,247,231,228,229,246,250,233,234,235,236,237,238,239,240,242,243,244,245,230,232,227,254,251,253,255,249,248,252,224,241,193,194,215,199,196,197,214,218,201,202,203,204,205,206,207,208,210,211,212,213,198,200,195,222,219,221,223,217,216,220,192,209,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,184,186,32,179,191,32,32,32,32,32,180,162,32,32,32,32,168,170,32,178,175,32,32,32,32,32,165,161,169,254,224,225,246,228,229,244,227,245,232,233,234,235,236,237,238,239,255,240,241,242,243,230,226,252,251,231,248,253,249,247,250,222,192,193,214,196,197,212,195,213,200,201,202,203,204,205,206,207,223,208,209,210,211,198,194,220,219,199,216,221,217,215,218],i=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,225,226,247,231,228,229,246,250,233,234,235,236,237,238,239,240,242,243,244,245,230,232,227,254,251,253,255,249,248,252,224,241,193,194,215,199,196,197,214,218,201,202,203,204,205,206,207,208,35,35,35,124,124,124,124,43,43,124,124,43,43,43,43,43,43,45,45,124,45,43,124,124,43,43,45,45,124,45,43,45,45,45,45,43,43,43,43,43,43,43,43,35,35,124,124,35,210,211,212,213,198,200,195,222,219,221,223,217,216,220,192,209,179,163,180,164,183,167,190,174,32,149,158,32,152,159,148,154,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,205,186,213,241,243,201,32,245,187,212,211,200,190,32,247,198,199,204,181,240,242,185,32,244,203,207,208,202,216,32,246,32,238,160,161,230,164,165,228,163,229,168,169,170,171,172,173,174,175,239,224,225,226,227,166,162,236,235,167,232,237,233,231,234,158,128,129,150,132,133,148,131,149,136,137,138,139,140,141,142,143,159,144,145,146,147,134,130,156,155,135,152,157,153,151,154],r=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,179,32,32,32,32,32,32,32,32,32,32,32,32,32,32,225,226,247,231,228,229,246,250,233,234,235,236,237,238,239,240,242,243,244,245,230,232,227,254,251,253,255,249,248,252,224,241,193,194,215,199,196,197,214,218,201,202,203,204,205,206,207,208,210,211,212,213,198,200,195,222,219,221,223,217,216,220,192,209,32,163,32,32,32,32,32,32,32,32,32,32,32,32,32,32,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,241,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,161,32,32,32,32,32,32,32,32,32,32,32,32,238,208,209,230,212,213,228,211,229,216,217,218,219,220,221,222,223,239,224,225,226,227,214,210,236,235,215,232,237,233,231,234,206,176,177,198,180,181,196,179,197,184,185,186,187,188,189,190,191,207,192,193,194,195,182,178,204,203,183,200,205,201,199,202],s=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,225,226,247,231,228,229,246,250,233,234,235,236,237,238,239,240,242,243,244,245,230,232,227,254,251,253,255,249,248,252,224,241,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,179,163,209,193,194,215,199,196,197,214,218,201,202,203,204,205,206,207,208,210,211,212,213,198,200,195,222,219,221,223,217,216,220,192,255,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,160,161,162,222,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,221,180,181,182,183,184,185,186,187,188,189,190,191,254,224,225,246,228,229,244,227,245,232,233,234,235,236,237,238,239,223,240,241,242,243,230,226,252,251,231,248,253,249,247,250,158,128,129,150,132,133,148,131,149,136,137,138,139,140,141,142,143,159,144,145,146,147,134,130,156,155,135,152,157,153,151,154],u=null,c=null,l=0,p="";switch(t.toUpperCase()){case"W":u=o;break;case"A":case"D":u=i;break;case"I":u=r;break;case"M":u=s;break;case"K":break;default:throw"Unknown source charset: "+t}switch(a.toUpperCase()){case"W":c=o;break;case"A":case"D":c=i;break;case"I":c=r;break;case"M":c=s;break;case"K":break;default:throw"Unknown destination charset: "+a}if(!e)return e;for(l=0;l<e.length;l++)n=null===u?e.charAt(l):String.fromCharCode(u[e.charAt(l).charCodeAt(0)]),p+=null===c?n:String.fromCharCode(c[n.charCodeAt(0)+256]);return p},exports.count_chars=function(e,t){var a,n={},o=[];if(e=(""+e).split("").sort().join("").match(/(.)\1*/g),0==(1&t))for(a=0;256!=a;a++)n[a]=0;if(2===t||4===t){for(a=0;a!=e.length;a+=1)delete n[e[a].charCodeAt(0)];for(a in n)n[a]=4===t?String.fromCharCode(a):0}else if(3===t)for(a=0;a!=e.length;a+=1)n[a]=e[a].slice(0,1);else for(a=0;a!=e.length;a+=1)n[e[a].charCodeAt(0)]=e[a].length;if(t<3)return n;for(a in n)o.push(n[a]);return o.join("")},exports.explode=function(e,t,a){if(arguments.length<2||void 0===e||void 0===t)return null;if(""===e||!1===e||null===e)return!1;if("function"==typeof e||"object"==typeof e||"function"==typeof t||"object"==typeof t)return{0:""};!0===e&&(e="1");var n=(t+="").split(e+="");return void 0===a?n:(0===a&&(a=1),a>0?a>=n.length?n:n.slice(0,a-1).concat([n.slice(a-1).join(e)]):-a>=n.length?[]:(n.splice(n.length+a),n))},exports.get_html_translation_table=function(e,t){var a,n,o,i={},r={},s={},u={};if(s[0]="HTML_SPECIALCHARS",s[1]="HTML_ENTITIES",u[0]="ENT_NOQUOTES",u[2]="ENT_COMPAT",u[3]="ENT_QUOTES",n=isNaN(e)?e?e.toUpperCase():"HTML_SPECIALCHARS":s[e],o=isNaN(t)?t?t.toUpperCase():"ENT_COMPAT":u[t],"HTML_SPECIALCHARS"!==n&&"HTML_ENTITIES"!==n)throw new Error("Table: "+n+" not supported");for(a in i[38]="&amp;","HTML_ENTITIES"===n&&(i[160]="&nbsp;",i[161]="&iexcl;",i[162]="&cent;",i[163]="&pound;",i[164]="&curren;",i[165]="&yen;",i[166]="&brvbar;",i[167]="&sect;",i[168]="&uml;",i[169]="&copy;",i[170]="&ordf;",i[171]="&laquo;",i[172]="&not;",i[173]="&shy;",i[174]="&reg;",i[175]="&macr;",i[176]="&deg;",i[177]="&plusmn;",i[178]="&sup2;",i[179]="&sup3;",i[180]="&acute;",i[181]="&micro;",i[182]="&para;",i[183]="&middot;",i[184]="&cedil;",i[185]="&sup1;",i[186]="&ordm;",i[187]="&raquo;",i[188]="&frac14;",i[189]="&frac12;",i[190]="&frac34;",i[191]="&iquest;",i[192]="&Agrave;",i[193]="&Aacute;",i[194]="&Acirc;",i[195]="&Atilde;",i[196]="&Auml;",i[197]="&Aring;",i[198]="&AElig;",i[199]="&Ccedil;",i[200]="&Egrave;",i[201]="&Eacute;",i[202]="&Ecirc;",i[203]="&Euml;",i[204]="&Igrave;",i[205]="&Iacute;",i[206]="&Icirc;",i[207]="&Iuml;",i[208]="&ETH;",i[209]="&Ntilde;",i[210]="&Ograve;",i[211]="&Oacute;",i[212]="&Ocirc;",i[213]="&Otilde;",i[214]="&Ouml;",i[215]="&times;",i[216]="&Oslash;",i[217]="&Ugrave;",i[218]="&Uacute;",i[219]="&Ucirc;",i[220]="&Uuml;",i[221]="&Yacute;",i[222]="&THORN;",i[223]="&szlig;",i[224]="&agrave;",i[225]="&aacute;",i[226]="&acirc;",i[227]="&atilde;",i[228]="&auml;",i[229]="&aring;",i[230]="&aelig;",i[231]="&ccedil;",i[232]="&egrave;",i[233]="&eacute;",i[234]="&ecirc;",i[235]="&euml;",i[236]="&igrave;",i[237]="&iacute;",i[238]="&icirc;",i[239]="&iuml;",i[240]="&eth;",i[241]="&ntilde;",i[242]="&ograve;",i[243]="&oacute;",i[244]="&ocirc;",i[245]="&otilde;",i[246]="&ouml;",i[247]="&divide;",i[248]="&oslash;",i[249]="&ugrave;",i[250]="&uacute;",i[251]="&ucirc;",i[252]="&uuml;",i[253]="&yacute;",i[254]="&thorn;",i[255]="&yuml;"),"ENT_NOQUOTES"!==o&&(i[34]="&quot;"),"ENT_QUOTES"===o&&(i[39]="&#39;"),i[60]="&lt;",i[62]="&gt;",i)i.hasOwnProperty(a)&&(r[String.fromCharCode(a)]=i[a]);return r},exports.echo=function(){var isNode=module.exports&&"undefined"!=typeof window&&"[object global]"=={}.toString.call(window);if(isNode){var args=Array.prototype.slice.call(arguments);return console.log(args.join(" "))}var arg="",argc=arguments.length,argv=arguments,i=0,holder,win=this.window,d=win.document,ns_xhtml="http://www.w3.org/1999/xhtml",ns_xul="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul",stringToDOM=function(e,t,a,n){var o="";a===ns_xul&&(o=' xmlns:html="'+ns_xhtml+'"');var i="<"+n+' xmlns="'+a+'"'+o+">"+e+"</"+n+">",r=win.DOMImplementationLS,s=win.DOMParser,u=win.ActiveXObject;if(r&&r.createLSInput&&r.createLSParser){var c=r.createLSInput();return c.stringData=i,r.createLSParser(1,null).parse(c).firstChild}if(s)try{var l=(new s).parseFromString(i,"text/xml");if(l&&l.documentElement&&"parsererror"!==l.documentElement.localName&&"http://www.mozilla.org/newlayout/xml/parsererror.xml"!==l.documentElement.namespaceURI)return l.documentElement.firstChild}catch(e){}else if(u){var p=new u("MSXML2.DOMDocument");return p.loadXML(e),p.documentElement}for((holder=d.createElementNS&&(d.documentElement.namespaceURI||"html"!==d.documentElement.nodeName.toLowerCase()||d.contentType&&"text/html"!==d.contentType)?d.createElementNS(a,n):d.createElement(n)).innerHTML=e;holder.firstChild;)t.appendChild(holder.firstChild);return!1},ieFix=function(e){if(1===e.nodeType){var t,a,n=d.createElement(e.nodeName);if(e.attributes&&e.attributes.length>0)for(t=0,a=e.attributes.length;t<a;t++)n.setAttribute(e.attributes[t].nodeName,e.getAttribute(e.attributes[t].nodeName));if(e.childNodes&&e.childNodes.length>0)for(t=0,a=e.childNodes.length;t<a;t++)n.appendChild(ieFix(e.childNodes[t]));return n}return d.createTextNode(e.nodeValue)},replacer=function(s,m1,m2){return"\\"!==m1?m1+eval(m2):s};this.php_js=this.php_js||{};var phpjs=this.php_js,ini=phpjs.ini,obs=phpjs.obs;for(i=0;i<argc;i++)if(arg=argv[i],ini&&ini["phpjs.echo_embedded_vars"]&&(arg=arg.replace(/(.?)\{?\$(\w*?\}|\w*)/g,replacer)),!phpjs.flushing&&obs&&obs.length)obs[obs.length-1].buffer+=arg;else if(d.appendChild)if(d.body)if("Microsoft Internet Explorer"===win.navigator.appName)d.body.appendChild(stringToDOM(ieFix(arg)));else{var unappendedLeft=stringToDOM(arg,d.body,ns_xhtml,"div").cloneNode(!0);unappendedLeft&&d.body.appendChild(unappendedLeft)}else d.documentElement.appendChild(stringToDOM(arg,d.documentElement,ns_xul,"description"));else d.write&&d.write(arg)},exports.htmlspecialchars=function(e,t,a,n){var o=0,i=0,r=!1;null==t&&(t=2),e=e.toString(),!1!==n&&(e=e.replace(/&/g,"&amp;")),e=e.replace(/</g,"&lt;").replace(/>/g,"&gt;");var s={ENT_NOQUOTES:0,ENT_HTML_QUOTE_SINGLE:1,ENT_HTML_QUOTE_DOUBLE:2,ENT_COMPAT:2,ENT_QUOTES:3,ENT_IGNORE:4};if(0===t&&(r=!0),"number"!=typeof t){for(t=[].concat(t),i=0;i<t.length;i++)0===s[t[i]]?r=!0:s[t[i]]&&(o|=s[t[i]]);t=o}return t&s.ENT_HTML_QUOTE_SINGLE&&(e=e.replace(/'/g,"&#039;")),r||(e=e.replace(/"/g,"&quot;")),e},exports.htmlspecialchars_decode=function(e,t){var a=0,n=0,o=!1;void 0===t&&(t=2),e=e.toString().replace(/&lt;/g,"<").replace(/&gt;/g,">");var i={ENT_NOQUOTES:0,ENT_HTML_QUOTE_SINGLE:1,ENT_HTML_QUOTE_DOUBLE:2,ENT_COMPAT:2,ENT_QUOTES:3,ENT_IGNORE:4};if(0===t&&(o=!0),"number"!=typeof t){for(t=[].concat(t),n=0;n<t.length;n++)0===i[t[n]]?o=!0:i[t[n]]&&(a|=i[t[n]]);t=a}return t&i.ENT_HTML_QUOTE_SINGLE&&(e=e.replace(/&#0*39;/g,"'")),o||(e=e.replace(/&quot;/g,'"')),e=e.replace(/&amp;/g,"&")},exports.implode=function(e,t){var a="",n="",o="";if(1===arguments.length&&(t=e,e=""),"object"==typeof t){if("[object Array]"===Object.prototype.toString.call(t))return t.join(e);for(a in t)n+=o+t[a],o=e;return n}return t},exports.lcfirst=function(e){return(e+="").charAt(0).toLowerCase()+e.substr(1)},exports.levenshtein=function(e,t){if(e==t)return 0;var a=e.length,n=t.length;if(0===a)return n;if(0===n)return a;var o=!1;try{o=!"0"[0]}catch(e){o=!0}o&&(e=e.split(""),t=t.split(""));var i=new Array(a+1),r=new Array(a+1),s=0,u=0,c=0;for(s=0;s<a+1;s++)i[s]=s;var l="";for(u=1;u<=n;u++){for(r[0]=u,l=t[u-1],s=0;s<a;s++){c=e[s]==l?0:1;var p=i[s+1]+1,m=r[s]+1,f=i[s]+c;m<p&&(p=m),f<p&&(p=f),r[s+1]=p}var h=i;i=r,r=h}return i[a]},exports.ltrim=function(e,t){t=t?(t+"").replace(/([\[\]\(\)\.\?\/\*\{\}\+\$\^\:])/g,"$1"):" \\s ";var a=new RegExp("^["+t+"]+","g");return(e+"").replace(a,"")},exports.metaphone=function(e,t){var a=typeof e;if("undefined"===a||"object"===a&&null!==e)return null;if("number"===a&&(isNaN(e)?e="NAN":isFinite(e)||(e="INF")),t<0)return!1;t=Math.floor(+t)||0;var n="ABCDEFGHIJKLMNOPQRSTUVWXYZ",o=new RegExp("^[^"+n+"]+");if(!(e=(e="string"==typeof e?e:"").toUpperCase().replace(o,"")))return"";var i,r,s=function(e,t){return""!==t&&-1!==e.indexOf(t)},u=0,c=e.charAt(0),l=e.charAt(1),p=e.length,m="";switch(c){case"A":m+="E"===l?l:c,u+=1;break;case"G":case"K":case"P":"N"===l&&(m+=l,u+=2);break;case"W":"R"===l?(m+=l,u+=2):("H"===l||s("AEIOU",l))&&(m+="W",u+=2);break;case"X":m+="S",u+=1;break;case"E":case"I":case"O":case"U":m+=c,u++}for(;u<p&&(0===t||m.length<t);u+=1)if(c=e.charAt(u),l=e.charAt(u+1),r=e.charAt(u-1),i=e.charAt(u+2),c!==r||"C"===c)switch(c){case"B":"M"!==r&&(m+=c);break;case"C":s("EIY",l)?"I"===l&&"A"===i?m+="X":"S"!==r&&(m+="S"):"H"===l?(m+="X",u+=1):m+="K";break;case"D":"G"===l&&s("EIY",i)?(m+="J",u+=1):m+="T";break;case"G":"H"===l?s("BDH",e.charAt(u-3))||"H"===e.charAt(u-4)||(m+="F",u+=1):"N"===l?s(n,i)&&"NED"!==e.substr(u+1,3)&&(m+="K"):s("EIY",l)&&"G"!==r?m+="J":m+="K";break;case"H":s("AEIOU",l)&&!s("CGPST",r)&&(m+=c);break;case"K":"C"!==r&&(m+="K");break;case"P":m+="H"===l?"F":c;break;case"Q":m+="K";break;case"S":"I"===l&&s("AO",i)?m+="X":"H"===l?(m+="X",u+=1):m+="S";break;case"T":"I"===l&&s("AO",i)?m+="X":"H"===l?(m+="0",u+=1):"CH"!==e.substr(u+1,2)&&(m+="T");break;case"V":m+="F";break;case"W":case"Y":s("AEIOU",l)&&(m+=c);break;case"X":m+="KS";break;case"Z":m+="S";break;case"F":case"J":case"L":case"M":case"N":case"R":m+=c}return m},exports.nl2br=function(e,t){return(e+"").replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g,"$1"+(t||void 0===t?"<br />":"<br>")+"$2")},exports.number_format=function(e,t,a,n){e=(e+"").replace(/[^0-9+\-Ee.]/g,"");var o=isFinite(+e)?+e:0,i=isFinite(+t)?Math.abs(t):0,r=void 0===n?",":n,s=void 0===a?".":a,u="";return(u=(i?function(e,t){var a=Math.pow(10,t);return""+(Math.round(e*a)/a).toFixed(t)}(o,i):""+Math.round(o)).split("."))[0].length>3&&(u[0]=u[0].replace(/\B(?=(?:\d{3})+(?!\d))/g,r)),(u[1]||"").length<i&&(u[1]=u[1]||"",u[1]+=new Array(i-u[1].length+1).join("0")),u.join(s)},exports.ord=function(e){var t=e+"",a=t.charCodeAt(0);if(55296<=a&&a<=56319){var n=a;return 1===t.length?a:1024*(n-55296)+(t.charCodeAt(1)-56320)+65536}return a},exports.parse_str=function(e,t){var a,n,o,i,r,s,u,c,l,p,m,f,h,d=String(e).replace(/^&/,"").replace(/&$/,"").split("&"),g=d.length,b=function(e){return decodeURIComponent(e.replace(/\+/g,"%20"))};for(t||(t=this.window),a=0;a<g;a++){for(l=b((c=d[a].split("="))[0]),p=c.length<2?"":b(c[1]);" "===l.charAt(0);)l=l.slice(1);if(l.indexOf("\0")>-1&&(l=l.slice(0,l.indexOf("\0"))),l&&"["!==l.charAt(0)){for(f=[],m=0,n=0;n<l.length;n++)if("["!==l.charAt(n)||m){if("]"===l.charAt(n)&&m&&(f.length||f.push(l.slice(0,m-1)),f.push(l.substr(m,n-m)),m=0,"["!==l.charAt(n+1)))break}else m=n+1;for(f.length||(f=[l]),n=0;n<f[0].length&&(" "!==(u=f[0].charAt(n))&&"."!==u&&"["!==u||(f[0]=f[0].substr(0,n)+"_"+f[0].substr(n+1)),"["!==u);n++);for(s=t,n=0,h=f.length;n<h;n++)if(l=f[n].replace(/^['"]/,"").replace(/['"]$/,""),n!==f.length-1,r=s,""!==l&&" "!==l||0===n)void 0===s[l]&&(s[l]={}),s=s[l];else{for(i in o=-1,s)s.hasOwnProperty(i)&&+i>o&&i.match(/^\d+$/g)&&(o=+i);l=o+1}r[l]=p}}},exports.quoted_printable_decode=function(e){return e.replace(/=\r\n/gm,"").replace(/=([0-9A-F]{2})/gim,(function(e,t){return String.fromCharCode(parseInt(t,16))}))},exports.quoted_printable_encode=function(e){var t=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F"];return RFC2045Encode2IN=/.{1,72}(?!\r\n)[^=]{0,3}/g,RFC2045Encode2OUT=function(e){return"\r\n"===e.substr(e.length-2)?e:e+"=\r\n"},(e=e.replace(/ \r\n|\r\n|[^!-<>-~ ]/gm,(function(e){if(e.length>1)return e.replace(" ","=20");var a=e.charCodeAt(0);return"="+t[a>>>4&15]+t[15&a]})).replace(RFC2045Encode2IN,RFC2045Encode2OUT)).substr(0,e.length-3)},exports.quotemeta=function(e){return(e+"").replace(/([\.\\\+\*\?\[\^\]\$\(\)])/g,"\\$1")},exports.rtrim=function(e,t){t=t?(t+"").replace(/([\[\]\(\)\.\?\/\*\{\}\+\$\^\:])/g,"\\$1"):" \\s ";var a=new RegExp("["+t+"]+$","g");return(e+"").replace(a,"")},exports.similar_text=function(e,t,a){if(null===e||null===t||void 0===e||void 0===t)return 0;var n,o,i,r,s=0,u=0,c=0,l=(e+="").length,p=(t+="").length;for(c=0,n=0;n<l;n++)for(o=0;o<p;o++){for(i=0;n+i<l&&o+i<p&&e.charAt(n+i)===t.charAt(o+i);i++);i>c&&(c=i,s=n,u=o)}return(r=c)&&(s&&u&&(r+=this.similar_text(e.substr(0,s),t.substr(0,u))),s+c<l&&u+c<p&&(r+=this.similar_text(e.substr(s+c,l-s-c),t.substr(u+c,p-u-c)))),a?200*r/(l+p):r},exports.soundex=function(e){if(!(e=(e+"").toUpperCase()))return"";for(var t,a,n,o=[0,0,0,0],i={B:1,F:1,P:1,V:1,C:2,G:2,J:2,K:2,Q:2,S:2,X:2,Z:2,D:3,T:3,L:4,M:5,N:5,R:6},r=0,s=0;(a=e.charAt(r++))&&s<4;)(t=i[a])?t!==n&&(o[s++]=n=t):(s+=1===r,n=0);return o[0]=e.charAt(0),o.join("")},exports.sprintf=function(){var e=/%%|%(\d+\$)?([-+\'#0 ]*)(\*\d+\$|\*|\d+)?(\.(\*\d+\$|\*|\d+))?([scboxXuideEfFgG])/g,t=arguments,a=0,n=t[a++],o=function(e,t,a,n){a||(a=" ");var o=e.length>=t?"":new Array(1+t-e.length>>>0).join(a);return n?e+o:o+e},i=function(e,t,a,n,i,r){var s=n-e.length;return s>0&&(e=a||!i?o(e,n,r,a):e.slice(0,t.length)+o("",s,"0",!0)+e.slice(t.length)),e},r=function(e,t,a,n,r,s,u){var c=e>>>0;return e=(a=a&&c&&{2:"0b",8:"0",16:"0x"}[t]||"")+o(c.toString(t),s||0,"0",!1),i(e,a,n,r,u)},s=function(e,t,a,n,o,r){return null!=n&&(e=e.slice(0,n)),i(e,"",t,a,o,r)},u=function(e,n,u,c,l,p,m){var f,h,d,g,b;if("%%"===e)return"%";for(var k=!1,v="",y=!1,j=!1,w=" ",_=u.length,A=0;u&&A<_;A++)switch(u.charAt(A)){case" ":v=" ";break;case"+":v="+";break;case"-":k=!0;break;case"'":w=u.charAt(A+1);break;case"0":y=!0,w="0";break;case"#":j=!0}if((c=c?"*"===c?+t[a++]:"*"==c.charAt(0)?+t[c.slice(1,-1)]:+c:0)<0&&(c=-c,k=!0),!isFinite(c))throw new Error("sprintf: (minimum-)width must be finite");switch(p=p?"*"===p?+t[a++]:"*"==p.charAt(0)?+t[p.slice(1,-1)]:+p:"fFeE".indexOf(m)>-1?6:"d"===m?0:void 0,b=n?t[n.slice(0,-1)]:t[a++],m){case"s":return s(String(b),k,c,p,y,w);case"c":return s(String.fromCharCode(+b),k,c,p,y);case"b":return r(b,2,j,k,c,p,y);case"o":return r(b,8,j,k,c,p,y);case"x":return r(b,16,j,k,c,p,y);case"X":return r(b,16,j,k,c,p,y).toUpperCase();case"u":return r(b,10,j,k,c,p,y);case"i":case"d":return f=+b||0,b=(h=(f=Math.round(f-f%1))<0?"-":v)+o(String(Math.abs(f)),p,"0",!1),i(b,h,k,c,y);case"e":case"E":case"f":case"F":case"g":case"G":return h=(f=+b)<0?"-":v,d=["toExponential","toFixed","toPrecision"]["efg".indexOf(m.toLowerCase())],g=["toString","toUpperCase"]["eEfFgG".indexOf(m)%2],b=h+Math.abs(f)[d](p),i(b,h,k,c,y)[g]();default:return e}};return n.replace(e,u)},exports.sscanf=function(e,t){var a,n=[],o=/\S/,i=arguments,r=this,s=function(e){var a=t.slice(e).match(/%[cdeEufgosxX]/g);if(a)for(var o=a.length;o--;)n.push(null);return u()},u=function(){if(2===i.length)return n;for(var e=0;e<n.length;++e)r.window[i[e+2]]=n[e];return e},c=function(t,o,i){if(f){var r=e.slice(t),s=m?r.substr(0,m):r,u=o.exec(s);if(null===(n[void 0!==a?a:n.length]=u?i?i.apply(null,u):u[0]:null))throw"No match in string";return t+u[0].length}return t};if(arguments.length<2)throw"Not enough arguments passed to sscanf";for(var l=0,p=0;l<t.length;l++){var m=0,f=!0;if("%"===t.charAt(l)){if("%"===t.charAt(l+1)){if("%"===e.charAt(p)){++l,++p;continue}return s(l+2)}var h=new RegExp("^(?:(\\d+)\\$)?(\\*)?(\\d*)([hlL]?)","g"),d=h.exec(t.slice(l+1)),g=a;if(g&&void 0===d[1])throw"All groups in sscanf() must be expressed as numeric if any have already been used";a=d[1]?parseInt(d[1],10)-1:void 0,f=!d[2],m=parseInt(d[3],10);var b=d[4];if(l+=h.lastIndex,b)switch(b){case"h":case"l":case"L":break;default:throw"Unexpected size specifier in sscanf()!"}try{switch(t.charAt(l+1)){case"F":case"g":case"G":case"b":break;case"i":p=c(p,/([+-])?(?:(?:0x([\da-fA-F]+))|(?:0([0-7]+))|(\d+))/,(function(e,t,a,n,o){return a?parseInt(e,16):n?parseInt(e,8):parseInt(e,10)}));break;case"n":n[void 0!==a?a:n.length-1]=p;break;case"c":p=c(p,new RegExp(".{1,"+(m||1)+"}"));break;case"D":case"d":p=c(p,/([+-])?(?:0*)(\d+)/,(function(e,t,a){var n=parseInt((t||"")+a,10);return n<0?n<-2147483648?-2147483648:n:n<2147483647?n:2147483647}));break;case"f":case"E":case"e":p=c(p,/([+-])?(?:0*)(\d*\.?\d*(?:[eE]?\d+)?)/,(function(e,t,a){return"."===a?null:parseFloat((t||"")+a)}));break;case"u":p=c(p,/([+-])?(?:0*)(\d+)/,(function(e,t,a){var n=parseInt(a,10);return"-"===t?4294967296-n:n<4294967295?n:4294967295}));break;case"o":p=c(p,/([+-])?(?:0([0-7]+))/,(function(e,t,a){return parseInt(e,8)}));break;case"s":p=c(p,/\S+/);break;case"X":case"x":p=c(p,/([+-])?(?:(?:0x)?([\da-fA-F]+))/,(function(e,t,a){return parseInt(e,16)}));break;case"":throw"Missing character after percent mark in sscanf() format argument";default:throw"Unrecognized character after percent mark in sscanf() format argument"}}catch(e){if("No match in string"===e)return s(l+2)}++l}else if(t.charAt(l)!==e.charAt(p)){if(o.lastIndex=0,o.test(e.charAt(p))||""===e.charAt(p))return s(l+1);e=e.slice(0,p)+e.slice(p+1),l--}else p++}return u()},exports.str_getcsv=function(e,t,a,n){var o,i,r=[],s=function(e){return e.split("").reverse().join("")},u=function(e){return String(e).replace(/([\\\.\+\*\?\[\^\]\$\(\)\{\}\=\!<\>\|\:])/g,"\\$1")};t=t||",",n=n||"\\";var c=u(a=a||'"'),l=u(n);for(o=0,i=(e=s(e=e.replace(new RegExp("^\\s*"+c),"").replace(new RegExp(c+"\\s*$"),"")).split(new RegExp(c+"\\s*"+u(t)+"\\s*"+c+"(?!"+l+")","g")).reverse()).length;o<i;o++)r.push(s(e[o]).replace(new RegExp(l+c,"g"),a));return r},exports.str_ireplace=function(e,t,a){var n,o,i="",r=0;if(r=(e+="").length,"[object Array]"!==Object.prototype.toString.call(t)&&(t=[t],"[object Array]"===Object.prototype.toString.call(e)))for(;r>t.length;)t[t.length]=t[0];for("[object Array]"!==Object.prototype.toString.call(e)&&(e=[e]);e.length>t.length;)t[t.length]="";if("[object Array]"===Object.prototype.toString.call(a)){for(i in a)a.hasOwnProperty(i)&&(a[i]=str_ireplace(e,t,a[i]));return a}for(r=e.length,n=0;n<r;n++)o=new RegExp(e[n].replace(/([\\\^\$*+\[\]?{}.=!:(|)])/g,"\\$1"),"gi"),a=a.replace(o,t[n]);return a},exports.str_pad=function(e,t,a,n){var o,i="",r=function(e,t){for(var a="";a.length<t;)a+=e;return a=a.substr(0,t)};return a=void 0!==a?a:" ","STR_PAD_LEFT"!==n&&"STR_PAD_RIGHT"!==n&&"STR_PAD_BOTH"!==n&&(n="STR_PAD_RIGHT"),(o=t-(e+="").length)>0&&("STR_PAD_LEFT"===n?e=r(a,o)+e:"STR_PAD_RIGHT"===n?e+=r(a,o):"STR_PAD_BOTH"===n&&(e=(e=(i=r(a,Math.ceil(o/2)))+e+i).substr(0,t))),e},exports.str_repeat=function(e,t){for(var a="";1&t&&(a+=e),t>>=1;)e+=e;return a},exports.str_replace=function(e,t,a,n){var o,i=0,r=0,s="",u="",c=0,l=[].concat(e),p=[].concat(t),m=a,f="[object Array]"===Object.prototype.toString.call(p),h="[object Array]"===Object.prototype.toString.call(m);for(m=[].concat(m),n&&(this.window[n]=0),i=0,o=m.length;i<o;i++)if(""!==m[i])for(r=0,c=l.length;r<c;r++)s=m[i]+"",u=f?void 0!==p[r]?p[r]:"":p[0],m[i]=s.split(l[r]).join(u),n&&m[i]!==s&&(this.window[n]+=(s.length-m[i].length)/l[r].length);return h?m:m[0]},exports.str_rot13=function(e){return(e+"").replace(/[a-z]/gi,(function(e){return String.fromCharCode(e.charCodeAt(0)+(e.toLowerCase()<"n"?13:-13))}))},exports.str_shuffle=function(e){if(0===arguments.length)throw"Wrong parameter count for str_shuffle()";if(null==e)return"";for(var t,a="",n=(e+="").length;n;)t=Math.floor(Math.random()*n),a+=e.charAt(t),e=e.substring(0,t)+e.substr(t+1),n--;return a},exports.str_split=function(e,t){if(null===t&&(t=1),null===e||t<1)return!1;for(var a=[],n=0,o=(e+="").length;n<o;)a.push(e.slice(n,n+=t));return a},exports.strcasecmp=function(e,t){var a=(e+"").toLowerCase(),n=(t+"").toLowerCase();return a>n?1:a==n?0:-1},exports.strcmp=function(e,t){return e==t?0:e>t?1:-1},exports.strcspn=function(e,t,a,n){a=a||0;var o=n&&a+n<e.length?a+n:e.length;e:for(var i=a,r=0;i<o;i++){for(var s=0;s<t.length;s++)if(-1!==e.charAt(i).indexOf(t[s]))continue e;++r}return r},exports.strip_tags=function(e,t){t=(((t||"")+"").toLowerCase().match(/<[a-z][a-z0-9]*>/g)||[]).join("");return e.replace(/<!--[\s\S]*?-->|<\?(?:php)?[\s\S]*?\?>/gi,"").replace(/<\/?([a-z][a-z0-9]*)\b[^>]*>/gi,(function(e,a){return t.indexOf("<"+a.toLowerCase()+">")>-1?e:""}))},exports.stripos=function(e,t,a){var n,o=(e+"").toLowerCase(),i=(t+"").toLowerCase();return-1!==(n=o.indexOf(i,a))&&n},exports.stripslashes=function(e){return(e+"").replace(/\\(.?)/g,(function(e,t){switch(t){case"\\":return"\\";case"0":return"\0";case"":return"";default:return t}}))},exports.stristr=function(e,t,a){var n;return-1!=(n=(e+="").toLowerCase().indexOf((t+"").toLowerCase()))&&(a?e.substr(0,n):e.slice(n))},exports.strlen=function(e){var t=e+"",a=0,n=0;if(!this.php_js||!this.php_js.ini||!this.php_js.ini["unicode.semantics"]||"on"!==this.php_js.ini["unicode.semantics"].local_value.toLowerCase())return e.length;var o=function(e,t){var a=e.charCodeAt(t),n="",o="";if(55296<=a&&a<=56319){if(e.length<=t+1)throw"High surrogate without following low surrogate";if(56320>(n=e.charCodeAt(t+1))||n>57343)throw"High surrogate without following low surrogate";return e.charAt(t)+e.charAt(t+1)}if(56320<=a&&a<=57343){if(0===t)throw"Low surrogate without preceding high surrogate";if(55296>(o=e.charCodeAt(t-1))||o>56319)throw"Low surrogate without preceding high surrogate";return!1}return e.charAt(t)};for(a=0,n=0;a<t.length;a++)!1!==o(t,a)&&n++;return n},exports.strnatcasecmp=function(e,t){for(var a,n,o,i=(e+"").toLowerCase(),r=(t+"").toLowerCase(),s=function(e){return e.charCodeAt(0)<=32},u=function(e){var t=e.charCodeAt(0);return t>=48&&t<=57},c=function(e,t){for(var a,n,o=0,i=0,r=0;;i++,r++){if(a=e.charAt(i),n=t.charAt(r),!u(a)&&!u(n))return o;if(!u(a))return-1;if(!u(n))return 1;if(a<n)0===o&&(o=-1);else if(a>n)0===o&&(o=1);else if("0"===a&&"0"===n)return o}},l=0,p=0,m=0,f=0;;){for(m=f=0,a=i.charAt(l),n=r.charAt(p);s(a)||"0"===a;)"0"===a?m++:m=0,a=i.charAt(++l);for(;s(n)||"0"===n;)"0"===n?f++:f=0,n=r.charAt(++p);if(u(a)&&u(n)&&0!==(o=c(i.substring(l),r.substring(p))))return o;if("0"===a&&"0"===n)return m-f;if(a<n)return-1;if(a>n)return 1;if(l>=i.length&&p>=r.length)return 0;++l,++p}},exports.strncasecmp=function(e,t,a){var n,o=0,i=(e+"").toLowerCase().substr(0,a),r=(t+"").toLowerCase().substr(0,a);if(i.length!==r.length){if(i.length<r.length){if(a=i.length,r.substr(0,i.length)==i)return i.length-r.length}else if(a=r.length,i.substr(0,r.length)==r)return i.length-r.length}else a=i.length;for(n=0,o=0;o<a;o++)if(0!==(n=i.charCodeAt(o)-r.charCodeAt(o)))return n;return 0},exports.strncmp=function(e,t,a){var n=(e+"").substr(0,a),o=(t+"").substr(0,a);return n==o?0:n>o?1:-1},exports.strpbrk=function(e,t){for(var a=0,n=e.length;a<n;++a)if(t.indexOf(e.charAt(a))>=0)return e.slice(a);return!1},exports.strpos=function(e,t,a){var n=(e+"").indexOf(t,a||0);return-1!==n&&n},exports.strrchr=function(e,t){var a;return"string"!=typeof t&&(t=String.fromCharCode(parseInt(t,10))),t=t.charAt(0),-1!==(a=e.lastIndexOf(t))&&e.substr(a)},exports.strrev=function(e){return(e=(e+="").replace(/(.)([\uDC00-\uDFFF\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065E\u0670\u06D6-\u06DC\u06DE-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u0901-\u0903\u093C\u093E-\u094D\u0951-\u0954\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C01-\u0C03\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C82\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D02\u0D03\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F90-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B6-\u17D3\u17DD\u180B-\u180D\u18A9\u1920-\u192B\u1930-\u193B\u19B0-\u19C0\u19C8\u19C9\u1A17-\u1A1B\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAA\u1C24-\u1C37\u1DC0-\u1DE6\u1DFE\u1DFF\u20D0-\u20F0\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA67C\uA67D\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C4\uA926-\uA92D\uA947-\uA953\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uFB1E\uFE00-\uFE0F\uFE20-\uFE26]+)/g,"$2$1")).split("").reverse().join("")},exports.strripos=function(e,t,a){e=(e+"").toLowerCase(),t=(t+"").toLowerCase();var n=-1;return a?-1!==(n=(e+"").slice(a).lastIndexOf(t))&&(n+=a):n=(e+"").lastIndexOf(t),n>=0&&n},exports.strrpos=function(e,t,a){var n=-1;return a?-1!==(n=(e+"").slice(a).lastIndexOf(t))&&(n+=a):n=(e+"").lastIndexOf(t),n>=0&&n},exports.strspn=function(e,t,a,n){var o,i,r=0,s=0;for(a=a?a<0?e.length+a:a:0,n=n?n<0?e.length+n-a:n:e.length-a,e=e.substr(a,n),s=0;s<e.length;s++){for(o=0,i=e.substring(s,s+1),r=0;r<=t.length;r++)if(i==t.substring(r,r+1)){o=1;break}if(1!=o)return s}return s},exports.strstr=function(e,t,a){var n;return-1!=(n=(e+="").indexOf(t))&&(a?e.substr(0,n):e.slice(n))},exports.strtok=function(e,t){if(this.php_js=this.php_js||{},void 0===t&&(t=e,e=this.php_js.strtokleftOver),0===e.length)return!1;if(-1!==t.indexOf(e.charAt(0)))return this.strtok(e.substr(1),t);for(var a=0;a<e.length&&-1===t.indexOf(e.charAt(a));a++);return this.php_js.strtokleftOver=e.substr(a+1),e.substring(0,a)},exports.strtolower=function(e){return(e+"").toLowerCase()},exports.strtoupper=function(e){return(e+"").toUpperCase()},exports.substr=function(e,t,a){var n=0,o=!0,i=0,r=0,s=0,u="",c=(e+="").length;switch(this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},this.php_js.ini["unicode.semantics"]&&this.php_js.ini["unicode.semantics"].local_value.toLowerCase()){case"on":for(n=0;n<e.length;n++)if(/[\uD800-\uDBFF]/.test(e.charAt(n))&&/[\uDC00-\uDFFF]/.test(e.charAt(n+1))){o=!1;break}if(!o){if(t<0)for(n=c-1,i=t+=c;n>=i;n--)/[\uDC00-\uDFFF]/.test(e.charAt(n))&&/[\uD800-\uDBFF]/.test(e.charAt(n-1))&&(t--,i--);else for(var l=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g;null!=l.exec(e);){if(!(l.lastIndex-2<t))break;t++}if(t>=c||t<0)return!1;if(a<0){for(n=c-1,r=c+=a;n>=r;n--)/[\uDC00-\uDFFF]/.test(e.charAt(n))&&/[\uD800-\uDBFF]/.test(e.charAt(n-1))&&(c--,r--);return!(t>c)&&e.slice(t,c)}for(s=t+a,n=t;n<s;n++)u+=e.charAt(n),/[\uD800-\uDBFF]/.test(e.charAt(n))&&/[\uDC00-\uDFFF]/.test(e.charAt(n+1))&&s++;return u}case"off":default:return t<0&&(t+=c),c=void 0===a?c:a<0?a+c:a+t,!(t>=e.length||t<0||t>c)&&e.slice(t,c)}},exports.substr_compare=function(e,t,a,n,o){if(!a&&0!==a)throw"Missing offset for substr_compare()";return a<0&&(a=e.length+a),!(n&&n>e.length-a)&&(n=n||e.length-a,e=e.substr(a,n),t=t.substr(0,n),o?(e=(e+"").toLowerCase())==(t=(t+"").toLowerCase())?0:e>t?1:-1:e==t?0:e>t?1:-1)},exports.substr_count=function(e,t,a,n){var o=0;if(e+="",t+="",isNaN(a)&&(a=0),isNaN(n)&&(n=0),0==t.length)return!1;for(a--;-1!=(a=e.indexOf(t,a+1));){if(n>0&&a+t.length>n)return!1;o++}return o},exports.substr_replace=function(e,t,a,n){return a<0&&(a+=e.length),(n=void 0!==n?n:e.length)<0&&(n=n+e.length-a),e.slice(0,a)+t.substr(0,n)+t.slice(n)+e.slice(a+n)},exports.trim=function(e,t){var a,n=0,o=0;for(e+="",a=t?(t+="").replace(/([\[\]\(\)\.\?\/\*\{\}\+\$\^\:])/g,"$1"):" \n\r\t\f\v            ​\u2028\u2029　",n=e.length,o=0;o<n;o++)if(-1===a.indexOf(e.charAt(o))){e=e.substring(o);break}for(o=(n=e.length)-1;o>=0;o--)if(-1===a.indexOf(e.charAt(o))){e=e.substring(0,o+1);break}return-1===a.indexOf(e.charAt(0))?e:""},exports.ucfirst=function(e){return(e+="").charAt(0).toUpperCase()+e.substr(1)},exports.ucwords=function(e){return(e+"").replace(/^([a-z\u00E0-\u00FC])|\s+([a-z\u00E0-\u00FC])/g,(function(e){return e.toUpperCase()}))},exports.wordwrap=function(e,t,a,n){var o,i,r,s,u,c=arguments.length>=2?arguments[1]:75,l=arguments.length>=3?arguments[2]:"\n",p=arguments.length>=4&&arguments[3];if(e+="",c<1)return e;for(o=-1,r=(u=e.split(/\r\n|\n|\r/)).length;++o<r;u[o]+=s)for(s=u[o],u[o]="";s.length>c;u[o]+=s.slice(0,i)+((s=s.slice(i)).length?l:""))i=2==p||(i=s.slice(0,c+1).match(/\S*(\s)?$/))[1]?c:i.input.length-i[0].length||1==p&&c||i.input.length+(i=s.slice(c).match(/^\S*/))[0].length;return u.join("\n")},exports.base64_decode=function(e){var t,a,n,o,i,r,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",u=0,c=0,l="",p=[];if(!e)return e;e+="";do{t=(r=s.indexOf(e.charAt(u++))<<18|s.indexOf(e.charAt(u++))<<12|(o=s.indexOf(e.charAt(u++)))<<6|(i=s.indexOf(e.charAt(u++))))>>16&255,a=r>>8&255,n=255&r,p[c++]=64==o?String.fromCharCode(t):64==i?String.fromCharCode(t,a):String.fromCharCode(t,a,n)}while(u<e.length);return l=p.join(""),decodeURIComponent(escape(l.replace(/\0+$/,"")))},exports.base64_encode=function(e){var t,a,n,o,i,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s=0,u=0,c="",l=[];if(!e)return e;e=unescape(encodeURIComponent(e));do{t=(i=e.charCodeAt(s++)<<16|e.charCodeAt(s++)<<8|e.charCodeAt(s++))>>18&63,a=i>>12&63,n=i>>6&63,o=63&i,l[u++]=r.charAt(t)+r.charAt(a)+r.charAt(n)+r.charAt(o)}while(s<e.length);c=l.join("");var p=e.length%3;return(p?c.slice(0,p-3):c)+"===".slice(p||3)},exports.parse_url=function(e,t){for(var a=["source","scheme","authority","userInfo","user","pass","host","port","relative","path","directory","file","query","fragment"],n=this.php_js&&this.php_js.ini||{},o=n["phpjs.parse_url.mode"]&&n["phpjs.parse_url.mode"].local_value||"php",i={php:/^(?:([^:\/?#]+):)?(?:\/\/()(?:(?:()(?:([^:@]*):?([^:@]*))?@)?([^:\/?#]*)(?::(\d*))?))?()(?:(()(?:(?:[^?#\/]*\/)*)()(?:[^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,strict:/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@]*):?([^:@]*))?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,loose:/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/\/?)?((?:(([^:@]*):?([^:@]*))?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/},r=i[o].exec(e),s={},u=14;u--;)r[u]&&(s[a[u]]=r[u]);if(t)return s[t.replace("PHP_URL_","").toLowerCase()];if("php"!==o){var c=n["phpjs.parse_url.queryKey"]&&n["phpjs.parse_url.queryKey"].local_value||"queryKey";i=/(?:^|&)([^&=]*)=?([^&]*)/g,s[c]={},(s[a[12]]||"").replace(i,(function(e,t,a){t&&(s[c][t]=a)}))}return delete s.source,s},exports.rawurldecode=function(e){return decodeURIComponent((e+"").replace(/%(?![\da-f]{2})/gi,(function(){return"%25"})))},exports.rawurlencode=function(e){return e=(e+"").toString(),encodeURIComponent(e).replace(/!/g,"%21").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")},exports.urldecode=function(e){return decodeURIComponent((e+"").replace(/%(?![\da-f]{2})/gi,(function(){return"%25"})).replace(/\+/g,"%20"))},exports.urlencode=function(e){return e=(e+"").toString(),encodeURIComponent(e).replace(/!/g,"%21").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A").replace(/%20/g,"+")},exports.empty=function(e){var t,a,n,o=[void 0,null,!1,0,"","0"];for(a=0,n=o.length;a<n;a++)if(e===o[a])return!0;if("object"==typeof e){for(t in e)return!1;return!0}return!1},exports.floatval=function(e){return parseFloat(e)||0},exports.intval=function(e,t){var a,n=typeof e;return"boolean"===n?+e:"string"===n?(a=parseInt(e,t||10),isNaN(a)||!isFinite(a)?0:a):"number"===n&&isFinite(e)?0|e:0},exports.is_array=function(e){var t,a,n;return _isArray=function(e){if(!e||"object"!=typeof e||"number"!=typeof e.length)return!1;var t=e.length;return e[e.length]="bogus",t!==e.length?(e.length-=1,!0):(delete e[e.length],!1)},!(!e||"object"!=typeof e)&&(this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},t=this.php_js.ini["phpjs.objectsAsArrays"],_isArray(e)||(!t||0!==parseInt(t.local_value,10)&&(!t.local_value.toLowerCase||"off"!==t.local_value.toLowerCase()))&&"[object Object]"===Object.prototype.toString.call(e)&&"Object"===(a=e.constructor,(n=/\W*function\s+([\w\$]+)\s*\(/.exec(a))?n[1]:"(Anonymous)"))},exports.is_binary=function(e){return"string"==typeof e},exports.is_bool=function(e){return!0===e||!1===e},exports.is_buffer=function(e){return"string"==typeof e},exports.is_callable=function(e,t,a){var n="",o={},i="";if("string"==typeof e)o=this.window,i=e,n=e;else{if("function"==typeof e)return!0;if("[object Array]"!==Object.prototype.toString.call(e)||2!==e.length||"object"!=typeof e[0]||"string"!=typeof e[1])return!1;o=e[0],i=e[1],n=(o.constructor&&function(e){var t=/\W*function\s+([\w\$]+)\s*\(/.exec(e);return t?t[1]:"(Anonymous)"}(o.constructor))+"::"+i}return!(!t&&"function"!=typeof o[i])&&(a&&(this.window[a]=n),!0)},exports.is_float=function(e){return!(+e!==e||isFinite(e)&&!(e%1))},exports.is_int=function(e){return e===+e&&isFinite(e)&&!(e%1)},exports.is_null=function(e){return null===e},exports.is_numeric=function(e){return("number"==typeof e||"string"==typeof e&&-1===" \n\r\t\f\v            ​\u2028\u2029　".indexOf(e.slice(-1)))&&""!==e&&!isNaN(e)},exports.is_object=function(e){return"[object Array]"!==Object.prototype.toString.call(e)&&(null!==e&&"object"==typeof e)},exports.is_resource=function(e){var t,a;return!(!e||"object"!=typeof e||!e.constructor||"PHPJS_Resource"!==(t=e.constructor,a=/\W*function\s+([\w\$]+)\s*\(/.exec(t),a?a[1]:"(Anonymous)"))},exports.is_scalar=function(e){return/boolean|number|string/.test(typeof e)},exports.is_string=function(e){return"string"==typeof e},exports.is_unicode=function(e){if("string"!=typeof e)return!1;for(var t=[],a=new RegExp("[\ud800-\udbff]([sS])","g"),n=new RegExp("([sS])[\udc00-\udfff]","g"),o=new RegExp("^[\udc00-\udfff]$"),i=new RegExp("^[\ud800-\udbff]$");null!==(t=a.exec(e));)if(!t[1]||!t[1].match(o))return!1;for(;null!==(t=n.exec(e));)if(!t[1]||!t[1].match(i))return!1;return!0},exports.isset=function(){var e,t=arguments,a=t.length,n=0;if(0===a)throw new Error("Empty isset");for(;n!==a;){if(t[n]===e||null===t[n])return!1;n++}return!0},exports.serialize=function(e){var t,a,n,o="",i=0;switch(_getType=function(e){var t,a,n,o,i=typeof e;if("object"===i&&!e)return"null";if("object"===i){if(!e.constructor)return"object";for(a in(t=(n=e.constructor.toString()).match(/(\w+)\(/))&&(n=t[1].toLowerCase()),o=["boolean","number","string","array"])if(n==o[a]){i=o[a];break}}return i},type=_getType(e),type){case"function":t="";break;case"boolean":t="b:"+(e?"1":"0");break;case"number":t=(Math.round(e)==e?"i":"d")+":"+e;break;case"string":t="s:"+function(e){var t=0,a=0,n=e.length,o="";for(a=0;a<n;a++)t+=(o=e.charCodeAt(a))<128?1:o<2048?2:3;return t}(e)+':"'+e+'"';break;case"array":case"object":for(a in t="a",e)if(e.hasOwnProperty(a)){if("function"===_getType(e[a]))continue;n=a.match(/^[0-9]+$/)?parseInt(a,10):a,o+=this.serialize(n)+this.serialize(e[a]),i++}t+=":"+i+":{"+o+"}";break;case"undefined":default:t="N"}return"object"!==type&&"array"!==type&&(t+=";"),t},exports.settype=function(e,t){var a,n,o,i,r=function(e){return"object"==typeof e&&"number"==typeof e.length&&!e.propertyIsEnumerable("length")&&"function"==typeof e.splice};a=this[e]?this[e]:e;try{switch(t){case"boolean":if(r(a)&&0===a.length)this[e]=!1;else if("0"===a)this[e]=!1;else if("object"!=typeof a||r(a))this[e]=!!a;else{var s=!1;for(o in a)s=!0;this[e]=s}break;case"integer":"number"==typeof a?this[e]=parseInt(a,10):"string"==typeof a?(n=a.match(/^([+\-]?)(\d+)/),this[e]=n?parseInt(a,10):0):!0===a?this[e]=1:!1===a||null===a||r(a)&&0===a.length?this[e]=0:"object"==typeof a&&(this[e]=1);break;case"float":"string"==typeof a?(n=a.match(/^([+\-]?)(\d+(\.\d+)?|\.\d+)([eE][+\-]?\d+)?/),this[e]=n?parseFloat(a,10):0):!0===a?this[e]=1:!1===a||null===a||r(a)&&0===a.length?this[e]=0:"object"==typeof a&&(this[e]=1);break;case"string":null===a||!1===a?this[e]="":r(a)?this[e]="Array":"object"==typeof a?this[e]="Object":!0===a?this[e]="1":this[e]+="";break;case"array":null===a?this[e]=[]:"object"!=typeof a&&(this[e]=[a]);break;case"object":if(null===a)this[e]={};else if(r(a)){for(o=0,i={};o<a.length;o++)i[o]=a;this[e]=i}else"object"!=typeof a&&(this[e]={scalar:a});break;case"null":delete this[e]}return!0}catch(e){return!1}},exports.unserialize=function(e){var t=this,a=function(e){var t=e.charCodeAt(0);return t<128?0:t<2048?1:2};return error=function(e,a,n,o){throw new t.window[e](a,n,o)},read_until=function(e,t,a){for(var n=2,o=[],i=e.slice(t,t+1);i!=a;)n+t>e.length&&error("Error","Invalid"),o.push(i),i=e.slice(t+(n-1),t+n),n+=1;return[o.length,o.join("")]},read_chrs=function(e,t,n){var o,i,r;for(r=[],o=0;o<n;o++)i=e.slice(t+(o-1),t+o),r.push(i),n-=a(i);return[r.length,r.join("")]},_unserialize=function(e,t){var a,n,o,i,r,s,u,c,l,p,m,f,h,d,g,b,k,v,y=0,j=function(e){return e};switch(t||(t=0),n=t+2,a=e.slice(t,t+1).toLowerCase()){case"i":j=function(e){return parseInt(e,10)},y=(l=read_until(e,n,";"))[0],c=l[1],n+=y+1;break;case"b":j=function(e){return 0!==parseInt(e,10)},y=(l=read_until(e,n,";"))[0],c=l[1],n+=y+1;break;case"d":j=function(e){return parseFloat(e)},y=(l=read_until(e,n,";"))[0],c=l[1],n+=y+1;break;case"n":c=null;break;case"s":y=(p=read_until(e,n,":"))[0],m=p[1],n+=y+2,y=(l=read_chrs(e,n+1,parseInt(m,10)))[0],c=l[1],n+=y+2,y!=parseInt(m,10)&&y!=c.length&&error("SyntaxError","String length mismatch");break;case"a":for(c={},y=(o=read_until(e,n,":"))[0],i=o[1],n+=y+2,s=parseInt(i,10),r=!0,f=0;f<s;f++)g=(d=_unserialize(e,n))[1],h=d[2],n+=g,k=(b=_unserialize(e,n))[1],v=b[2],n+=k,h!==f&&(r=!1),c[h]=v;if(r){for(u=new Array(s),f=0;f<s;f++)u[f]=c[f];c=u}n+=1;break;default:error("SyntaxError","Unknown / Unhandled data type(s): "+a)}return[a,n-t,j(c)]},_unserialize(e+"",0)[2]},exports.xdiff_string_diff=function(e,t,a,n){var o,i,r,s,u,c,l=0,p=0,m=0,f=Number.POSITIVE_INFINITY,h=function(e){if("string"!=typeof e)throw new Error("String parameter required");return e.replace(/(^\s*)|(\s*$)/g,"")},d=function(e){var t,a,n=arguments,o=arguments.length,i=["number","boolean","string","function","object","undefined"],r=typeof e;if("string"!==r&&"function"!==r)throw new Error("Bad type parameter");if(o<2)throw new Error("Too few arguments");if("string"===r){if(""===(e=h(e)))throw new Error("Bad type parameter");for(a=0;a<i.length;a++)if(i[a]==e){for(t=1;t<o;t++)if(typeof n[t]!==e)throw new Error("Bad type");return}throw new Error("Bad type parameter")}for(t=1;t<o;t++)if(!(n[t]instanceof e))throw new Error("Bad type")},g=function(e){var t,a,n=arguments,o=arguments.length,i=["number","boolean","string","function","object","undefined"],r=typeof e;if("string"!==r&&"function"!==r)throw new Error("Bad type parameter");if(o<2)throw new Error("Too few arguments");if("string"===r){if(""===(e=h(e)))return!1;for(a=0;a<i.length;a++)if(i[a]==e){for(t=1;t<o;t++)if(typeof n[t]!=e)return!1;return!0}throw new Error("Bad type parameter")}for(t=1;t<o;t++)if(!(n[t]instanceof e))return!1;return!0},b=function(e,t){var a,n=[];for(d("number",e),a=0;a<e;a++)n.push(t);return n},k=function(e){return d("string",e),""===e?[]:e.split("\n")},v=function(e){return g(Array,e)&&0===e.length};if(!1===g("string",e,t))return!1;if(e==t)return"";("number"!=typeof a||a>f||a<0)&&(a=3),u=k(e),c=k(t);var y=u.length,j=c.length,w=b(y,!1),_=b(j,!1),A=function(e,t,a,n){if(!g(Array,e,t))throw new Error("Array parameters are required");if(v(e)||v(t))return[];var o=function(e,t){var a,n,o,i=b(t.length+1,0);for(a=0;a<e.length;a++)for(o=i.slice(0),n=0;n<t.length;n++)e[a]===t[n]?i[n+1]=o[n]+1:i[n+1]=Math.max(i[n],o[n+1]);return i},i=function(e,t,a,n){var r,s,u,c,l,m,f,h,g,b=e.length,k=n.length;if(0===b)return[];if(1===b)return function(e,t){var a;for(d(Array,e),a=0;a<e.length;a++)if(e[a]===t)return!0;return!1}(n,e[0])?(a[t]=!0,[e[0]]):[];for(r=Math.floor(b/2),s=e.slice(0,r),u=e.slice(r),c=o(s,n),l=o(u.slice(0).reverse(),n.slice(0).reverse()),m=0,f=0,p=0;p<=k;p++)c[p]+l[k-p]>f&&(m=p,f=c[p]+l[k-p]);return h=n.slice(0,m),g=n.slice(m),i(s,t,a,h).concat(i(u,t+r,a,g))};return i(e,0,a,t),i(t,0,n,e)}(u,c,w,_).length,x="";if(0===A){for(x="@@ -"+(y>0?"1":"0")+","+y+" +"+(j>0?"1":"0")+","+j+" @@",l=0;l<y;l++)x+="\n-"+u[l];for(p=0;p<j;p++)x+="\n+"+c[p];return x}for(var z,S=[],C=[],E=[],T=[],I=function(e){if(0===e.length||0===a)return[];var t=Math.max(e.length-a,0);return e.slice(t)};l<y&&!0===w[l]&&!0===_[l];)S.push(u[l]),l++;for(m=l,o=l,i=p=l,r=l,s=p;l<y||p<j;){for(;l<y&&!1===w[l];)l++;for(r=l;p<j&&!1===_[p];)p++;for(s=p,C=[];l<y&&!0===w[l]&&p<j&&!0===_[p];)C.push(u[l]),m++,l++,p++;if(m>=A||C.length>=2*a){for(C.length<2*a&&(C=[],l=y,p=j,r=y,s=j),E=I(S),T=0===(z=C).length||0===a?[]:z.slice(0,Math.min(a,z.length)),o-=E.length,i-=E.length,x+="@@ -"+(o+1)+","+((r+=T.length)-o)+" +"+(i+1)+","+((s+=T.length)-i)+" @@\n";o<r||i<s;)o<r&&!0===w[o]&&!0===_[i]?(x+=" "+u[o]+"\n",o++,i++):o<r&&!1===w[o]?(x+="-"+u[o]+"\n",o++):i<s&&!1===_[i]&&(x+="+"+c[i]+"\n",i++);o=l,i=p,S=C}}return x.length>0&&"\n"===x.charAt(x.length)&&(x=x.slice(0,-1)),x},exports.xdiff_string_patch=function(e,t,a,n){var o=function(e){return(e.global?"g":"")+(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.extended?"x":"")+(e.sticky?"y":"")},i=function(e,t){if(!(t instanceof RegExp))return String.prototype.split.apply(e,arguments);var a,n,i=String(e),r=[],s=0,u=1/0,c=t._xregexp,l=new RegExp(t.source,o(t)+"g");for(c&&(l._xregexp={source:c.source,captureNames:c.captureNames?c.captureNames.slice(0):null});(a=l.exec(i))&&!(l.lastIndex>s&&(r.push(i.slice(s,a.index)),a.length>1&&a.index<i.length&&Array.prototype.push.apply(r,a.slice(1)),n=a[0].length,s=l.lastIndex,r.length>=u));)l.lastIndex===a.index&&l.lastIndex++;return s===i.length?l.test("")&&!n||r.push(""):r.push(i.slice(s)),r.length>u?r.slice(0,u):r},r=0,s=0,u=[],c=0,l=/^@@\s+-(\d+),(\d+)\s+\+(\d+),(\d+)\s+@@$/,p=/\r?\n/,m=i(t.replace(/(\r?\n)+$/,""),p),f=i(e,p),h=[],d=0,g=0,b={XDIFF_PATCH_NORMAL:1,XDIFF_PATCH_REVERSE:2,XDIFF_PATCH_IGNORESPACE:4};if("string"!=typeof e||!t)return!1;if(a||(a="XDIFF_PATCH_NORMAL"),"number"!=typeof a){for(a=[].concat(a),r=0;r<a.length;r++)b[a[r]]&&(g|=b[a[r]]);a=g}if(a&b.XDIFF_PATCH_NORMAL){for(r=0,s=m.length;r<s;r++)if(u=m[r].match(l)){for(c=d,d=u[1]-1;c<d;)h[h.length]=f[c++];for(;m[++r]&&null===l.exec(m[r]);)switch(m[r].charAt(0)){case"-":++d;break;case"+":h[h.length]=m[r].slice(1);break;case" ":h[h.length]=f[d++];break;default:throw"Unrecognized initial character in unidiff line"}m[r]&&r--}for(;d>0&&d<f.length;)h[h.length]=f[d++]}else if(a&b.XDIFF_PATCH_REVERSE){for(r=0,s=m.length;r<s;r++)if(u=m[r].match(l)){for(c=d,d=u[3]-1;c<d;)h[h.length]=f[c++];for(;m[++r]&&null===l.exec(m[r]);)switch(m[r].charAt(0)){case"-":h[h.length]=m[r].slice(1);break;case"+":++d;break;case" ":h[h.length]=f[d++];break;default:throw"Unrecognized initial character in unidiff line"}m[r]&&r--}for(;d>0&&d<f.length;)h[h.length]=f[d++]}return"string"==typeof n&&(this.window[n]=""),h.join("\n")},exports.utf8_decode=function(e){var t=[],a=0,n=0,o=0,i=0,r=0;for(e+="";a<e.length;)(o=e.charCodeAt(a))<=191?(t[n++]=String.fromCharCode(o),a++):o<=223?(i=e.charCodeAt(a+1),t[n++]=String.fromCharCode((31&o)<<6|63&i),a+=2):o<=239?(i=e.charCodeAt(a+1),r=e.charCodeAt(a+2),t[n++]=String.fromCharCode((15&o)<<12|(63&i)<<6|63&r),a+=3):(o=(7&o)<<18|(63&(i=e.charCodeAt(a+1)))<<12|(63&(r=e.charCodeAt(a+2)))<<6|63&e.charCodeAt(a+3),o-=65536,t[n++]=String.fromCharCode(55296|o>>10&1023),t[n++]=String.fromCharCode(56320|1023&o),a+=4);return t.join("")},exports.utf8_encode=function(e){if(null==e)return"";var t,a,n,o=e+"",i="";t=a=0,n=o.length;for(var r=0;r<n;r++){var s=o.charCodeAt(r),u=null;if(s<128)a++;else if(s>127&&s<2048)u=String.fromCharCode(s>>6|192,63&s|128);else if(55296!=(63488&s))u=String.fromCharCode(s>>12|224,s>>6&63|128,63&s|128);else{if(55296!=(64512&s))throw new RangeError("Unmatched trail surrogate at "+r);var c=o.charCodeAt(++r);if(56320!=(64512&c))throw new RangeError("Unmatched lead surrogate at "+(r-1));s=((1023&s)<<10)+(1023&c)+65536,u=String.fromCharCode(s>>18|240,s>>12&63|128,s>>6&63|128,63&s|128)}null!==u&&(a>t&&(i+=o.slice(t,a)),i+=u,t=a=r+1)}return a>t&&(i+=o.slice(t,n)),i},exports.array_flip=function(e){var t,a={};if(e&&"object"==typeof e&&e.change_key_case)return e.flip();for(t in e)e.hasOwnProperty(t)&&(a[e[t]]=t);return a},exports.array_merge_recursive=function(e,t){var a="";if(e&&"[object Array]"===Object.prototype.toString.call(e)&&t&&"[object Array]"===Object.prototype.toString.call(t))for(a in t)e.push(t[a]);else if(e&&e instanceof Object&&t&&t instanceof Object)for(a in t)a in e&&"object"==typeof e[a]&&"object"==typeof t?e[a]=this.array_merge(e[a],t[a]):e[a]=t[a];return e},exports.array_search=function(e,t,a){var n=!!a,o="";if(t&&"object"==typeof t&&t.change_key_case)return t.search(e,a);if("object"==typeof e&&e.exec){if(!n){var i="i"+(e.global?"g":"")+(e.multiline?"m":"")+(e.sticky?"y":"");e=new RegExp(e.source,i)}for(o in t)if(e.test(t[o]))return o;return!1}for(o in t)if(n&&t[o]===e||!n&&t[o]==e)return o;return!1},exports.array_slice=function(e,t,a,n){var o="";if("[object Array]"!==Object.prototype.toString.call(e)||n&&0!==t){var i=0,r={};for(o in e)i+=1,r[o]=e[o];e=r,t=t<0?i+t:t,a=void 0===a?i:a<0?i+a-t:a;var s={},u=!1,c=-1,l=0,p=0;for(o in e){if(++c,l>=a)break;c==t&&(u=!0),u&&(++l,this.is_int(o)&&!n?s[p++]=e[o]:s[o]=e[o])}return s}return void 0===a?e.slice(t):a>=0?e.slice(t,t+a):e.slice(t,a)},exports.array_splice=function(e,t,a,n){var o=function(e,t,a){if(void 0!==e[t]){var n=t;(t+=1)===a&&(t+=1),e[t=o(e,t,a)]=e[n],delete e[n]}return t};if(n&&"object"!=typeof n&&(n=[n]),void 0===a?a=t>=0?e.length-t:-t:a<0&&(a=(t>=0?e.length-t:-t)+a),"[object Array]"!==Object.prototype.toString.call(e)){var i=0,r=-1,s=[],u={},c=-1,l=-1,p=!0,m=0,f="";for(f in e)i+=1;for(f in t=t>=0?t:i+t,e)if((r+=1)<t){if(this.is_int(f)){if(l+=1,parseInt(f,10)===l)continue;o(e,l,f),e[l]=e[f],delete e[f]}}else p&&this.is_int(f)?(s.push(e[f]),u[m++]=e[f]):(u[f]=e[f],p=!1),1,n&&n[++c]?e[f]=n[c]:delete e[f];return p?s:u}return n?(n.unshift(t,a),Array.prototype.splice.apply(e,n)):e.splice(t,a)},exports.array_walk=function(array,funcname,userdata){var key,value,ini;if(!array||"object"!=typeof array)return!1;if("object"==typeof array&&array.change_key_case)return arguments.length>2?array.walk(funcname,userdata):array.walk(funcname);try{if("function"==typeof funcname)for(key in array)arguments.length>2?funcname(array[key],key,userdata):funcname(array[key],key);else if("string"==typeof funcname)if(this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},ini=this.php_js.ini["phpjs.no-eval"],!ini||0===parseInt(ini.local_value,10)||ini.local_value.toLowerCase&&"off"===ini.local_value.toLowerCase())if(arguments.length>2)for(key in array)eval(funcname+"(array[key], key, userdata)");else for(key in array)eval(funcname+"(array[key], key)");else if(arguments.length>2)for(key in array)this.window[funcname](array[key],key,userdata);else for(key in array)this.window[funcname](array[key],key);else{if(!funcname||"object"!=typeof funcname||2!==funcname.length)return!1;var obj=funcname[0],func=funcname[1];if(arguments.length>2)for(key in array)obj[func](array[key],key,userdata);else for(key in array)obj[func](array[key],key)}}catch(e){return!1}return!0},exports.natcasesort=function(e){var t,a,n,o=[],i=this,r={};for(t in this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},r=(n=this.php_js.ini["phpjs.strictForIn"]&&this.php_js.ini["phpjs.strictForIn"].local_value&&"off"!==this.php_js.ini["phpjs.strictForIn"].local_value)?e:r,e)e.hasOwnProperty(t)&&(o.push([t,e[t]]),n&&delete e[t]);for(o.sort((function(e,t){return i.strnatcasecmp(e[1],t[1])})),a=0;a<o.length;a++)r[o[a][0]]=o[a][1];return n||r},exports.pos=function(e){return this.current(e)},exports.sizeof=function(e,t){return this.count(e,t)},exports.bcadd=function(e,t,a){var n,o,i,r=this._phpjs_shared_bc();return void 0===a&&(a=r.scale),a=a<0?0:a,r.bc_init_num(),r.bc_init_num(),i=r.bc_init_num(),n=r.php_str2num(e.toString()),o=r.php_str2num(t.toString()),(i=r.bc_add(n,o,a)).n_scale>a&&(i.n_scale=a),i.toString()},exports.bccomp=function(e,t,a){var n,o,i=this._phpjs_shared_bc();return void 0===a&&(a=i.scale),a=a<0?0:a,i.bc_init_num(),i.bc_init_num(),n=i.bc_str2num(e.toString(),a),o=i.bc_str2num(t.toString(),a),i.bc_compare(n,o,a)},exports.bcdiv=function(e,t,a){var n,o,i,r=this._phpjs_shared_bc();if(void 0===a&&(a=r.scale),a=a<0?0:a,r.bc_init_num(),r.bc_init_num(),i=r.bc_init_num(),n=r.php_str2num(e.toString()),o=r.php_str2num(t.toString()),-1===(i=r.bc_divide(n,o,a)))throw new Error(11,"(BC) Division by zero");return i.n_scale>a&&(i.n_scale=a),i.toString()},exports.bcmul=function(e,t,a){var n,o,i,r=this._phpjs_shared_bc();return void 0===a&&(a=r.scale),a=a<0?0:a,r.bc_init_num(),r.bc_init_num(),i=r.bc_init_num(),n=r.php_str2num(e.toString()),o=r.php_str2num(t.toString()),(i=r.bc_multiply(n,o,a)).n_scale>a&&(i.n_scale=a),i.toString()},exports.bcround=function(e,t){var a,n,o,i,r=this._phpjs_shared_bc();if(a=r.bc_init_num(),t>=(a=r.php_str2num(e.toString())).n_scale){for(;a.n_scale<t;)a.n_value[a.n_len+a.n_scale]=0,a.n_scale++;return a.toString()}return o=a.n_value[a.n_len+t],i=r.bc_init_num(),i=r.bc_new_num(1,t),o>=5?(i.n_value[i.n_len+i.n_scale-1]=1,a.n_sign==r.MINUS&&(i.n_sign=r.MINUS),n=r.bc_add(a,i,t)):n=a,n.n_scale>t&&(n.n_scale=t),n.toString()},exports.bcscale=function(e){var t=this._phpjs_shared_bc();return e=parseInt(e,10),!isNaN(e)&&(!(e<0)&&(t.scale=e,!0))},exports.bcsub=function(e,t,a){var n,o,i,r=this._phpjs_shared_bc();return void 0===a&&(a=r.scale),a=a<0?0:a,r.bc_init_num(),r.bc_init_num(),i=r.bc_init_num(),n=r.php_str2num(e.toString()),o=r.php_str2num(t.toString()),(i=r.bc_sub(n,o,a)).n_scale>a&&(i.n_scale=a),i.toString()},exports.date_parse=function(e){this.php_js=this.php_js||{};var t,a=this.php_js.warnings?this.php_js.warnings.length:null,n=this.php_js.errors?this.php_js.errors.length:null;try{this.php_js.date_parse_state=!0,t=this.strtotime(e),this.php_js.date_parse_state=!1}finally{if(!t)return!1}var o=new Date(1e3*t),i={warning_count:null!==a?this.php_js.warnings.slice(a).length:0,warnings:null!==a?this.php_js.warnings.slice(a):[],error_count:null!==n?this.php_js.errors.slice(n).length:0,errors:null!==n?this.php_js.errors.slice(n):[]};return i.year=o.getFullYear(),i.month=o.getMonth()+1,i.day=o.getDate(),i.hour=o.getHours(),i.minute=o.getMinutes(),i.second=o.getSeconds(),i.fraction=parseFloat("0."+o.getMilliseconds()),i.is_localtime=0!==o.getTimezoneOffset(),i},exports.gmdate=function(e,t){var a=void 0===t?new Date:"object"==typeof t?new Date(t):new Date(1e3*t);return t=Date.parse(a.toUTCString().slice(0,-4))/1e3,this.date(e,t)},exports.pathinfo=function(e,t){var a="",n="",o=0,i={},r=0,s=0,u=!1,c=!1,l=!1;if(!e)return!1;t||(t="PATHINFO_ALL");var p={PATHINFO_DIRNAME:1,PATHINFO_BASENAME:2,PATHINFO_EXTENSION:4,PATHINFO_FILENAME:8,PATHINFO_ALL:0};for(n in p)p.PATHINFO_ALL=p.PATHINFO_ALL|p[n];if("number"!=typeof t){for(t=[].concat(t),s=0;s<t.length;s++)p[t[s]]&&(o|=p[t[s]]);t=o}var m=function(e){var t=e+"",a=t.lastIndexOf(".")+1;return!!a&&(a!==t.length?t.substr(a):"")};if(t&p.PATHINFO_DIRNAME){var f=e.replace(/\\/g,"/").replace(/\/[^\/]*\/?$/,"");i.dirname=f===e?".":f}for(a in t&p.PATHINFO_BASENAME&&(!1===u&&(u=this.basename(e)),i.basename=u),t&p.PATHINFO_EXTENSION&&(!1===u&&(u=this.basename(e)),!1===c&&(c=m(u)),!1!==c&&(i.extension=c)),t&p.PATHINFO_FILENAME&&(!1===u&&(u=this.basename(e)),!1===c&&(c=m(u)),!1===l&&(l=u.slice(0,u.length-(c?c.length+1:!1===c?0:1))),i.filename=l),r=0,i)r++;return 1==r?i[a]:i},exports.i18n_loc_get_default=function(){try{this.php_js=this.php_js||{}}catch(e){this.php_js={}}return this.php_js.i18nLocale||(i18n_loc_set_default("en_US_POSIX"),"en_US_POSIX")},exports.setcookie=function(e,t,a,n,o,i){return this.setrawcookie(e,encodeURIComponent(t),a,n,o,i)},exports.chop=function(e,t){return this.rtrim(e,t)},exports.convert_uuencode=function(e){var t=function(e){return String.fromCharCode(e)};if(!e||""===e)return t(0);if(!this.is_scalar(e))return!1;for(var a=0,n=0,o=0,i=0,r="",s="",u="",c={},l=function(){for(o in c=e.substr(n,45))c[o]=c[o].charCodeAt(0);return 0!=c.length?c.length:0};0!==l();){for(o in a=l(),n+=45,r+=t(a+32),c){for(s=c[o].charCodeAt(0).toString(2);s.length<8;)s="0"+s;u+=s}for(;u.length%6;)u+="0";for(o=0;o<=u.length/6-1;o++)r+=t("000000"==(s=u.substr(i,6))?96:parseInt(s,2)+32),i+=6;i=0,u="",r+="\n"}return r+=t(96)+"\n"},exports.crc32=function(e){var t=0,a=0;t^=-1;for(var n=0,o=(e=this.utf8_encode(e)).length;n<o;n++)a=255&(t^e.charCodeAt(n)),t=t>>>8^"0x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substr(9*a,8);return-1^t},exports.html_entity_decode=function(e,t){var a={},n="",o="",i="";if(o=e.toString(),!1===(a=this.get_html_translation_table("HTML_ENTITIES",t)))return!1;for(n in delete a["&"],a["&"]="&amp;",a)i=a[n],o=o.split(i).join(n);return o=o.split("&#039;").join("'")},exports.htmlentities=function(e,t,a,n){var o=this.get_html_translation_table("HTML_ENTITIES",t),i="";if(e=null==e?"":e+"",!o)return!1;if(t&&"ENT_QUOTES"===t&&(o["'"]="&#039;"),n||null==n)for(i in o)o.hasOwnProperty(i)&&(e=e.split(i).join(o[i]));else e=e.replace(/([\s\S]*?)(&(?:#\d+|#x[\da-f]+|[a-zA-Z][\da-z]*);|$)/g,(function(e,t,a){for(i in o)o.hasOwnProperty(i)&&(t=t.split(i).join(o[i]));return t+a}));return e},exports.join=function(e,t){return this.implode(e,t)},exports.md5=function(e){var t,a,n,o,i,r,s,u,c,l,p,m=function(e,t){return e<<t|e>>>32-t},f=function(e,t){var a,n,o,i,r;return o=2147483648&e,i=2147483648&t,r=(1073741823&e)+(1073741823&t),(a=1073741824&e)&(n=1073741824&t)?2147483648^r^o^i:a|n?1073741824&r?3221225472^r^o^i:1073741824^r^o^i:r^o^i},h=function(e,t,a,n,o,i,r){return e=f(e,f(f(function(e,t,a){return e&t|~e&a}(t,a,n),o),r)),f(m(e,i),t)},d=function(e,t,a,n,o,i,r){return e=f(e,f(f(function(e,t,a){return e&a|t&~a}(t,a,n),o),r)),f(m(e,i),t)},g=function(e,t,a,n,o,i,r){return e=f(e,f(f(function(e,t,a){return e^t^a}(t,a,n),o),r)),f(m(e,i),t)},b=function(e,t,a,n,o,i,r){return e=f(e,f(f(function(e,t,a){return t^(e|~a)}(t,a,n),o),r)),f(m(e,i),t)},k=function(e){var t,a="",n="";for(t=0;t<=3;t++)a+=(n="0"+(e>>>8*t&255).toString(16)).substr(n.length-2,2);return a};for(u=1732584193,c=4023233417,l=2562383102,p=271733878,t=(a=function(e){for(var t,a=e.length,n=a+8,o=16*((n-n%64)/64+1),i=new Array(o-1),r=0,s=0;s<a;)r=s%4*8,i[t=(s-s%4)/4]=i[t]|e.charCodeAt(s)<<r,s++;return r=s%4*8,i[t=(s-s%4)/4]=i[t]|128<<r,i[o-2]=a<<3,i[o-1]=a>>>29,i}(e=this.utf8_encode(e))).length,n=0;n<t;n+=16)o=u,i=c,r=l,s=p,u=h(u,c,l,p,a[n+0],7,3614090360),p=h(p,u,c,l,a[n+1],12,3905402710),l=h(l,p,u,c,a[n+2],17,606105819),c=h(c,l,p,u,a[n+3],22,3250441966),u=h(u,c,l,p,a[n+4],7,4118548399),p=h(p,u,c,l,a[n+5],12,1200080426),l=h(l,p,u,c,a[n+6],17,2821735955),c=h(c,l,p,u,a[n+7],22,4249261313),u=h(u,c,l,p,a[n+8],7,1770035416),p=h(p,u,c,l,a[n+9],12,2336552879),l=h(l,p,u,c,a[n+10],17,4294925233),c=h(c,l,p,u,a[n+11],22,2304563134),u=h(u,c,l,p,a[n+12],7,1804603682),p=h(p,u,c,l,a[n+13],12,4254626195),l=h(l,p,u,c,a[n+14],17,2792965006),u=d(u,c=h(c,l,p,u,a[n+15],22,1236535329),l,p,a[n+1],5,4129170786),p=d(p,u,c,l,a[n+6],9,3225465664),l=d(l,p,u,c,a[n+11],14,643717713),c=d(c,l,p,u,a[n+0],20,3921069994),u=d(u,c,l,p,a[n+5],5,3593408605),p=d(p,u,c,l,a[n+10],9,38016083),l=d(l,p,u,c,a[n+15],14,3634488961),c=d(c,l,p,u,a[n+4],20,3889429448),u=d(u,c,l,p,a[n+9],5,568446438),p=d(p,u,c,l,a[n+14],9,3275163606),l=d(l,p,u,c,a[n+3],14,4107603335),c=d(c,l,p,u,a[n+8],20,1163531501),u=d(u,c,l,p,a[n+13],5,2850285829),p=d(p,u,c,l,a[n+2],9,4243563512),l=d(l,p,u,c,a[n+7],14,1735328473),u=g(u,c=d(c,l,p,u,a[n+12],20,2368359562),l,p,a[n+5],4,4294588738),p=g(p,u,c,l,a[n+8],11,2272392833),l=g(l,p,u,c,a[n+11],16,1839030562),c=g(c,l,p,u,a[n+14],23,4259657740),u=g(u,c,l,p,a[n+1],4,2763975236),p=g(p,u,c,l,a[n+4],11,1272893353),l=g(l,p,u,c,a[n+7],16,4139469664),c=g(c,l,p,u,a[n+10],23,3200236656),u=g(u,c,l,p,a[n+13],4,681279174),p=g(p,u,c,l,a[n+0],11,3936430074),l=g(l,p,u,c,a[n+3],16,3572445317),c=g(c,l,p,u,a[n+6],23,76029189),u=g(u,c,l,p,a[n+9],4,3654602809),p=g(p,u,c,l,a[n+12],11,3873151461),l=g(l,p,u,c,a[n+15],16,530742520),u=b(u,c=g(c,l,p,u,a[n+2],23,3299628645),l,p,a[n+0],6,4096336452),p=b(p,u,c,l,a[n+7],10,1126891415),l=b(l,p,u,c,a[n+14],15,2878612391),c=b(c,l,p,u,a[n+5],21,4237533241),u=b(u,c,l,p,a[n+12],6,1700485571),p=b(p,u,c,l,a[n+3],10,2399980690),l=b(l,p,u,c,a[n+10],15,4293915773),c=b(c,l,p,u,a[n+1],21,2240044497),u=b(u,c,l,p,a[n+8],6,1873313359),p=b(p,u,c,l,a[n+15],10,4264355552),l=b(l,p,u,c,a[n+6],15,2734768916),c=b(c,l,p,u,a[n+13],21,1309151649),u=b(u,c,l,p,a[n+4],6,4149444226),p=b(p,u,c,l,a[n+11],10,3174756917),l=b(l,p,u,c,a[n+2],15,718787259),c=b(c,l,p,u,a[n+9],21,3951481745),u=f(u,o),c=f(c,i),l=f(l,r),p=f(p,s);return(k(u)+k(c)+k(l)+k(p)).toLowerCase()},exports.md5_file=function(e){var t;return!!(t=this.file_get_contents(e))&&this.md5(t)},exports.printf=function(){var e,t,a=this.window.document,n="",o="http://www.w3.org/1999/xhtml";return!!(e=a.getElementsByTagNameNS?a.getElementsByTagNameNS(o,"body")[0]?a.getElementsByTagNameNS(o,"body")[0]:a.documentElement.lastChild:a.getElementsByTagName("body")[0])&&(n=this.sprintf.apply(this,arguments),t=a.createTextNode(n),e.appendChild(t),n.length)},exports.setlocale=function(e,t){var a="",n=[],o=0,i=this.window.document,r=function e(t){if(t instanceof RegExp)return new RegExp(t);if(t instanceof Date)return new Date(t);var a={};for(var n in t)"object"==typeof t[n]?a[n]=e(t[n]):a[n]=t[n];return a};try{this.php_js=this.php_js||{}}catch(e){this.php_js={}}var s=this.php_js;if(s.locales||(s.locales={},s.locales.en={LC_COLLATE:function(e,t){return e==t?0:e>t?1:-1},LC_CTYPE:{an:/^[A-Za-z\d]+$/g,al:/^[A-Za-z]+$/g,ct:/^[\u0000-\u001F\u007F]+$/g,dg:/^[\d]+$/g,gr:/^[\u0021-\u007E]+$/g,lw:/^[a-z]+$/g,pr:/^[\u0020-\u007E]+$/g,pu:/^[\u0021-\u002F\u003A-\u0040\u005B-\u0060\u007B-\u007E]+$/g,sp:/^[\f\n\r\t\v ]+$/g,up:/^[A-Z]+$/g,xd:/^[A-Fa-f\d]+$/g,CODESET:"UTF-8",lower:"abcdefghijklmnopqrstuvwxyz",upper:"ABCDEFGHIJKLMNOPQRSTUVWXYZ"},LC_TIME:{a:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],A:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],b:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],B:["January","February","March","April","May","June","July","August","September","October","November","December"],c:"%a %d %b %Y %r %Z",p:["AM","PM"],P:["am","pm"],r:"%I:%M:%S %p",x:"%m/%d/%Y",X:"%r",alt_digits:"",ERA:"",ERA_YEAR:"",ERA_D_T_FMT:"",ERA_D_FMT:"",ERA_T_FMT:""},LC_MONETARY:{int_curr_symbol:"USD",currency_symbol:"$",mon_decimal_point:".",mon_thousands_sep:",",mon_grouping:[3],positive_sign:"",negative_sign:"-",int_frac_digits:2,frac_digits:2,p_cs_precedes:1,p_sep_by_space:0,n_cs_precedes:1,n_sep_by_space:0,p_sign_posn:3,n_sign_posn:0},LC_NUMERIC:{decimal_point:".",thousands_sep:",",grouping:[3]},LC_MESSAGES:{YESEXPR:"^[yY].*",NOEXPR:"^[nN].*",YESSTR:"",NOSTR:""},nplurals:function(e){return 1!==e?1:0}},s.locales.en_US=r(s.locales.en),s.locales.en_US.LC_TIME.c="%a %d %b %Y %r %Z",s.locales.en_US.LC_TIME.x="%D",s.locales.en_US.LC_TIME.X="%r",s.locales.en_US.LC_MONETARY.int_curr_symbol="USD ",s.locales.en_US.LC_MONETARY.p_sign_posn=1,s.locales.en_US.LC_MONETARY.n_sign_posn=1,s.locales.en_US.LC_MONETARY.mon_grouping=[3,3],s.locales.en_US.LC_NUMERIC.thousands_sep="",s.locales.en_US.LC_NUMERIC.grouping=[],s.locales.en_GB=r(s.locales.en),s.locales.en_GB.LC_TIME.r="%l:%M:%S %P %Z",s.locales.en_AU=r(s.locales.en_GB),s.locales.C=r(s.locales.en),s.locales.C.LC_CTYPE.CODESET="ANSI_X3.4-1968",s.locales.C.LC_MONETARY={int_curr_symbol:"",currency_symbol:"",mon_decimal_point:"",mon_thousands_sep:"",mon_grouping:[],p_cs_precedes:127,p_sep_by_space:127,n_cs_precedes:127,n_sep_by_space:127,p_sign_posn:127,n_sign_posn:127,positive_sign:"",negative_sign:"",int_frac_digits:127,frac_digits:127},s.locales.C.LC_NUMERIC={decimal_point:".",thousands_sep:"",grouping:[]},s.locales.C.LC_TIME.c="%a %b %e %H:%M:%S %Y",s.locales.C.LC_TIME.x="%m/%d/%y",s.locales.C.LC_TIME.X="%H:%M:%S",s.locales.C.LC_MESSAGES.YESEXPR="^[yY]",s.locales.C.LC_MESSAGES.NOEXPR="^[nN]",s.locales.fr=r(s.locales.en),s.locales.fr.nplurals=function(e){return e>1?1:0},s.locales.fr.LC_TIME.a=["dim","lun","mar","mer","jeu","ven","sam"],s.locales.fr.LC_TIME.A=["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],s.locales.fr.LC_TIME.b=["jan","fév","mar","avr","mai","jun","jui","aoû","sep","oct","nov","déc"],s.locales.fr.LC_TIME.B=["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"],s.locales.fr.LC_TIME.c="%a %d %b %Y %T %Z",s.locales.fr.LC_TIME.p=["",""],s.locales.fr.LC_TIME.P=["",""],s.locales.fr.LC_TIME.x="%d.%m.%Y",s.locales.fr.LC_TIME.X="%T",s.locales.fr_CA=r(s.locales.fr),s.locales.fr_CA.LC_TIME.x="%Y-%m-%d"),!s.locale){s.locale="en_US";var u="http://www.w3.org/1999/xhtml",c="http://www.w3.org/XML/1998/namespace";i.getElementsByTagNameNS&&i.getElementsByTagNameNS(u,"html")[0]?i.getElementsByTagNameNS(u,"html")[0].getAttributeNS&&i.getElementsByTagNameNS(u,"html")[0].getAttributeNS(c,"lang")?s.locale=i.getElementsByTagName(u,"html")[0].getAttributeNS(c,"lang"):i.getElementsByTagNameNS(u,"html")[0].lang&&(s.locale=i.getElementsByTagNameNS(u,"html")[0].lang):i.getElementsByTagName("html")[0]&&i.getElementsByTagName("html")[0].lang&&(s.locale=i.getElementsByTagName("html")[0].lang)}if(s.locale=s.locale.replace("-","_"),s.locale in s.locales||s.locale.replace(/_[a-zA-Z]+$/,"")in s.locales&&(s.locale=s.locale.replace(/_[a-zA-Z]+$/,"")),s.localeCategories||(s.localeCategories={LC_COLLATE:s.locale,LC_CTYPE:s.locale,LC_MONETARY:s.locale,LC_NUMERIC:s.locale,LC_TIME:s.locale,LC_MESSAGES:s.locale}),null===t||""===t)t=this.getenv(e)||this.getenv("LANG");else if("[object Array]"===Object.prototype.toString.call(t))for(o=0;o<t.length;o++){if(t[o]in this.php_js.locales){t=t[o];break}if(o===t.length-1)return!1}if("0"===t||0===t){if("LC_ALL"===e){for(a in this.php_js.localeCategories)n.push(a+"="+this.php_js.localeCategories[a]);return n.join(";")}return this.php_js.localeCategories[e]}if(!(t in this.php_js.locales))return!1;if("LC_ALL"===e)for(a in this.php_js.localeCategories)this.php_js.localeCategories[a]=t;else this.php_js.localeCategories[e]=t;return t},exports.sha1=function(e){var t,a,n,o,i,r,s,u,c,l=function(e,t){return e<<t|e>>>32-t},p=function(e){var t,a="";for(t=7;t>=0;t--)a+=(e>>>4*t&15).toString(16);return a},m=new Array(80),f=1732584193,h=4023233417,d=2562383102,g=271733878,b=3285377520,k=(e=this.utf8_encode(e)).length,v=[];for(a=0;a<k-3;a+=4)n=e.charCodeAt(a)<<24|e.charCodeAt(a+1)<<16|e.charCodeAt(a+2)<<8|e.charCodeAt(a+3),v.push(n);switch(k%4){case 0:a=2147483648;break;case 1:a=e.charCodeAt(k-1)<<24|8388608;break;case 2:a=e.charCodeAt(k-2)<<24|e.charCodeAt(k-1)<<16|32768;break;case 3:a=e.charCodeAt(k-3)<<24|e.charCodeAt(k-2)<<16|e.charCodeAt(k-1)<<8|128}for(v.push(a);v.length%16!=14;)v.push(0);for(v.push(k>>>29),v.push(k<<3&4294967295),t=0;t<v.length;t+=16){for(a=0;a<16;a++)m[a]=v[t+a];for(a=16;a<=79;a++)m[a]=l(m[a-3]^m[a-8]^m[a-14]^m[a-16],1);for(o=f,i=h,r=d,s=g,u=b,a=0;a<=19;a++)c=l(o,5)+(i&r|~i&s)+u+m[a]+1518500249&4294967295,u=s,s=r,r=l(i,30),i=o,o=c;for(a=20;a<=39;a++)c=l(o,5)+(i^r^s)+u+m[a]+1859775393&4294967295,u=s,s=r,r=l(i,30),i=o,o=c;for(a=40;a<=59;a++)c=l(o,5)+(i&r|i&s|r&s)+u+m[a]+2400959708&4294967295,u=s,s=r,r=l(i,30),i=o,o=c;for(a=60;a<=79;a++)c=l(o,5)+(i^r^s)+u+m[a]+3395469782&4294967295,u=s,s=r,r=l(i,30),i=o,o=c;f=f+o&4294967295,h=h+i&4294967295,d=d+r&4294967295,g=g+s&4294967295,b=b+u&4294967295}return(c=p(f)+p(h)+p(d)+p(g)+p(b)).toLowerCase()},exports.sha1_file=function(e){var t=this.file_get_contents(e);return this.sha1(t)},exports.split=function(e,t){return this.explode(e,t)},exports.strchr=function(e,t,a){return this.strstr(e,t,a)},exports.strnatcmp=function(e,t,a){var n=0;null==a&&(a=!1);var o=function(e){var t,a=[],n="",o="",i=0,r=!0;for(t=e.length,i=0;i<t;i++)(o=e.substring(i,i+1)).match(/\d/)?(r&&(n.length>0&&(a[a.length]=n,n=""),r=!1),n+=o):0==r&&"."===o&&i<e.length-1&&e.substring(i+1,i+2).match(/\d/)?(a[a.length]=n,n=""):(0==r&&(n.length>0&&(a[a.length]=parseInt(n,10),n=""),r=!0),n+=o);return n.length>0&&(a[a.length]=r?n:parseInt(n,10)),a},i=o(e+""),r=o(t+""),s=i.length,u=!0,c=-1,l=0;for(s>r.length&&(s=r.length,c=1),n=0;n<s;n++)if(isNaN(i[n])){if(!isNaN(r[n]))return u?1:-1;if(u=!0,0!=(l=this.strcmp(i[n],r[n])))return l}else{if(isNaN(r[n]))return u?-1:1;if(u||a){if(0!=(l=i[n]-r[n]))return l}else if(0!=(l=this.strcmp(i[n].toString(),r[n].toString())))return l;u=!1}return c},exports.vprintf=function(e,t){var a,n,o,i=this.window.document,r="http://www.w3.org/1999/xhtml";return!!(a=i.getElementsByTagNameNS?i.getElementsByTagNameNS(r,"body")[0]?i.getElementsByTagNameNS(r,"body")[0]:i.documentElement.lastChild:i.getElementsByTagName("body")[0])&&(o=this.sprintf.apply(this,[e].concat(t)),n=i.createTextNode(o),a.appendChild(n),o.length)},exports.vsprintf=function(e,t){return this.sprintf.apply(this,[e].concat(t))},exports.get_headers=function(e,t){var a=this.window.ActiveXObject?new ActiveXObject("Microsoft.XMLHTTP"):new XMLHttpRequest;if(!a)throw new Error("XMLHttpRequest not supported");var n,o,i,r=0;if(ß,a.open("HEAD",e,!1),a.send(null),a.readyState<3)return!1;for(var s in n=(n=a.getAllResponseHeaders()).split("\n"),o=t?{}:[],n=this.array_filter(n,(function(e){return""!==e.substring(1)})))t?o[(i=n[s].split(":")).splice(0,1)]=i.join(":").substring(1):o[r++]=n[s];return o},exports.get_meta_tags=function(e){var t="";t=this.file_get_contents(e).match(/^[\s\S]*<\/head>/i);for(var a,n,o=/<meta[^>]*?>/gim,i=/<meta\s+.*?name\s*=\s*(['"]?)(.*?)\1\s+.*?content\s*=\s*(['"]?)(.*?)\3/gim,r=/<meta\s+.*?content\s*=\s*(['"?])(.*?)\1\s+.*?name\s*=\s*(['"]?)(.*?)\3/gim,s={};null!==(a=o.exec(t));){for(;null!==(n=i.exec(a));)s[n[2].replace(/\W/g,"_").toLowerCase()]=n[4];for(;null!==(n=r.exec(a));)s[n[4].replace(/\W/g,"_").toLowerCase()]=n[2]}return s},exports.http_build_query=function(e,t,a){var n,o,i=[],r=this,s=function(e,t,a){var n,o=[];if(!0===t?t="1":!1===t&&(t="0"),null!=t){if("object"==typeof t){for(n in t)null!=t[n]&&o.push(s(e+"["+n+"]",t[n],a));return o.join(a)}if("function"!=typeof t)return r.urlencode(e)+"="+r.urlencode(t);throw new Error("There was an error processing for http_build_query().")}return""};for(o in a||(a="&"),e){n=e[o],t&&!isNaN(o)&&(o=String(t)+o);var u=s(o,n,a);""!==u&&i.push(u)}return i.join(a)},exports.doubleval=function(e){return this.floatval(e)},exports.gettype=function(e){var t,a=typeof e,n=function(e){var t=/\W*function\s+([\w\$]+)\s*\(/.exec(e);return t?t[1]:"(Anonymous)"};return"object"===a?null!==e?"number"!=typeof e.length||e.propertyIsEnumerable("length")||"function"!=typeof e.splice?e.constructor&&n(e.constructor)&&("Date"===(t=n(e.constructor))?a="date":"RegExp"===t?a="regexp":"PHPJS_Resource"===t&&(a="resource")):a="array":a="null":"number"===a&&(a=this.is_float(e)?"double":"integer"),a},exports.is_double=function(e){return this.is_float(e)},exports.is_integer=function(e){return this.is_int(e)},exports.is_long=function(e){return this.is_float(e)},exports.is_real=function(e){return this.is_float(e)},exports.print_r=function(e,t){var a,n=this.window.document;if(repeat_char=function(e,t){for(var a="",n=0;n<e;n++)a+=t;return a},formatArray=function(e,t,a,n){t>0&&t++;var o,i,r=repeat_char(a*t,n),s=repeat_char(a*(t+1),n),u="";if("object"==typeof e&&null!==e&&e.constructor&&"PHPJS_Resource"!==(o=e.constructor,(i=/\W*function\s+([\w\$]+)\s*\(/.exec(o))?i[1]:"(Anonymous)")){for(var c in u+="Array\n"+r+"(\n",e)"[object Array]"===Object.prototype.toString.call(e[c])?u+=s+"["+c+"] => "+formatArray(e[c],t+1,a,n):u+=s+"["+c+"] => "+e[c]+"\n";u+=r+")\n"}else u=null==e?"":e.toString();return u},a=formatArray(e,0,4," "),!0!==t){if(n.body)this.echo(a);else try{n=XULDocument,this.echo('<pre xmlns="http://www.w3.org/1999/xhtml" style="white-space:pre;">'+a+"</pre>")}catch(e){this.echo(a)}return!0}return a},exports.var_dump=function(){var e="",t=" ",a=4,n=0,o=0,i=function(e){var t=/\W*function\s+([\w\$]+)\s*\(/.exec(e);return t?t[1]:"(Anonymous)"},r=function(e,t){for(var a="",n=0;n<e;n++)a+=t;return a},s=function(e,t){var a="";if(null===e)a="NULL";else if("boolean"==typeof e)a="bool("+e+")";else if("string"==typeof e)a="string("+e.length+') "'+e+'"';else if("number"==typeof e)a=parseFloat(e)==parseInt(e,10)?"int("+e+")":"float("+e+")";else if(void 0===e)a="undefined";else if("function"==typeof e){var n=e.toString().split("\n");a="";for(var o=0,i=n.length;o<i;o++)a+=(0!==o?"\n"+t:"")+n[o]}else if(e instanceof Date)a="Date("+e+")";else if(e instanceof RegExp)a="RegExp("+e+")";else if(e.nodeName)switch(e.nodeType){case 1:a=void 0===e.namespaceURI||"http://www.w3.org/1999/xhtml"===e.namespaceURI?'HTMLElement("'+e.nodeName+'")':'XML Element("'+e.nodeName+'")';break;case 2:a="ATTRIBUTE_NODE("+e.nodeName+")";break;case 3:a="TEXT_NODE("+e.nodeValue+")";break;case 4:a="CDATA_SECTION_NODE("+e.nodeValue+")";break;case 5:a="ENTITY_REFERENCE_NODE";break;case 6:a="ENTITY_NODE";break;case 7:a="PROCESSING_INSTRUCTION_NODE("+e.nodeName+":"+e.nodeValue+")";break;case 8:a="COMMENT_NODE("+e.nodeValue+")";break;case 9:a="DOCUMENT_NODE";break;case 10:a="DOCUMENT_TYPE_NODE";break;case 11:a="DOCUMENT_FRAGMENT_NODE";break;case 12:a="NOTATION_NODE"}return a},u=function(e,t,a,o){var c="";t>0&&t++;var l=r(a*(t-1),o),p=r(a*(t+1),o),m="";if("object"==typeof e&&null!==e){if(e.constructor&&"PHPJS_Resource"===i(e.constructor))return e.var_dump();for(c in n=0,e)n++;for(var f in m+="array("+n+") {\n",e){var h=e[f];"object"!=typeof h||null===h||h instanceof Date||h instanceof RegExp||h.nodeName?m+=p+"["+f+"] =>\n"+p+s(h,p)+"\n":m+=p+"["+f+"] =>\n"+p+u(h,t+1,a,o)}m+=l+"}\n"}else m=s(e,p);return m};for(e=u(arguments[0],0,a,t),o=1;o<arguments.length;o++)e+="\n"+u(arguments[o],0,a,t);this.echo(e)},exports.var_export=function(e,t){var a,n="",o=0,i=[],r=0,s=[],u=arguments[2]||2,c="",l="",p=function(e){var t=/\W*function\s+([\w\$]+)\s*\(/.exec(e);return t?t[1]:"(Anonymous)"};if(_makeIndent=function(e){return new Array(e+1).join(" ")},__getType=function(e){var t,a,n,o=0,i=typeof e;if("object"===i&&e&&e.constructor&&"PHPJS_Resource"===p(e.constructor))return"resource";if("function"===i)return"function";if("object"===i&&!e)return"null";if("object"===i){if(!e.constructor)return"object";for((t=(n=e.constructor.toString()).match(/(\w+)\(/))&&(n=t[1].toLowerCase()),a=["boolean","number","string","array"],o=0;o<a.length;o++)if(n===a[o]){i=a[o];break}}return i},type=__getType(e),null===type)n="NULL";else if("array"===type||"object"===type){for(r in l=_makeIndent(u-2),c=_makeIndent(u),e)a="string"==typeof(a=this.var_export(e[r],1,u+2))?a.replace(/</g,"&lt;").replace(/>/g,"&gt;"):a,i[o++]=c+r+" => "+("array"===__getType(e[r])?"\n":"")+a;n=l+"array (\n"+i.join(",\n")+"\n"+l+")"}else n="function"===type?"create_function ('"+(s=e.toString().match(/function .*?\((.*?)\) \{([\s\S]*)\}/))[1]+"', '"+s[2].replace(new RegExp("'","g"),"\\'")+"')":"resource"===type?"NULL":"string"!=typeof e?e:"'"+e.replace(/(["'])/g,"\\$1").replace(/\0/g,"\\0")+"'";return t?n:(this.echo(n),null)},exports.arsort=function(e,t){var a,n,o,i,r,s=[],u=this,c={};switch(t){case"SORT_STRING":i=function(e,t){return u.strnatcmp(t,e)};break;case"SORT_LOCALE_STRING":var l=this.i18n_loc_get_default();i=this.php_js.i18nLocales[l].sorting;break;case"SORT_NUMERIC":i=function(e,t){return e-t};break;case"SORT_REGULAR":default:i=function(e,t){var a=parseFloat(t),n=parseFloat(e),o=a+""===t,i=n+""===e;return o&&i?a>n?1:a<n?-1:0:o&&!i?1:!o&&i?-1:t>e?1:t<e?-1:0}}for(n in this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},c=(r=this.php_js.ini["phpjs.strictForIn"]&&this.php_js.ini["phpjs.strictForIn"].local_value&&"off"!==this.php_js.ini["phpjs.strictForIn"].local_value)?e:c,e)e.hasOwnProperty(n)&&(s.push([n,e[n]]),r&&delete e[n]);for(s.sort((function(e,t){return i(e[1],t[1])})),o=0,a=s.length;o<a;o++)c[s[o][0]]=s[o][1];return r||c},exports.asort=function(e,t){var a,n,o,i,r,s=[],u=this,c={};switch(t){case"SORT_STRING":i=function(e,t){return u.strnatcmp(e,t)};break;case"SORT_LOCALE_STRING":var l=this.i18n_loc_get_default();i=this.php_js.i18nLocales[l].sorting;break;case"SORT_NUMERIC":i=function(e,t){return e-t};break;case"SORT_REGULAR":default:i=function(e,t){var a=parseFloat(e),n=parseFloat(t),o=a+""===e,i=n+""===t;return o&&i?a>n?1:a<n?-1:0:o&&!i?1:!o&&i?-1:e>t?1:e<t?-1:0}}for(n in this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},c=(r=this.php_js.ini["phpjs.strictForIn"]&&this.php_js.ini["phpjs.strictForIn"].local_value&&"off"!==this.php_js.ini["phpjs.strictForIn"].local_value)?e:c,e)e.hasOwnProperty(n)&&(s.push([n,e[n]]),r&&delete e[n]);for(s.sort((function(e,t){return i(e[1],t[1])})),o=0,a=s.length;o<a;o++)c[s[o][0]]=s[o][1];return r||c},exports.krsort=function(e,t){var a,n,o,i,r={},s=[],u=this,c={};switch(t){case"SORT_STRING":a=function(e,t){return u.strnatcmp(t,e)};break;case"SORT_LOCALE_STRING":var l=this.i18n_loc_get_default();a=this.php_js.i18nLocales[l].sorting;break;case"SORT_NUMERIC":a=function(e,t){return t-e};break;case"SORT_REGULAR":default:a=function(e,t){var a=parseFloat(t),n=parseFloat(e),o=a+""===t,i=n+""===e;return o&&i?a>n?1:a<n?-1:0:o&&!i?1:!o&&i?-1:t>e?1:t<e?-1:0}}for(o in e)e.hasOwnProperty(o)&&s.push(o);for(s.sort(a),this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},c=(i=this.php_js.ini["phpjs.strictForIn"]&&this.php_js.ini["phpjs.strictForIn"].local_value&&"off"!==this.php_js.ini["phpjs.strictForIn"].local_value)?e:c,n=0;n<s.length;n++)r[o=s[n]]=e[o],i&&delete e[o];for(n in r)r.hasOwnProperty(n)&&(c[n]=r[n]);return i||c},exports.ksort=function(e,t){var a,n,o,i,r={},s=[],u=this,c={};switch(t){case"SORT_STRING":a=function(e,t){return u.strnatcmp(e,t)};break;case"SORT_LOCALE_STRING":var l=this.i18n_loc_get_default();a=this.php_js.i18nLocales[l].sorting;break;case"SORT_NUMERIC":a=function(e,t){return e+0-(t+0)};break;default:a=function(e,t){var a=parseFloat(e),n=parseFloat(t),o=a+""===e,i=n+""===t;return o&&i?a>n?1:a<n?-1:0:o&&!i?1:!o&&i?-1:e>t?1:e<t?-1:0}}for(o in e)e.hasOwnProperty(o)&&s.push(o);for(s.sort(a),this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},c=(i=this.php_js.ini["phpjs.strictForIn"]&&this.php_js.ini["phpjs.strictForIn"].local_value&&"off"!==this.php_js.ini["phpjs.strictForIn"].local_value)?e:c,n=0;n<s.length;n++)r[o=s[n]]=e[o],i&&delete e[o];for(n in r)r.hasOwnProperty(n)&&(c[n]=r[n]);return i||c},exports.natsort=function(e){var t,a,n,o=[],i=this,r={};for(t in this.php_js=this.php_js||{},this.php_js.ini=this.php_js.ini||{},r=(n=this.php_js.ini["phpjs.strictForIn"]&&this.php_js.ini["phpjs.strictForIn"].local_value&&"off"!==this.php_js.ini["phpjs.strictForIn"].local_value)?e:r,e)e.hasOwnProperty(t)&&(o.push([t,e[t]]),n&&delete e[t]);for(o.sort((function(e,t){return i.strnatcmp(e[1],t[1])})),a=0;a<o.length;a++)r[o[a][0]]=o[a][1];return n||r},exports.rsort=function(e,t){var a,n=[],o="",i=0,r=!1,s=this,u=[];switch(t){case"SORT_STRING":r=function(e,t){return s.strnatcmp(t,e)};break;case"SORT_LOCALE_STRING":var c=this.i18n_loc_get_default();r=this.php_js.i18nLocales[c].sorting;break;case"SORT_NUMERIC":r=function(e,t){return t-e};break;case"SORT_REGULAR":default:r=function(e,t){var a=parseFloat(t),n=parseFloat(e),o=a+""===t,i=n+""===e;return o&&i?a>n?1:a<n?-1:0:o&&!i?1:!o&&i?-1:t>e?1:t<e?-1:0}}try{this.php_js=this.php_js||{}}catch(e){this.php_js={}}for(o in this.php_js.ini=this.php_js.ini||{},u=(a=this.php_js.ini["phpjs.strictForIn"]&&this.php_js.ini["phpjs.strictForIn"].local_value&&"off"!==this.php_js.ini["phpjs.strictForIn"].local_value)?e:u,e)e.hasOwnProperty(o)&&(n.push(e[o]),a&&delete e[o]);for(n.sort(r),i=0;i<n.length;i++)u[i]=n[i];return a||u},exports.sort=function(e,t){var a,n=[],o="",i=0,r=!1,s=this,u=[];switch(t){case"SORT_STRING":r=function(e,t){return s.strnatcmp(e,t)};break;case"SORT_LOCALE_STRING":var c=this.i18n_loc_get_default();r=this.php_js.i18nLocales[c].sorting;break;case"SORT_NUMERIC":r=function(e,t){return e-t};break;case"SORT_REGULAR":default:r=function(e,t){var a=parseFloat(e),n=parseFloat(t),o=a+""===e,i=n+""===t;return o&&i?a>n?1:a<n?-1:0:o&&!i?1:!o&&i?-1:e>t?1:e<t?-1:0}}try{this.php_js=this.php_js||{}}catch(e){this.php_js={}}for(o in this.php_js.ini=this.php_js.ini||{},u=(a=this.php_js.ini["phpjs.strictForIn"]&&this.php_js.ini["phpjs.strictForIn"].local_value&&"off"!==this.php_js.ini["phpjs.strictForIn"].local_value)?e:u,e)e.hasOwnProperty(o)&&(n.push(e[o]),a&&delete e[o]);for(n.sort(r),i=0;i<n.length;i++)u[i]=n[i];return a||u},exports.ctype_alnum=function(e){return"string"==typeof e&&(this.setlocale("LC_ALL",0),-1!==e.search(this.php_js.locales[this.php_js.localeCategories.LC_CTYPE].LC_CTYPE.an))},exports.ctype_alpha=function(e){return"string"==typeof e&&(this.setlocale("LC_ALL",0),-1!==e.search(this.php_js.locales[this.php_js.localeCategories.LC_CTYPE].LC_CTYPE.al))},exports.ctype_cntrl=function(e){return"string"==typeof e&&(this.setlocale("LC_ALL",0),-1!==e.search(this.php_js.locales[this.php_js.localeCategories.LC_CTYPE].LC_CTYPE.ct))},exports.ctype_digit=function(e){return"string"==typeof e&&(this.setlocale("LC_ALL",0),-1!==e.search(this.php_js.locales[this.php_js.localeCategories.LC_CTYPE].LC_CTYPE.dg))},exports.ctype_graph=function(e){return"string"==typeof e&&(this.setlocale("LC_ALL",0),-1!==e.search(this.php_js.locales[this.php_js.localeCategories.LC_CTYPE].LC_CTYPE.gr))},exports.ctype_lower=function(e){return"string"==typeof e&&(this.setlocale("LC_ALL",0),-1!==e.search(this.php_js.locales[this.php_js.localeCategories.LC_CTYPE].LC_CTYPE.lw))},exports.ctype_print=function(e){return"string"==typeof e&&(this.setlocale("LC_ALL",0),-1!==e.search(this.php_js.locales[this.php_js.localeCategories.LC_CTYPE].LC_CTYPE.pr))},exports.ctype_punct=function(e){return"string"==typeof e&&(this.setlocale("LC_ALL",0),-1!==e.search(this.php_js.locales[this.php_js.localeCategories.LC_CTYPE].LC_CTYPE.pu))},exports.ctype_space=function(e){return"string"==typeof e&&(this.setlocale("LC_ALL",0),-1!==e.search(this.php_js.locales[this.php_js.localeCategories.LC_CTYPE].LC_CTYPE.sp))},exports.ctype_upper=function(e){return"string"==typeof e&&(this.setlocale("LC_ALL",0),-1!==e.search(this.php_js.locales[this.php_js.localeCategories.LC_CTYPE].LC_CTYPE.up))},exports.ctype_xdigit=function(e){return"string"==typeof e&&(this.setlocale("LC_ALL",0),-1!==e.search(this.php_js.locales[this.php_js.localeCategories.LC_CTYPE].LC_CTYPE.xd))},exports.strftime=function(e,t){this.php_js=this.php_js||{},this.setlocale("LC_ALL",0);for(var a=this.php_js,n=function(e,t,a){for(void 0===a&&(a=10);parseInt(e,10)<a&&a>1;a/=10)e=t.toString()+e;return e.toString()},o=a.localeCategories.LC_TIME,i=a.locales[o].LC_TIME,r={a:function(e){return i.a[e.getDay()]},A:function(e){return i.A[e.getDay()]},b:function(e){return i.b[e.getMonth()]},B:function(e){return i.B[e.getMonth()]},C:function(e){return n(parseInt(e.getFullYear()/100,10),0)},d:["getDate","0"],e:["getDate"," "],g:function(e){return n(parseInt(this.G(e)/100,10),0)},G:function(e){var t=e.getFullYear(),a=parseInt(r.V(e),10),n=parseInt(r.W(e),10);return n>a?t++:0===n&&a>=52&&t--,t},H:["getHours","0"],I:function(e){var t=e.getHours()%12;return n(0===t?12:t,0)},j:function(e){var t=e-new Date(e.getFullYear()+"/1/1 GMT");t+=6e4*e.getTimezoneOffset();var a=parseInt(t/6e4/60/24,10)+1;return n(a,0,100)},k:["getHours","0"],l:function(e){var t=e.getHours()%12;return n(0===t?12:t," ")},m:function(e){return n(e.getMonth()+1,0)},M:["getMinutes","0"],p:function(e){return i.p[e.getHours()>=12?1:0]},P:function(e){return i.P[e.getHours()>=12?1:0]},s:function(e){return Date.parse(e)/1e3},S:["getSeconds","0"],u:function(e){var t=e.getDay();return 0===t?7:t},U:function(e){var t=parseInt(r.j(e),10),a=6-e.getDay(),o=parseInt((t+a)/7,10);return n(o,0)},V:function(e){var t=parseInt(r.W(e),10),a=new Date(e.getFullYear()+"/1/1").getDay(),o=t+(a>4||a<=1?0:1);return 53===o&&new Date(e.getFullYear()+"/12/31").getDay()<4?o=1:0===o&&(o=r.V(new Date(e.getFullYear()-1+"/12/31"))),n(o,0)},w:"getDay",W:function(e){var t=parseInt(r.j(e),10),a=7-r.u(e),o=parseInt((t+a)/7,10);return n(o,0,10)},y:function(e){return n(e.getFullYear()%100,0)},Y:"getFullYear",z:function(e){var t=e.getTimezoneOffset();return(t>0?"-":"+")+n(parseInt(Math.abs(t/60),10),0)+n(t%60,0)},Z:function(e){return e.toString().replace(/^.*\(([^)]+)\)$/,"$1")},"%":function(e){return"%"}},s=void 0===t?new Date:"object"==typeof t?new Date(t):new Date(1e3*t),u={c:"locale",D:"%m/%d/%y",F:"%y-%m-%d",h:"%b",n:"\n",r:"locale",R:"%H:%M",t:"\t",T:"%H:%M:%S",x:"locale",X:"locale"};e.match(/%[cDFhnrRtTxX]/);)e=e.replace(/%([cDFhnrRtTxX])/g,(function(e,t){var a=u[t];return"locale"===a?i[t]:a}));return e.replace(/%([aAbBCdegGHIjklmMpPsSuUVwWyYzZ%])/g,(function(e,t){var a=r[t];return"string"==typeof a?s[a]():"function"==typeof a?a(s):"object"==typeof a&&"string"==typeof a[0]?n(s[a[0]](),a[1]):t}))},exports.strptime=function(e,t){var a={tm_sec:0,tm_min:0,tm_hour:0,tm_mday:0,tm_mon:0,tm_year:0,tm_wday:0,tm_yday:0,unparsed:""},n=0,o=this,i=0,r=!1,s=function(e,t){var n,o=a,i=e;o.tm_sec=i.getUTCSeconds(),o.tm_min=i.getUTCMinutes(),o.tm_hour=i.getUTCHours(),o.tm_mday=0===t?t:i.getUTCDate(),o.tm_mon=i.getUTCMonth(),o.tm_year=i.getUTCFullYear()-1900,o.tm_wday=0===t?i.getUTCDay()>0?i.getUTCDay()-1:6:i.getUTCDay(),n=new Date(Date.UTC(i.getUTCFullYear(),0,1)),o.tm_yday=Math.ceil((i-n)/864e5)},u=function(){var e=a;return s(new Date(Date.UTC(e.tm_year+1900,e.tm_mon,e.tm_mday||1,e.tm_hour,e.tm_min,e.tm_sec)),e.tm_mday)},c=/\S/,l=/\s/,p={c:"locale",D:"%m/%d/%y",F:"%y-%m-%d",r:"locale",R:"%H:%M",T:"%H:%M:%S",x:"locale",X:"locale"},m=function(e){return(e+"").replace(/([\\\.\+\*\?\[\^\]\$\(\)\{\}\=\!<>\|\:])/g,"\\$1")};this.php_js=this.php_js||{},this.setlocale("LC_ALL",0);for(var f=this.php_js,h=f.localeCategories.LC_TIME,d=f.locales[h].LC_TIME;t.match(/%[cDFhnrRtTxX]/);)t=t.replace(/%([cDFhnrRtTxX])/g,(function(e,t){var a=p[t];return"locale"===a?d[t]:a}));var g=function(t,a,n){"string"==typeof a&&(a=new RegExp("^"+a,"i"));var o=e.slice(t),i=a.exec(o);if(null===(i?n.apply(null,i):null))throw"No match in string";return t+i[0].length},b=function(e,t,n){return g(e,o.array_map(m,d[t]).join("|"),(function(e){var o=d[t].search(new RegExp("^"+m(e)+"$","i"));o&&(a[n]=o[0])}))};for(n=0,j=0;n<t.length;n++)if("%"===t.charAt(n)){var k=["%","n","t"].indexOf(t.charAt(n+1));if(-1!==k){if(["%","\n","\t"].indexOf(e.charAt(j))===k){++n,++j;continue}return!1}var v=t.charAt(n+1);try{switch(v){case"a":case"A":j=b(j,v,"tm_wday");break;case"h":case"b":j=b(j,"b","tm_mon"),u();break;case"B":j=b(j,v,"tm_mon"),u();break;case"C":j=g(j,/^\d?\d/,(function(e){var t=100*(parseInt(e,10)-19);a.tm_year=t,u(),a.tm_yday||(a.tm_yday=-1)}));break;case"d":case"e":j=g(j,"d"===v?/^(0[1-9]|[1-2]\d|3[0-1])/:/^([1-2]\d|3[0-1]|[1-9])/,(function(e){var t=parseInt(e,10);a.tm_mday=t,u()}));break;case"g":case"G":break;case"H":j=g(j,/^([0-1]\d|2[0-3])/,(function(e){var t=parseInt(e,10);a.tm_hour=t}));break;case"l":case"I":j=g(j,"l"===v?/^([1-9]|1[0-2])/:/^(0[1-9]|1[0-2])/,(function(e){var t=parseInt(e,10)-1+i;a.tm_hour=t,r=!0}));break;case"j":j=g(j,/^(00[1-9]|0[1-9]\d|[1-2]\d\d|3[0-6][0-6])/,(function(e){var t=parseInt(e,10)-1;a.tm_yday=t}));break;case"m":j=g(j,/^(0[1-9]|1[0-2])/,(function(e){var t=parseInt(e,10)-1;a.tm_mon=t,u()}));break;case"M":j=g(j,/^[0-5]\d/,(function(e){var t=parseInt(e,10);a.tm_min=t}));break;case"P":return!1;case"p":j=g(j,/^(am|pm)/i,(function(e){i=/a/.test(e)?0:12,r&&(a.tm_hour+=i)}));break;case"s":j=g(j,/^\d+/,(function(e){var t=parseInt(e,10),a=new Date(Date.UTC(1e3*t));s(a)}));break;case"S":j=g(j,/^[0-5]\d/,(function(e){var t=parseInt(e,10);a.tm_sec=t}));break;case"u":case"w":j=g(j,/^\d/,(function(e){a.tm_wday=e-("u"===v)}));break;case"U":case"V":case"W":break;case"y":j=g(j,/^\d?\d/,(function(e){var t=(e=parseInt(e,10))>=69?e:e+100;a.tm_year=t,u(),a.tm_yday||(a.tm_yday=-1)}));break;case"Y":j=g(j,/^\d{1,4}/,(function(e){var t=parseInt(e,10)-1900;a.tm_year=t,u(),a.tm_yday||(a.tm_yday=-1)}));break;case"z":case"Z":break;default:throw"Unrecognized formatting character in strptime()"}}catch(e){if("No match in string"===e)return!1}++n}else if(t.charAt(n)!==e.charAt(j)){if(-1!==e.charAt(j).search(l))j++,n--;else if(-1!==t.charAt(n).search(c))return!1}else j++;return a.unparsed=e.slice(j),a},exports.sql_regcase=function(e){this.setlocale("LC_ALL",0);var t=0,a="",n="",o=0,i="";for(a=this.php_js.locales[this.php_js.localeCategories.LC_CTYPE].LC_CTYPE.upper,n=this.php_js.locales[this.php_js.localeCategories.LC_CTYPE].LC_CTYPE.lower,t=0;t<e.length;t++)-1!==(o=a.indexOf(e.charAt(t)))||-1!==(o=n.indexOf(e.charAt(t)))?i+="["+a.charAt(o)+n.charAt(o)+"]":i+=e.charAt(t);return i},exports.localeconv=function(){var e={},t="";for(t in this.setlocale("LC_ALL",0),this.php_js.locales[this.php_js.localeCategories.LC_NUMERIC].LC_NUMERIC)e[t]=this.php_js.locales[this.php_js.localeCategories.LC_NUMERIC].LC_NUMERIC[t];for(t in this.php_js.locales[this.php_js.localeCategories.LC_MONETARY].LC_MONETARY)e[t]=this.php_js.locales[this.php_js.localeCategories.LC_MONETARY].LC_MONETARY[t];return e},exports.money_format=function(e,t){if("number"!=typeof t)return null;this.setlocale("LC_ALL",0);var a=this.php_js.locales[this.php_js.localeCategories.LC_MONETARY].LC_MONETARY;return e.replace(/%((=.|[+^(!-])*?)(\d*?)(#(\d+))?(\.(\d+))?([in%])/g,(function(e,n,o,i,r,s,u,c,l){var p="",m="";if("%"===l)return"%";var f=n&&/=./.test(n)?n.match(/=(.)/)[1]:" ",h=!n||-1===n.indexOf("!");i=parseInt(i,10)||0;var d=t<0;t+="";var g=(t=d?t.slice(1):t).indexOf("."),b=-1!==g?t.slice(0,g):t,k=-1!==g?t.slice(g+1):"",v=function(e,t,a){var n=e.split("");return n.splice(t,0,a),n.join("")},y=b.length,j=y<(s=parseInt(s,10));if(j){var w=s-y;b=new Array(w+1).join(f)+b}if(-1===n.indexOf("^")){var _=a.mon_thousands_sep,A=a.mon_grouping;if(A[0]<b.length)for(var x=0,z=b.length;x<A.length&&!((z-=A[x])<=0);x++)j&&z<w&&(_=f),b=v(b,z,_);if(A[x-1]>0)for(;z>A[x-1];)z-=A[x-1],j&&z<w&&(_=f),b=v(b,z,_)}if("0"===c)p=b;else{var S=a.mon_decimal_point;""!==c&&void 0!==c||(c="i"===l?a.int_frac_digits:a.frac_digits),0===(c=parseInt(c,10))?(k="",S=""):c<k.length?c>(k=Math.round(parseFloat(k.slice(0,c)+"."+k.substr(c,1)))+"").length&&(k=new Array(c-k.length+1).join("0")+k):c>k.length&&(k+=new Array(c-k.length+1).join("0")),p=b+S+k}var C="";h&&(C="i"===l?a.int_curr_symbol:a.currency_symbol);var E=d?a.n_sign_posn:a.p_sign_posn,T=d?a.n_sep_by_space:a.p_sep_by_space,I=d?a.n_cs_precedes:a.p_cs_precedes;if(-1!==n.indexOf("("))m=(I?C+(1===T?" ":""):"")+p+(I?"":(1===T?" ":"")+C),m=d?"("+m+")":" "+m+" ";else{var P=a.positive_sign,M=a.negative_sign,O=d?M:P,F="";E&&(F=new Array((d?P:M).length-O.length+1).join(" "));switch(E){case 0:m="("+(I?C+(1===T?" ":"")+p:p+(1===T?" ":"")+C)+")";break;case 1:m=F+O+(2===T?" ":"")+(I?C+(1===T?" ":"")+p:p+(1===T?" ":"")+C);break;case 2:m=(I?C+(1===T?" ":"")+p:p+(1===T?" ":"")+C)+(2===T?" ":"")+O+F;break;case 3:m=I?F+O+(2===T?" ":"")+C+(1===T?" ":"")+p:p+(1===T?" ":"")+O+F+(2===T?" ":"")+C;break;case 4:m=I?C+(2===T?" ":"")+F+O+(1===T?" ":"")+p:p+(1===T?" ":"")+C+(2===T?" ":"")+O+F}}var D=i-m.length;return D>0&&(D=new Array(D+1).join(" "),-1!==n.indexOf("-")?m+=D:m=D+m),m}))},exports.nl_langinfo=function(e){this.setlocale("LC_ALL",0);var t=this.php_js.locales[this.php_js.localeCategories.LC_TIME];if(0===e.indexOf("ABDAY_"))return t.LC_TIME.a[parseInt(e.replace(/^ABDAY_/,""),10)-1];if(0===e.indexOf("DAY_"))return t.LC_TIME.A[parseInt(e.replace(/^DAY_/,""),10)-1];if(0===e.indexOf("ABMON_"))return t.LC_TIME.b[parseInt(e.replace(/^ABMON_/,""),10)-1];if(0===e.indexOf("MON_"))return t.LC_TIME.B[parseInt(e.replace(/^MON_/,""),10)-1];switch(e){case"AM_STR":return t.LC_TIME.p[0];case"PM_STR":return t.LC_TIME.p[1];case"D_T_FMT":return t.LC_TIME.c;case"D_FMT":return t.LC_TIME.x;case"T_FMT":return t.LC_TIME.X;case"T_FMT_AMPM":return t.LC_TIME.r;case"ERA":case"ERA_YEAR":case"ERA_D_T_FMT":case"ERA_D_FMT":case"ERA_T_FMT":return t.LC_TIME[e]}switch(t=this.php_js.locales[this.php_js.localeCategories.LC_MONETARY],"CRNCYSTR"===e&&(e="CURRENCY_SYMBOL"),e){case"INT_CURR_SYMBOL":case"CURRENCY_SYMBOL":case"MON_DECIMAL_POINT":case"MON_THOUSANDS_SEP":case"POSITIVE_SIGN":case"NEGATIVE_SIGN":case"INT_FRAC_DIGITS":case"FRAC_DIGITS":case"P_CS_PRECEDES":case"P_SEP_BY_SPACE":case"N_CS_PRECEDES":case"N_SEP_BY_SPACE":case"P_SIGN_POSN":case"N_SIGN_POSN":case"MON_GROUPING":return t.LC_MONETARY[e.toLowerCase()]}switch(t=this.php_js.locales[this.php_js.localeCategories.LC_NUMERIC],e){case"RADIXCHAR":case"DECIMAL_POINT":return t.LC_NUMERIC[e.toLowerCase()];case"THOUSEP":case"THOUSANDS_SEP":case"GROUPING":return t.LC_NUMERIC[e.toLowerCase()]}switch(t=this.php_js.locales[this.php_js.localeCategories.LC_MESSAGES],e){case"YESEXPR":case"NOEXPR":case"YESSTR":case"NOSTR":return t.LC_MESSAGES[e]}return t=this.php_js.locales[this.php_js.localeCategories.LC_CTYPE],"CODESET"===e&&t.LC_CTYPE[e]},exports.strcoll=function(e,t){return this.setlocale("LC_ALL",0),(0,this.php_js.locales[this.php_js.localeCategories.LC_COLLATE].LC_COLLATE)(e,t)},exports.strval=function(e){if(null===e)return"";switch(this.gettype(e)){case"boolean":return!0===e?"1":"";case"array":return"Array";case"object":return"Object"}return e},exports.gmstrftime=function(e,t){var a=void 0===t?new Date:"object"==typeof t?new Date(t):new Date(1e3*t);return t=Date.parse(a.toUTCString().slice(0,-4))/1e3,this.strftime(e,t)},exports.str_word_count=function(e,t,a){var n=e.length,o=a&&a.length,i="",r="",s=0,u="",c=[],l=0,p={},m=0,f="",h=!1,d=function(e){return(e+"").replace(/([\\\.\+\*\?\[\^\]\$\(\)\{\}\=\!<>\|\:])/g,"\\$1")};if(_getWholeChar=function(e,t){var a=e.charCodeAt(t);if(a<55296||a>57343)return e.charAt(t);if(55296<=a&&a<=56319){if(e.length<=t+1)throw"High surrogate without following low surrogate";var n=e.charCodeAt(t+1);if(56320>n||n>57343)throw"High surrogate without following low surrogate";return e.charAt(t)+e.charAt(t+1)}if(0===t)throw"Low surrogate without preceding high surrogate";var o=e.charCodeAt(t-1);if(55296>o||o>56319)throw"Low surrogate without preceding high surrogate";return!1},o){for(f="^("+d(_getWholeChar(a,0)),s=1;s<o;s++)!1!==(i=_getWholeChar(a,s))&&(f+="|"+d(i));f+=")$",f=new RegExp(f)}for(s=0;s<n;s++)!1!==(u=_getWholeChar(e,s))&&((h=this.ctype_alpha(u)||f&&-1!==u.search(f)||0!==s&&s!==n-1&&"-"===u||0!==s&&"'"===u)&&(""===r&&2===t&&(m=s),r+=u),(s===n-1||!h&&""!==r)&&(2!==t?c[c.length]=r:p[m]=r,r="",l++));if(!t)return l;if(1===t)return c;if(2===t)return p;throw"You have supplied an incorrect format"},exports.strtr=function(e,t,a){var n,o,i,r,s="",u=0,c=0,l=!1,p="",m=[],f=[],h="",d=!1;if("object"==typeof t){for(s in l=this.ini_set("phpjs.strictForIn",!1),t=this.krsort(t),this.ini_set("phpjs.strictForIn",l),t)t.hasOwnProperty(s)&&(m.push(s),f.push(t[s]));t=m,a=f}for(n=e.length,o=t.length,i="string"==typeof t,r="string"==typeof a,u=0;u<n;u++){if(d=!1,i){for(p=e.charAt(u),c=0;c<o;c++)if(p==t.charAt(c)){d=!0;break}}else for(c=0;c<o;c++)if(e.substr(u,t[c].length)==t[c]){d=!0,u=u+t[c].length-1;break}h+=d?r?a.charAt(c):a[c]:e.charAt(u)}return h}},17:function(e,t,a){"use strict";var n,o={calculate:function(e,t){var a,n,o,i=[];try{a=this.toDecimal(e),n=this.toDecimal(t)}catch(e){return null}if(n<a)return null;for(o=a;o<=n;){var r=this.getOptimalRange(o,n);if(null===r)return null;i.push(r),o=r.ipHigh+1}return i},calculateSubnetMask:function(e,t){var a;try{a=this.toDecimal(e)}catch(e){return null}return this.getMaskRange(a,t)},calculateCIDRPrefix:function(e,t){var a,n,o,i=0,r=0;try{a=this.toDecimal(e),n=this.toDecimal(t)}catch(e){return null}for(o=0;o<32&&(n&(r=i+(1<<32-(o+1))>>>0))>>>0===r;o++)i=r;return this.getMaskRange(a,o)},getOptimalRange:function(e,t){var a,n=null;for(a=32;a>=0;a--){var o=this.getMaskRange(e,a);if(!(o.ipLow===e&&o.ipHigh<=t))break;n=o}return n},getMaskRange:function(e,t){var a=this.getPrefixMask(t),n=this.getMask(32-t),o=(e&a)>>>0,i=((e&a)>>>0)+n>>>0;return{ipLow:o,ipLowStr:this.toString(o),ipHigh:i,ipHighStr:this.toString(i),prefixMask:a,prefixMaskStr:this.toString(a),prefixSize:t,invertedMask:n,invertedMaskStr:this.toString(n),invertedSize:32-t}},getPrefixMask:function(e){var t,a=0;for(t=0;t<e;t++)a+=1<<32-(t+1)>>>0;return a},getMask:function(e){var t,a=0;for(t=0;t<e;t++)a+=1<<t>>>0;return a},isIp:function(e){if("string"!=typeof e)return!1;var t=e.match(/^([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})$/);if(null===t)return!1;for(var a=1;a<=4;a++){var n=parseInt(t[a],10);if(n>255||n<0)return!1}return!0},isDecimalIp:function(e){return"number"==typeof e&&e%1==0&&e>=0&&e<=4294967295},toDecimal:function(e){if("number"==typeof e&&!0===this.isDecimalIp(e))return e;if(!1===this.isIp(e))throw new Error("Not an IP address: "+e);var t=e.split(".");return 256*(256*(256*+t[0]+ +t[1])+ +t[2])+ +t[3]},toString:function(e){if("string"==typeof e&&!0===this.isIp(e))return e;if(!1===this.isDecimalIp(e))throw new Error("Not a numeric IP address: "+e);for(var t=e%256,a=3;a>0;a--)t=(e=Math.floor(e/256))%256+"."+t;return t}};void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)},19:function(e,t,a){"use strict";a.r(t),a.d(t,"getDevice",(function(){return o}));let n={mobile:{and1:[{name:"Google Pixel 5",build:"Pixel 5",viewport:"393x851",deviceScaleFactor:2.75,memory:8,hw:8},{name:"Moto G9 Play",build:"moto g(9) play",viewport:"412x915",deviceScaleFactor:1.75,memory:4,hw:8},{name:"OnePlus 8",build:"IN2013",viewport:"412x915",deviceScaleFactor:2.625,memory:8,hw:8},{name:"Samsung Galaxy A52",build:"SM-A525F",viewport:"412x915",deviceScaleFactor:2.625,memory:4,hw:8},{name:"Samsung Galaxy S21",build:"SM-G991B",viewport:"360x800",deviceScaleFactor:3,memory:8,hw:8}],and2:[{name:"Google Pixel 6",build:"Pixel 6",viewport:"412x915",deviceScaleFactor:2.625,memory:8,hw:8},{name:"Redmi Note 12 Pro",build:"22101316I",viewport:"393x873",deviceScaleFactor:2.75,memory:8,hw:8},{name:"Moto G71 5G",build:"moto g71 5G",viewport:"412x915",deviceScaleFactor:2.625,memory:4,hw:8},{name:"OnePlus 9",build:"LE2113",viewport:"384x854",deviceScaleFactor:2.8125,memory:8,hw:8},{name:"Samsung Galaxy S22",build:"SM-S901U",viewport:"360x780",deviceScaleFactor:3,memory:8,hw:8}],and3:[{name:"Google Pixel 7",build:"Pixel 7",viewport:"412x915",deviceScaleFactor:2.625,memory:8,hw:8},{name:"Google Pixel 7 Pro",build:"Pixel 7 Pro",viewport:"412x892",deviceScaleFactor:2.625,memory:8,hw:8},{name:"Redmi Note 12 4G",build:"23027RAD4I",viewport:"393x783",deviceScaleFactor:2.75,memory:4,hw:8},{name:"Samsung Galaxy S23",build:"SM-S911B",viewport:"360x780",deviceScaleFactor:3,memory:8,hw:8},{name:"OnePlus 11",build:"CPH2487",viewport:"355x793",deviceScaleFactor:3.5,memory:8,hw:8}],and4:[{name:"Google Pixel 8",build:"Pixel 8",viewport:"412x915",deviceScaleFactor:2.625,memory:8,hw:9},{name:"Google Pixel 8 Pro",build:"Pixel 8 Pro",viewport:"448x998",deviceScaleFactor:2.25,memory:8,hw:9},{name:"OnePlus 11",build:"CPH2487",viewport:"355x793",deviceScaleFactor:3.5,memory:8,hw:8},{name:"Samsung Galaxy S23",build:"SM-S911B",viewport:"360x780",deviceScaleFactor:3,memory:8,hw:8},{name:"Samsung Galaxy S23 Ultra",build:"SM-S918B",viewport:"384x824",deviceScaleFactor:2.8125,memory:8,hw:8}],ios1:[{name:"iPhone 6S",build:"19H370",viewport:"375x667",deviceScaleFactor:2},{name:"iPhone 6S Plus",build:"19H370",viewport:"414x736",deviceScaleFactor:3},{name:"iPhone SE (2022)",build:"19H370",viewport:"375x548",deviceScaleFactor:2},{name:"iPhone 7",build:"19H370",viewport:"375x667",deviceScaleFactor:2},{name:"iPhone 7 Plus",build:"19H370",viewport:"414x736",deviceScaleFactor:3},{name:"iPhone 8",build:"19H370",viewport:"375x667",deviceScaleFactor:2},{name:"iPhone 8 Plus",build:"19H370",viewport:"414x736",deviceScaleFactor:3},{name:"iPhone X",build:"19H370",viewport:"375x812",deviceScaleFactor:3},{name:"iPhone XS",build:"19H370",viewport:"375x812",deviceScaleFactor:3},{name:"iPhone XS Max",build:"19H370",viewport:"414x896",deviceScaleFactor:3},{name:"iPhone XR",build:"19H370",viewport:"414x896",deviceScaleFactor:3},{name:"iPhone 11",build:"19H370",viewport:"414x896",deviceScaleFactor:3},{name:"iPhone 11 Pro",build:"19H370",viewport:"375x812",deviceScaleFactor:3},{name:"iPhone 11 Pro Max",build:"19H370",viewport:"414x896",deviceScaleFactor:3},{name:"iPhone 12",build:"19H370",viewport:"360x780",deviceScaleFactor:3},{name:"iPhone 12 Pro",build:"19H370",viewport:"390x844",deviceScaleFactor:3},{name:"iPhone 12 Pro Max",build:"19H370",viewport:"428x926",deviceScaleFactor:3},{name:"iPhone 13",build:"19H370",viewport:"390x844",deviceScaleFactor:3},{name:"iPhone 13 Pro",build:"19H370",viewport:"390x844",deviceScaleFactor:3},{name:"iPhone 13 Pro Max",build:"19H370",viewport:"428x926",deviceScaleFactor:3}]},tablet:{and1:[{name:"Samsung Galaxy Tab S4 10.5",build:"SM-T830",viewport:"800x1280",deviceScaleFactor:2,memory:4,hw:8},{name:"Samsung Galaxy Tab A 10.5",build:"SM-T590",viewport:"600x960",deviceScaleFactor:2,memory:2,hw:8},{name:"Xiaomi Mi Pad 4",build:"MI PAD 4",viewport:"600x960",deviceScaleFactor:2,memory:2,hw:8},{name:"Xiaomi Mi Pad 4 Plus",build:"MI PAD 4 PLUS",viewport:"600x960",deviceScaleFactor:2,memory:4,hw:8}],and2:[{name:"Samsung Galaxy Tab S4 10.5",build:"SM-T830",viewport:"800x1280",deviceScaleFactor:2,memory:4,hw:8},{name:"ASUS ZenPad 3S 10",build:"P027",viewport:"748x1024",deviceScaleFactor:2,memory:4,hw:8},{name:"Samsung Galaxy Tab S5e",build:"SM-T720",viewport:"800x1280",deviceScaleFactor:2,memory:4,hw:8},{name:"Samsung Galaxy Tab A 10.1 (2019)",build:"SM-T510",viewport:"600x960",deviceScaleFactor:1.5,memory:2,hw:8}],and3:[{name:"Samsung Galaxy Tab S4 10.5",build:"SM-T830",viewport:"800x1280",deviceScaleFactor:2,memory:4,hw:8},{name:"Samsung Galaxy Tab S5e",build:"SM-T720",viewport:"800x1280",deviceScaleFactor:2,memory:4,hw:8},{name:"Samsung Galaxy Tab A 10.1 (2019)",build:"SM-T510",viewport:"600x960",deviceScaleFactor:1.5,memory:2,hw:8},{name:"Samsung Galaxy Tab A7 10.4 (2020)",build:"SM-T500",viewport:"600x1000",deviceScaleFactor:2,memory:2,hw:8}],and4:[{name:"Samsung Galaxy Tab S7+",build:"SM-T970",viewport:"876x1400",deviceScaleFactor:2,memory:4,hw:8},{name:"Samsung Galaxy Tab S5e",build:"SM-T720",viewport:"800x1280",deviceScaleFactor:2,memory:4,hw:8},{name:"Samsung Galaxy Tab A7 10.4 (2020)",build:"SM-T500",viewport:"600x1000",deviceScaleFactor:2,memory:2,hw:8}],ios1:[{device:"iPad Air 2",build:"19H370",viewport:"768x1024",deviceScaleFactor:2},{device:"iPad (2017)",build:"19H370",viewport:"768x1024",deviceScaleFactor:2},{device:"iPad (2018)",build:"19H370",viewport:"768x1024",deviceScaleFactor:2},{device:"iPad Mini 4",build:"19H370",viewport:"768x1024",deviceScaleFactor:2},{device:"iPad Pro (9.7in)",build:"19H370",viewport:"768x1024",deviceScaleFactor:2},{device:"iPad Pro (10.5in)",build:"19H370",viewport:"834x1112",deviceScaleFactor:2},{device:"iPad Pro (12.9in, 1st gen)",build:"19H370",viewport:"1024x1366",deviceScaleFactor:2}]}};n.mobile.ios2=n.mobile.ios1.map(e=>({build:"20H115",name:e.name,viewport:e.viewport})).concat([{name:"iPhone 14",build:"20H115",viewport:"390x844",deviceScaleFactor:3},{name:"iPhone 14 Pro",build:"20H115",viewport:"393x852",deviceScaleFactor:3},{name:"iPhone 14 Pro Max",build:"20H115",viewport:"430x932",deviceScaleFactor:3}]),n.mobile.ios3=n.mobile.ios2.map(e=>({build:"21B74",name:e.name,viewport:e.viewport})).concat([{name:"iPhone 15",build:"21B80",viewport:"393x852",deviceScaleFactor:3},{name:"iPhone 15 Pro",build:"21B80",viewport:"393x852",deviceScaleFactor:3},{name:"iPhone 15 Pro Max",build:"21B74",viewport:"430x932",deviceScaleFactor:3}]),n.tablet.ios2=n.tablet.ios1.map(e=>({build:"20H115",name:e.name,viewport:e.viewport,deviceScaleFactor:e.deviceScaleFactor})).concat([{name:"iPad Air (2019)",build:"20H115",viewport:"834x1112",deviceScaleFactor:2},{name:"iPad Mini (5th gen)",build:"20H115",viewport:"768x1024",deviceScaleFactor:2},{name:"iPad Pro (12.9-inch 2nd gen)",build:"20H115",viewport:"1024x1366",deviceScaleFactor:2},{name:"iPad Pro (12.9-inch 3rd gen)",build:"20H115",viewport:"1024x1366",deviceScaleFactor:2}]),n.tablet.ios3=n.tablet.ios2.map(e=>({build:"21B74",name:e.name,viewport:e.viewport,deviceScaleFactor:e.deviceScaleFactor}));let o=(e,t)=>n[e][t][Math.floor(Math.random()*n[e][t].length)]},194:function(e,t,a){"use strict";var n,o=new Uint8Array(16);function i(){if(!n&&!(n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return n(o)}var r=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;for(var s=function(e){return"string"==typeof e&&r.test(e)},u=[],c=0;c<256;++c)u.push((c+256).toString(16).substr(1));var l=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=(u[e[t+0]]+u[e[t+1]]+u[e[t+2]]+u[e[t+3]]+"-"+u[e[t+4]]+u[e[t+5]]+"-"+u[e[t+6]]+u[e[t+7]]+"-"+u[e[t+8]]+u[e[t+9]]+"-"+u[e[t+10]]+u[e[t+11]]+u[e[t+12]]+u[e[t+13]]+u[e[t+14]]+u[e[t+15]]).toLowerCase();if(!s(a))throw TypeError("Stringified UUID is invalid");return a};t.a=function(e,t,a){var n=(e=e||{}).random||(e.rng||i)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){a=a||0;for(var o=0;o<16;++o)t[a+o]=n[o];return t}return l(n)}},195:function(e,t,a){"use strict";a.r(t);var n,o,i=a(4),r=a(5),s=a(6),u=a(2),c=a(3),l=a(9);!function(e){e[e.AlwaysSend=0]="AlwaysSend",e[e.MatchBaseDomain=1]="MatchBaseDomain",e[e.MatchHost=2]="MatchHost"}(n||(n={})),function(e){e[e.SendFullURI=0]="SendFullURI",e[e.SchemeWithPath=1]="SchemeWithPath",e[e.SchemeNoPath=2]="SchemeNoPath"}(o||(o={}));class p{constructor(e,t,a,n){this.regex={CLOUDFLARE:RegExp(/chk_jschl/),HTTPS:RegExp(/^https:\/\//)},this.LINK=document.createElement("a"),this.profiles=new r.a,this.settings=e,this.tempStore=t,this.profileCache=a,this.olderThanNinety=n}blockWebsocket(e){if(!this.settings.config.enabled)return;let t=this.checkWhitelist(e);if(!t.active&&"allow_all"===this.settings.options.webSockets)return;let a=!1;if(e.requestHeaders)for(let t of e.requestHeaders)"x-websocket-extensions"==t.name.toLowerCase()&&(a=!0);if("websocket"===e.type||e.url.includes("transport=polling")||a){if(t.active)return{cancel:t.opt.ws};if("block_all"===this.settings.options.webSockets)return{cancel:!0};if("block_3rd_party"===this.settings.options.webSockets){let t=u.a.parseURL(e.documentUrl||e.originUrl),a=u.a.parseURL(e.url);if(!t.error&&!a.error&&t.domain!=a.domain)return{cancel:!0}}}}checkWhitelist(e){let t;if("main_frame"===e.type)t=e.url;else if(-1==e.parentFrameId)t=e.documentUrl;else{let a=e.frameAncestors?e.frameAncestors.find(e=>0===e.frameId):"";t=a?a.url:e.documentUrl}if(t){this.LINK.href=t;let e=u.a.findWhitelistRule(this.settings.whitelist.rules,this.LINK.host,t);if(e)return{active:!0,lang:e.lang,opt:e.options,pattern:e.pattern,profile:e.profile,spoofIP:e.spoofIP}}return{active:!1}}modifyRequest(e){if(!this.settings.config.enabled)return;for(let t=0;t<l.a.length;t++)if(e.originUrl&&e.originUrl.startsWith(l.a[t])||e.documentUrl&&e.documentUrl.startsWith(l.a[t])||e.url&&e.url.startsWith(l.a[t]))return;if(this.LINK.href=e.documentUrl||e.url,u.a.isInternalIP(this.LINK.hostname))return;let t,a=this.checkWhitelist(e),r=this.regex.HTTPS.test(e.url),s=-1;if(a.active)"default"===a.profile?"none"!=this.settings.whitelist.defaultProfile&&(t=this.profileCache[this.settings.whitelist.defaultProfile]):"none"!=a.profile&&(t=this.profileCache[a.profile]);else if("none"!=this.settings.profile.selected&&!this.settings.excluded.includes(this.settings.profile.selected)&&"none"!=this.tempStore.profile){let e=this.settings.profile.selected.includes("-")?this.settings.profile.selected:this.tempStore.profile;t=this.profileCache[e]}let c=!!t&&t.navigator.userAgent.includes("Chrome");for(let c=0;c<e.requestHeaders.length;c++){let l=e.requestHeaders[c].name.toLowerCase();if("referer"===l){if(a.active)a.opt.ref&&(e.requestHeaders[c].value="");else if(this.settings.headers.referer.disabled)e.requestHeaders[c].value="";else if(!this.regex.CLOUDFLARE.test(e.url)){if(this.settings.headers.referer.xorigin!=n.AlwaysSend){let t=u.a.parseURL(e.url),a=u.a.parseURL(e.requestHeaders[c].value);this.settings.headers.referer.xorigin===n.MatchBaseDomain?t.domain!=a.domain&&(e.requestHeaders[c].value=""):t.origin!=a.origin&&(e.requestHeaders[c].value="")}if(this.settings.headers.referer.trimming!=o.SendFullURI&&""!=e.requestHeaders[c].value){let t=u.a.parseURL(e.requestHeaders[c].value);e.requestHeaders[c].value=this.settings.headers.referer.trimming===o.SchemeWithPath?t.origin+t.pathname:t.origin}}}else"user-agent"===l?t&&(e.requestHeaders[c].value=t.navigator.userAgent):"accept"===l?"main_frame"!==e.type&&"sub_frame"!==e.type||t&&(e.requestHeaders[c].value=t.accept.header):"accept-encoding"===l?"main_frame"!==e.type&&"sub_frame"!==e.type||t&&(e.requestHeaders[c].value=r?t.accept.encodingHTTPS:t.accept.encodingHTTP):"accept-language"===l?a.active&&""!=a.lang?e.requestHeaders[c].value=a.lang:this.settings.headers.spoofAcceptLang.enabled&&("ip"===this.settings.headers.spoofAcceptLang.value?this.tempStore.ipInfo.lang&&(e.requestHeaders[c].value=i.getLanguage(this.tempStore.ipInfo.lang).value):"default"!==this.settings.headers.spoofAcceptLang.value&&(e.requestHeaders[c].value=i.getLanguage(this.settings.headers.spoofAcceptLang.value).value)):"dnt"===l&&(s=c)}if(this.settings.headers.enableDNT?-1===s&&e.requestHeaders.push({name:"DNT",value:"1"}):s>-1&&e.requestHeaders.splice(s,1),a.active?a.spoofIP&&(e.url.includes("cdn-cgi/challenge-platform/generate/")||e.url.includes("__cf_chl_jschl_tk__=")||e.url.includes("jschal/js/nocookie/transparent.gif")||(e.requestHeaders.push({name:"Via",value:"1.1 "+a.spoofIP}),e.requestHeaders.push({name:"X-Forwarded-For",value:a.spoofIP}))):this.settings.headers.spoofIP.enabled&&(e.url.includes("cdn-cgi/challenge-platform/generate/")||e.url.includes("__cf_chl_jschl_tk__=")||e.url.includes("jschal/js/nocookie/transparent.gif")||(e.requestHeaders.push({name:"Via",value:"1.1 "+this.tempStore.spoofIP}),e.requestHeaders.push({name:"X-Forwarded-For",value:this.tempStore.spoofIP}))),r&&c&&this.olderThanNinety){let t="empty";if("main_frame"==e.type)t="document";else if("sub_frame"==e.type)t="iframe";else if("font"==e.type)t="font";else if("imageset"==e.type||"image"==e.type)t="image";else if("media"==e.type){let a=e.requestHeaders.find(e=>"accept"==e.name.toLowerCase());"a"==a.value.charAt(0)?t="audio":"v"==a.value.charAt(0)?t="video":e.url.includes(".vtt")&&(t="track")}else"xslt"==e.type?t="xslt":"web_manifest"==e.type?t="manifest":"csp_report"==e.type?t="report":"object"==e.type?t="object":"stylesheet"==e.type?t="style":"script"==e.type&&(t="script");e.requestHeaders.push({name:"sec-fetch-dest",value:t});let a=u.a.determineRequestType("main_frame"==e.type?e.documentUrl:e.originUrl,e.url);e.requestHeaders.push({name:"sec-fetch-site",value:a});let n="no-cors",o=e.requestHeaders.findIndex(e=>"origin"==e.name.toLowerCase())>-1;"websocket"==e.type?n="websocket":"main_frame"==e.type||"sub_frame"==e.type?n="navigate":o&&(n="cors"),e.requestHeaders.push({name:"sec-fetch-mode",value:n}),"main_frame"===e.type&&e.requestHeaders.push({name:"sec-fetch-user",value:"?1"})}return{requestHeaders:e.requestHeaders}}modifyResponse(e){if(!this.settings.config.enabled)return;if(!this.checkWhitelist(e).active&&this.settings.headers.blockEtag)for(let t=0;t<e.responseHeaders.length;t++)"etag"===e.responseHeaders[t].name.toLowerCase()&&(e.responseHeaders[t].value="");return{responseHeaders:e.responseHeaders}}}var m,f,h=a(194);!function(e){e[e.None=0]="None",e[e.Custom=-1]="Custom"}(m||(m={})),function(e){e[e.Random=0]="Random",e[e.Custom=1]="Custom"}(f||(f={}));var d=a(7);c.a.firstTimeInstall(),d.a.state.version=browser.runtime.getManifest().version;let g=new class{constructor(e){this.settings=e,this.defaultSettings=e,this.tempStore={badge:{text:"",title:""},ipInfo:{cache:null,lang:"",tz:""},profile:"",screenSize:"",spoofIP:"",version:""},this.injectionScript=null,this.intervalTimeout=null,this.profileCache={},this.version=browser.runtime.getManifest().version,this.REGEX_UUID=/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/,this.FF_PROFILES={"win1-ff":"win1-esr2","win1-esr":"win1-esr2","win2-ff":"win2-esr2","win2-esr":"win2-esr2","win3-ff":"win3-esr2","win3-esr":"win3-esr2"},this.updateContextMenu=(e,t)=>{browser.contextMenus.removeAll();for(let e=0;e<this.settings.whitelist.rules.length;e++)browser.contextMenus.remove("chameleon-rule-"+this.settings.whitelist.rules[e].id);let a=document.createElement("a");a.href=e.pageUrl;let n=u.a.findWhitelistRule(this.settings.whitelist.rules,a.host,e.pageUrl);if(n)browser.contextMenus.create({id:"chameleon-rule-"+n.id,title:browser.i18n.getMessage("text-removeFromRule",n.name),contexts:["page"],onclick:e=>{let t=document.createElement("a");if(t.href=e.pageUrl,["http:","https:"].includes(t.protocol)){const e=this.settings.whitelist.rules.findIndex(e=>e.id===n.id);e>-1&&(this.settings.whitelist.rules[e].sites.splice(n.siteIndex,1),this.saveSettings(this.settings),this.buildInjectionScript())}}});else{browser.contextMenus.create({id:"chameleon-openInWhitelist",title:browser.i18n.getMessage("text-createNewRule"),contexts:["page"],onclick:function(e){var t=document.createElement("a");t.href=e.pageUrl,["http:","https:"].includes(t.protocol)&&browser.tabs.create({url:browser.runtime.getURL("/options/options.html#whitelist?site="+t.host)})}});for(let e=0;e<this.settings.whitelist.rules.length;e++)browser.contextMenus.create({id:"chameleon-rule-"+this.settings.whitelist.rules[e].id,title:browser.i18n.getMessage("text-addToRule",this.settings.whitelist.rules[e].name),contexts:["page"],onclick:t=>{let a=document.createElement("a");a.href=t.pageUrl,["http:","https:"].includes(a.protocol)&&(this.settings.whitelist.rules[e].sites.push({domain:a.host}),this.saveSettings(this.settings),this.buildInjectionScript())}})}browser.contextMenus.refresh()}}async buildInjectionScript(){this.injectionScript&&(await this.injectionScript.unregister(),this.injectionScript=null),this.injectionScript=await browser.contentScripts.register({allFrames:!0,matchAboutBlank:!0,matches:["http://*/*","https://*/*"],js:[{code:`\n            let settings = JSON.parse(\`${JSON.stringify(this.settings)}\`);\n            let tempStore = JSON.parse(\`${JSON.stringify(this.tempStore)}\`);\n            let profileCache = JSON.parse(\`${JSON.stringify(this.profileCache)}\`);\n            let seed = ${1e-8*Math.random()};\n            let randObjName = '${String.fromCharCode(65+Math.floor(26*Math.random()))+Math.random().toString(36).substring(Math.floor(5*Math.random())+5)}';\n          `},{file:"inject.js"}],runAt:"document_start"})}async changeBrowserSettings(){await browser.privacy.websites.cookieConfig.set({value:{behavior:this.settings.options.cookiePolicy,nonPersistentCookies:this.settings.options.cookieNotPersistent}}),["firstPartyIsolate","resistFingerprinting","trackingProtectionMode"].forEach(async e=>{await browser.privacy.websites[e].set({value:this.settings.options[e]})}),await browser.privacy.network.peerConnectionEnabled.set({value:!this.settings.options.disableWebRTC}),await browser.privacy.network.webRTCIPHandlingPolicy.set({value:this.settings.options.webRTCPolicy})}cleanSettings(){"string"==typeof this.settings.options.protectKBFingerprint.delay&&(this.settings.options.protectKBFingerprint.delay=Number(this.settings.options.protectKBFingerprint.delay)),"string"==typeof this.settings.headers.referer.xorigin&&(this.settings.headers.referer.xorigin=Number(this.settings.headers.referer.xorigin)),"string"==typeof this.settings.headers.referer.trimming&&(this.settings.headers.referer.trimming=Number(this.settings.headers.referer.trimming)),this.settings.settings&&delete this.settings.settings;let e=["audioContext","clientRects","cssExfil","mediaDevices"];for(let t=0;t<this.settings.whitelist.rules.length;t++)for(let a=0;a<e.length;a++)e[a]in this.settings.whitelist.rules[t].options||(this.settings.whitelist.rules[t].options[e[a]]=!1);this.settings.config.reloadIPStartupDelay=this.settings.config.reloadIPStartupDelay||0,this.FF_PROFILES[this.settings.profile.selected]&&(this.settings.profile.selected=this.FF_PROFILES[this.settings.profile.selected]),this.FF_PROFILES[this.settings.whitelist.defaultProfile]&&(this.settings.whitelist.defaultProfile=this.FF_PROFILES[this.settings.whitelist.defaultProfile]);for(let e=0;e<this.settings.whitelist.rules.length;e++)this.FF_PROFILES[this.settings.whitelist.rules[e].profile]&&(this.settings.whitelist.rules[e].profile=this.FF_PROFILES[this.settings.whitelist.rules[e].profile])}async init(e){if(/^0\.12/.test(e.version)?this.migrateLegacy(e):e.options?this.settings=e:this.settings=this.defaultSettings,this.cleanSettings(),browser.privacy){let e=await browser.privacy.websites.cookieConfig.get({});this.settings.options.cookieNotPersistent=e.value.nonPersistentCookies,this.settings.options.cookiePolicy=e.value.behavior;let t=await browser.privacy.websites.firstPartyIsolate.get({});this.settings.options.firstPartyIsolate=t.value;let a=await browser.privacy.websites.resistFingerprinting.get({});this.settings.options.resistFingerprinting=a.value;let n=await browser.privacy.websites.trackingProtectionMode.get({});this.settings.options.trackingProtectionMode=n.value;let o=await browser.privacy.network.peerConnectionEnabled.get({});this.settings.options.disableWebRTC=!o.value;let i=await browser.privacy.network.webRTCIPHandlingPolicy.get({});this.settings.options.webRTCPolicy=i.value}this.platform=await browser.runtime.getPlatformInfo(),this.browserInfo=await browser.runtime.getBrowserInfo(),this.tempStore.version=this.browserInfo.version,this.intercept=new p(this.settings,this.tempStore,this.profileCache,parseInt(this.browserInfo.version)<90),await this.saveSettings(this.settings)}getProfileInUse(){if("none"===this.settings.profile.selected||this.settings.excluded.includes(this.settings.profile.selected)||"none"===this.tempStore.profile)this.tempStore.badge={text:"",title:browser.i18n.getMessage("text-realProfile")};else{let e=(new r.a).getAllProfiles();for(let t of Object.keys(e)){let a=e[t].find(e=>e.id==(/\d/.test(this.settings.profile.selected)?this.settings.profile.selected:this.tempStore.profile));null!=a&&(this.tempStore.badge={text:a.badge,title:a.name})}}}migrateLegacy(e,t=!1){let a=Object.assign({},this.defaultSettings),n="",o=i.getAllLanguages(),c=(new r.a).getAllProfiles(),l=Object(s.a)().map(e=>e.zone),p={custom:" none",real:"none",none:"none",random:"random",randomDesktop:"randomDesktop",randomMobile:"randomMobile",random_win:"windows",random_mac:"macOS",random_linux:"linux",random_ios:"iOS",random_android:"android",win1:"win1-gcr",win2:"win2-gcr",win3:"win3-gcr",win4:"win4-gcr",win6:"win1-esr2",win7:"win2-esr2",win8:"win3-esr2",win9:"win4-ff",win10:"win2-ie",win11:"win1-ie",win12:"win3-ie",win13:"win1-esr2",win14:"win3-esr2",win15:"win4-esr",win16:"win4-ie",mac1:"mac1-gcr",mac2:"mac2-gcr",mac3:"mac1-ff",mac4:"mac2-ff",mac5:"mac1-sf",mac6:"mac2-sf",mac7:"mac3-sf",mac8:"mac1-esr",linux1:"lin1-gcr",linux2:"lin2-gcr",linux3:"lin3-gcr",linux4:"lin1-cr",linux5:"lin2-cr",linux6:"lin3-cr",linux7:"lin1-ff",linux8:"lin2-ff",linux9:"lin3-ff",linux10:"lin1-esr",linux11:"lin3-esr",ios1:"ios1-sfm",ios2:"ios1-sft",ios3:"ios1-gcrm",ios4:"ios2-sft",ios5:"ios2-gcrm",ios6:"ios2-sfm",ios7:"ios3-sfm",ios8:"ios3-sft",ios9:"ios3-gcrm",android1:"and4-ff",android2:"and1-gcrm",android3:"and1-gcrm",android4:"and2-gcrm",android5:"and2-gcrm",android6:"and3-gcrm",android7:"and3-gcrm",android8:"and3-gcrm",android9:"and4-gcrm"};if(!e.settings&&t)return n=browser.i18n.getMessage("options-import-invalid-settings"),{error:!0,msg:n};if(t){let t=[["config.notificationsEnabled",e.settings.notificationsEnabled,"boolean"],["options.limitHistory",e.settings.limitHistory,"boolean"],["options.protectWinName",e.settings.protectWinName,"boolean"],["profile.interval.option",e.settings.interval,[0,-1,1,5,10,20,30,40,50,60]],["profile.interval.min",null===e.settings.minInterval?1:e.settings.minInterval,"number"],["profile.interval.max",null===e.settings.maxInterval?1:e.settings.maxInterval,"number"],["options.spoofClientRects",e.settings.spoofClientRects,"boolean"],["options.spoofAudioContext",e.settings.spoofAudioContext,"boolean"],["options.webSockets",e.settings.webSockets,["allow_all","block_3rd_party","block_all"]],["options.protectKBFingerprint.enabled",e.settings.protectKeyboardFingerprint,"boolean"],["options.protectKBFingerprint.delay",e.settings.kbDelay,"number"],["options.screenSize",e.settings.screenSize,["default","profile","1366x768","1440x900","1600x900","1920x1080","2560x1440","2560x1600"]],["options.timeZone",e.settings.timeZone,l.concat(["default","ip"])]];for(let e=0;e<t.length;e++){let a=this.checkValidOption(t[e][0],t[e][1],t[e][2]);if(a.error)return a}}if(a.config.notificationsEnabled=e.settings.notificationsEnabled,a.options.limitHistory=e.settings.limitHistory,a.options.protectWinName=e.settings.protectWinName,a.profile.interval.option=e.settings.interval,a.profile.interval.min=null===e.settings.minInterval?1:e.settings.minInterval,a.profile.interval.max=null===e.settings.maxInterval?1:e.settings.maxInterval,a.options.spoofClientRects=e.settings.spoofClientRects,a.options.spoofAudioContext=e.settings.spoofAudioContext,a.options.webSockets=e.settings.webSockets,a.options.protectKBFingerprint.enabled=e.settings.protectKeyboardFingerprint,a.options.protectKBFingerprint.delay=e.settings.kbDelay,a.options.screenSize=e.settings.screenSize,a.options.timeZone=e.settings.timeZone,e.settings.useragent)if(e.settings.useragent in p)a.profile.selected=p[e.settings.useragent];else if(t)return n=browser.i18n.getMessage("options-import-invalid-profile"),{error:!0,msg:n};if(!e.headers&&t)return n=browser.i18n.getMessage("options-import-invalid-headers"),{error:!0,msg:n};{let n="";if(n=""===e.headers.spoofAcceptLangValue?"default":"ip"===e.headers.spoofAcceptLangValue?"ip":o.find(t=>t.value===e.headers.spoofAcceptLangValue).code,t){e.headers.refererXorigin=Number(e.headers.refererXorigin)||0,e.headers.refererTrimming=Number(e.headers.refererTrimming)||0;let t=[["headers.blockEtag",e.headers.blockEtag,"boolean"],["headers.enableDNT",e.headers.enableDNT,"boolean"],["headers.referer.disabled",e.headers.disableRef,"boolean"],["headers.referer.xorigin",e.headers.refererXorigin,[0,1,2]],["headers.referer.trimming",e.headers.refererTrimming,[0,1,2]],["headers.spoofAcceptLang.enabled",e.headers.spoofAcceptLang,"boolean"],["headers.spoofAcceptLang.value",n,["default","ip"].concat(o.map(e=>e.code))],["headers.spoofIP.enabled",e.headers.spoofIP,"boolean"],["headers.spoofIP.option",Number(e.headers.spoofIPValue),[0,1]],["headers.spoofIP.rangeFrom",u.a.isValidIP(e.headers.rangeFrom),"boolean"],["headers.spoofIP.rangeTo",u.a.isValidIP(e.headers.rangeTo),"boolean"]];for(let e=0;e<t.length;e++){let a=this.checkValidOption(t[e][0],t[e][1],t[e][2]);if(a.error)return a}}a.headers.blockEtag=e.headers.blockEtag,a.headers.enableDNT=e.headers.enableDNT,a.headers.referer.disabled=e.headers.disableRef,a.headers.referer.xorigin=e.headers.refererXorigin,a.headers.referer.trimming=e.headers.refererTrimming,a.headers.spoofAcceptLang.enabled=e.headers.spoofAcceptLang,a.headers.spoofAcceptLang.value=n,a.headers.spoofIP.enabled=e.headers.spoofIP,a.headers.spoofIP.option=e.headers.spoofIPValue,a.headers.spoofIP.rangeFrom=e.headers.rangeFrom,a.headers.spoofIP.rangeTo=e.headers.rangeTo}if(!e.excluded&&t)return n=browser.i18n.getMessage("options-import-invalid-excluded"),{error:!0,msg:n};{let t=new Set;["win","mac","linux","ios","android"].forEach(a=>{e.excluded[a].filter((e,n)=>{e&&("win5"==`${a}${n+1}`?(t.add("win1-edg"),t.add("win2-edg"),t.add("win3-edg"),t.add("win4-edg")):t.add(p[`${a}${n+1}`]))})}),e.excluded.win[12]&&e.excluded.win[13]&&e.excluded.win[14]&&t.add("win2-esr");let n=["windows","macOS","linux","iOS","android"];e.excluded.all.filter((e,a)=>{e&&c[n[a]].forEach(e=>{t.add(e.id)})}),a.excluded=Array.from(t)}if(!e.ipRules&&t)return n=browser.i18n.getMessage("options-import-invalid-ipRules"),{error:!0,msg:n};for(let t=0;t<e.ipRules.length;t++){let n={};n.id=Object(h.a)(),n.name="Rule #"+(t+1),n.lang=o.find(a=>a.name==e.ipRules[t].lang).code,n.tz=e.ipRules[t].tz,n.ips=[];for(let a=0;a<e.ipRules[t].ip.length;a++)n.ips.push(u.a.getIPRange(e.ipRules[t].ip[a]));a.ipRules.push(n)}if(!e.whitelist&&t)return n=browser.i18n.getMessage("options-import-invalid-whitelist"),{error:!0,msg:n};if(t){let t=[["whitelist.enabledContextMenu",e.whitelist.enabledContextMenu,"boolean"],["whitelist.defaultProfile",e.whitelist.defaultProfile,Object.keys(p)]];for(let e=0;e<t.length;e++){let a=this.checkValidOption(t[e][0],t[e][1],t[e][2]);if(a.error)return a}}a.whitelist.enabledContextMenu=e.whitelist.enabledContextMenu,a.whitelist.defaultProfile=p[e.whitelist.defaultProfile],a.whitelist.rules=[];for(let t=0;t<e.whitelist.urlList.length;t++){let n={};n.id=Object(h.a)(),n.name="Rule #"+(t+1),n.sites=[];for(let a=0;a<e.whitelist.urlList[t].domains.length;a++){let o={domain:e.whitelist.urlList[t].domains[a].domain};""!=e.whitelist.urlList[t].domains[a].pattern&&(o.pattern=e.whitelist.urlList[t].domains[a].pattern),n.sites.push(o)}n.spoofIP=e.whitelist.urlList[t].spoofIP,n.profile="default"===e.whitelist.urlList[t].profile?"default":p[e.whitelist.urlList[t].profile];let i=o.find(a=>a.value===e.whitelist.urlList[t].lang);n.lang=i?i.code:"en-US",n.options={name:!0===e.whitelist.urlList[t].options.winName,ref:!0===e.whitelist.urlList[t].options.ref,tz:!0===e.whitelist.urlList[t].options.timezone,ws:!0===e.whitelist.urlList[t].options.websocket},a.whitelist.rules.push(n)}if(t)return setTimeout(async()=>{await this.saveSettings(a),browser.runtime.reload()},2500),n=browser.i18n.getMessage("options-import-success"),{error:!1,msg:n};this.settings=a}updateProfileCache(){let e=new r.a;this.settings.whitelist.defaultProfile in this.profileCache||"none"==this.settings.whitelist.defaultProfile||(this.profileCache[this.settings.whitelist.defaultProfile]=e.getProfile(this.settings.whitelist.defaultProfile));for(let t=0;t<this.settings.whitelist.rules.length;t++){let a=this.settings.whitelist.rules[t].profile;["default","none"].includes(a)||a in this.profileCache||(this.profileCache[a]=e.getProfile(a))}if(this.settings.profile.selected.includes("-")&&(this.settings.profile.selected in this.profileCache||(this.profileCache[this.settings.profile.selected]=e.getProfile(this.settings.profile.selected))),this.tempStore.profile&&"none"!=this.tempStore.profile&&(this.tempStore.profile in this.profileCache||(this.profileCache[this.tempStore.profile]=e.getProfile(this.tempStore.profile))),"profile"===this.settings.options.screenSize){let e;this.tempStore.profile&&"none"!=this.tempStore.profile?e=this.profileCache[this.tempStore.profile]:"none"!=this.settings.profile.selected&&(e=this.profileCache[this.settings.profile.selected]),this.tempStore.screenSize=e?`${e.screen.width}x${e.screen.height}`:""}this.getProfileInUse(),browser.runtime.sendMessage({action:"tempStore",data:this.tempStore},e=>{browser.runtime.lastError}),"android"!=this.platform.os&&(this.updateBadgeText(),browser.browserAction.setTitle({title:this.tempStore.badge.title}))}reset(){this.saveSettings(this.defaultSettings)}run(){this.start(),this.settings.config.notificationsEnabled&&browser.notifications.create({type:"basic",title:"Chameleon",message:browser.i18n.getMessage("notifications-profileChange")+" "+this.tempStore.badge.title})}setupHeaderListeners(){browser.webRequest.onBeforeRequest.addListener(e=>this.intercept.blockWebsocket(e),{urls:["<all_urls>"]},["blocking"]),browser.webRequest.onBeforeSendHeaders.addListener(e=>this.intercept.modifyRequest(e),{urls:["<all_urls>"]},["blocking","requestHeaders"]),browser.webRequest.onHeadersReceived.addListener(e=>this.intercept.modifyResponse(e),{urls:["<all_urls>"]},["blocking","responseHeaders"])}setTimer(e=null){browser.alarms.clearAll();let t={when:Date.now()+250};if(null===e&&(e=this.settings.profile.interval.option),e!=m.None)if(e===m.Custom){if(!this.settings.profile.interval.min||!this.settings.profile.interval.max)return;let e=Math.random()*(60*this.settings.profile.interval.max*1e3-60*this.settings.profile.interval.min*1e3)+60*this.settings.profile.interval.min*1e3;this.intervalTimeout&&clearTimeout(this.intervalTimeout);let t=this;this.intervalTimeout=setTimeout((function(){t.setTimer()}),e)}else t.periodInMinutes=e;browser.alarms.create(t)}start(){this.updateProfile(this.settings.profile.selected),this.updateSpoofIP(),this.updateProfileCache(),this.buildInjectionScript()}async saveSettings(e){await c.a.setSettings(Object.assign(Object.assign({},e),{version:this.version}))}updateBadgeText(e){void 0!==e&&(this.settings.profile.showProfileOnIcon=e),browser.browserAction.setBadgeText({text:this.settings.profile.showProfileOnIcon?this.tempStore.badge.text:""})}async updateIPInfo(){try{let e,t=await fetch("https://geoip-lookup.vercel.app/api/geoip");this.tempStore.ipInfo.cache=await t.json();let a=this.tempStore.ipInfo.cache;if(a.timezone&&a.languages||a.ip){let t=!1;for(let n=0;n<this.settings.ipRules.length;n++)for(let o=0;o<this.settings.ipRules[n].ips.length;o++)u.a.ipInRange(a.ip,this.settings.ipRules[n].ips[o].split("-"))&&(t=!0,this.tempStore.ipInfo.lang=this.settings.ipRules[n].lang,this.tempStore.ipInfo.tz=this.settings.ipRules[n].tz,e=`${browser.i18n.getMessage("notifications-usingIPRule")} ${this.tempStore.ipInfo.tz}, ${i.getLanguage(this.tempStore.ipInfo.lang).name}`);if(!t){if(""===a.timezone&&"ip"===this.settings.options.timeZone||""===a.languages&&"ip"===this.settings.headers.spoofAcceptLang.value)throw"Couldn't find info";if(e=browser.i18n.getMessage("notifications-usingIPInfo")+" ","ip"===this.settings.options.timeZone&&(this.tempStore.ipInfo.tz=a.timezone,e=`${e} ${a.timezone}${"ip"===this.settings.headers.spoofAcceptLang.value?", ":""}`),"ip"===this.settings.headers.spoofAcceptLang.value){let t,n=a.languages.split(",")[0],o=i.getAllLanguages();if("en"!==n&&"en-US"!==n)if(t=o.find(e=>e.nav.includes(n)),t)this.tempStore.ipInfo.lang=t.code;else{let e=[],a=n.split("-")[0];if("en"!==a)for(let t=0;t<o.length;t++){let n=o[t].nav.findIndex(e=>e.includes(a));n>-1&&e.push([t,n])}e.length>0&&(e.sort((e,t)=>e[1]>t[1]?1:e[1]<t[1]?-1:0),t=o[e[0][0]],this.tempStore.ipInfo.lang=t.code),t||(t=i.getLanguage("en-US"),this.tempStore.ipInfo.lang=t.nav[0])}else t=i.getLanguage("en-US"),this.tempStore.ipInfo.lang=t.nav[0];e=`${e} ${t.name}`}}browser.runtime.sendMessage({action:"tempStore",data:this.tempStore},e=>{browser.runtime.lastError}),this.settings.config.notificationsEnabled&&browser.notifications.create({type:"basic",title:"Chameleon",message:e}),this.buildInjectionScript()}}catch(e){let t=browser.i18n.getMessage("notifications-unableToGetIPInfo");this.settings.config.notificationsEnabled&&browser.notifications.create({type:"basic",title:"Chameleon",message:t})}}updateProfile(e){if(/\d|none/.test(e))this.tempStore.profile="";else{let t=new r.a(this.settings.excluded);e.includes("random")?this.tempStore.profile=t.getRandomByDevice(e):this.tempStore.profile=t.getRandomByOS(e)}}updateSpoofIP(){if(this.settings.headers.spoofIP.enabled)if(this.settings.headers.spoofIP.option===f.Random)this.tempStore.spoofIP=u.a.generateIP();else{let e=u.a.ipToInt(this.settings.headers.spoofIP.rangeFrom),t=u.a.ipToInt(this.settings.headers.spoofIP.rangeTo);this.tempStore.spoofIP=u.a.ipToString(Math.floor(Math.random()*(t-e+1)+e))}}checkValidOption(e,t,a){return"boolean"===a&&"boolean"==typeof t||"number"===a&&"number"==typeof t||a.includes(t)?{error:!1}:{error:!0,msg:`${browser.i18n.getMessage("options-import-invalid-setting")} ${e}`}}toggleContextMenu(e){e&&"android"!=this.platform.os?browser.contextMenus.onShown.addListener(this.updateContextMenu):"android"==this.platform.os||e||(browser.contextMenus.removeAll(),browser.contextMenus.onShown.removeListener(this.updateContextMenu))}validateSettings(e){var t;if(/^0\.12/.test(e.version))return this.migrateLegacy(e,!0);let a=Object.assign({},this.defaultSettings),n="",o=(new r.a).getAllProfiles(),c=Object.keys(o),l=[];for(let e of c)l=l.concat(o[e].map(e=>e.id));let p=i.getAllLanguages().map(e=>e.code),m=Object(s.a)().map(e=>e.zone);if(e.version.split(".",3).join(".")>this.settings.version.split(".",3).join("."))return n=browser.i18n.getMessage("options-import-invalid-version"),{error:!0,msg:n};if(!e.config)return n=browser.i18n.getMessage("options-import-invalid-config"),{error:!0,msg:n};{"hasPrivacyPermission"in e.config||(e.config.hasPrivacyPermission=!!browser.privacy);let t=[["config.enabled",e.config.enabled,"boolean"],["config.notificationsEnabled",e.config.notificationsEnabled,"boolean"],["config.theme",e.config.theme,["light","dark"]],["config.hasPrivacyPermission",null==e.config.hasPrivacyPermission?!!browser.privacy:e.config.hasPrivacyPermission,"boolean"],["config.reloadIPStartupDelay",e.config.reloadIPStartupDelay||0,"number"]];for(let n=0;n<t.length;n++){let o=this.checkValidOption(t[n][0],t[n][1],t[n][2]);if(o.error)return o;{let o=t[n][0].split("."),i=o[o.length-1],r=o.slice(0,-1).reduce((e,t)=>e[t],a),s=o.slice(0,-1).reduce((e,t)=>e[t],e);r[i]=s[i]}}}if(!e.excluded)return n=browser.i18n.getMessage("options-import-invalid-excluded"),{error:!0,msg:n};{let t=[];for(let a of e.excluded)l.includes(a)&&t.push(a);a.excluded=t}if(!e.ipRules)return n=browser.i18n.getMessage("options-import-invalid-ipRules"),{error:!0,msg:n};{let t=new Set;if(e.ipRules.some(e=>t.size===t.add(e.id).size))return n=browser.i18n.getMessage("options-import-invalid-ipRulesDupe"),{error:!0,msg:n};for(let t=0;t<e.ipRules.length;t++){let o=[["ipRule.lang",e.ipRules[t].lang,p],["ipRule.tz",e.ipRules[t].tz,Object(s.a)().map(e=>e.zone)]];for(let e=0;e<o.length;e++){let t=this.checkValidOption(o[e][0],o[e][1],o[e][2]);if(t.error)return t}if(""===e.ipRules[t].name)return n=browser.i18n.getMessage("options-import-invalid-ipRuleName"),{error:!0,msg:n};if(!this.REGEX_UUID.test(e.ipRules[t].id))return n=browser.i18n.getMessage("options-import-invalid-ipRuleId"),{error:!0,msg:n};for(let a=0;a<e.ipRules[t].ips.length;a++){let o=e.ipRules[t].ips[a].split("-");if(o.length>2||2===o.length&&!u.a.validateIPRange(o[0],o[1])||1===o.length&&!u.a.isValidIP(o[0]))return n=browser.i18n.getMessage("options-import-invalid-ipRuleRange"),{error:!0,msg:n}}a.ipRules=e.ipRules}}if(!e.profile)return n=browser.i18n.getMessage("options-import-invalid-profile"),{error:!0,msg:n};{this.FF_PROFILES[e.profile.selected]&&(e.profile.selected=this.FF_PROFILES[e.profile.selected]);let n=[["profile.selected",e.profile.selected,l.concat(["none","random","randomDesktop","randomMobile","windows","macOS","linux","iOS","android"])],["profile.interval.option",e.profile.interval.option,[0,-1,1,5,10,20,30,40,50,60]],["profile.interval.min",e.profile.interval.min,"number"],["profile.interval.max",e.profile.interval.max,"number"]];for(let t=0;t<n.length;t++){let o=this.checkValidOption(n[t][0],n[t][1],n[t][2]);if(o.error)return o;{let o=n[t][0].split("."),i=o[o.length-1],r=o.slice(0,-1).reduce((e,t)=>e[t],a),s=o.slice(0,-1).reduce((e,t)=>e[t],e);r[i]=s[i]}}null!=(null===(t=null==e?void 0:e.profile)||void 0===t?void 0:t.showProfileOnIcon)&&(a.profile.showProfileOnIcon=e.profile.showProfileOnIcon)}if(!e.headers)return n=browser.i18n.getMessage("options-import-invalid-headers"),{error:!0,msg:n};{let t=[["headers.blockEtag",e.headers.blockEtag,"boolean"],["headers.enableDNT",e.headers.enableDNT,"boolean"],["headers.referer.disabled",e.headers.referer.disabled,"boolean"],["headers.referer.xorigin",e.headers.referer.xorigin,[0,1,2]],["headers.referer.trimming",e.headers.referer.trimming,[0,1,2]],["headers.spoofAcceptLang.enabled",e.headers.spoofAcceptLang.enabled,"boolean"],["headers.spoofAcceptLang.value",e.headers.spoofAcceptLang.value,p.concat(["default","ip"])],["headers.spoofIP.enabled",e.headers.spoofIP.enabled,"boolean"],["headers.spoofIP.option",e.headers.spoofIP.option,[0,1]]];for(let n=0;n<t.length;n++){let o=this.checkValidOption(t[n][0],t[n][1],t[n][2]);if(o.error)return o;{let o=t[n][0].split("."),i=o[o.length-1],r=o.slice(0,-1).reduce((e,t)=>e[t],a),s=o.slice(0,-1).reduce((e,t)=>e[t],e);r[i]=s[i]}}if(e.headers.spoofIP.rangeFrom&&e.headers.spoofIP.rangeTo){if(!u.a.validateIPRange(e.headers.spoofIP.rangeFrom,e.headers.spoofIP.rangeTo))return n=browser.i18n.getMessage("options-import-invalid-spoofIP"),{error:!0,msg:n};a.headers.spoofIP.rangeFrom=e.headers.spoofIP.rangeFrom,a.headers.spoofIP.rangeTo=e.headers.spoofIP.rangeTo}}if(!e.options)return n=browser.i18n.getMessage("options-import-invalid-options"),{error:!0,msg:n};{"spoofMediaDevices"in e.options||(e.options.spoofMediaDevices=!1),"blockCSSExfil"in e.options||(e.options.blockCSSExfil=!1);let t=[["options.blockMediaDevices",e.options.blockMediaDevices,"boolean"],["options.disableWebRTC",e.options.disableWebRTC,"boolean"],["options.firstPartyIsolate",e.options.firstPartyIsolate,"boolean"],["options.limitHistory",e.options.limitHistory,"boolean"],["options.protectKBFingerprint.enabled",e.options.protectKBFingerprint.enabled,"boolean"],["options.protectKBFingerprint.delay",e.options.protectKBFingerprint.delay,"number"],["options.protectWinName",e.options.protectWinName,"boolean"],["options.resistFingerprinting",e.options.resistFingerprinting,"boolean"],["options.spoofAudioContext",e.options.spoofAudioContext,"boolean"],["options.spoofClientRects",e.options.spoofClientRects,"boolean"],["options.spoofFontFingerprint",e.options.spoofFontFingerprint,"boolean"],["options.spoofMediaDevices",e.options.spoofMediaDevices,"boolean"],["options.blockCSSExfil",e.options.blockCSSExfil,"boolean"],["options.screenSize",e.options.screenSize,["default","profile","1366x768","1440x900","1600x900","1920x1080","1920x1200","2560x1440","2560x1600","3840x2160","4096x2304","5120x2880"]],["options.timeZone",e.options.timeZone,m.concat(["default","ip"])],["options.cookieNotPersistent",e.options.cookieNotPersistent,"boolean"],["options.cookiePolicy",e.options.cookiePolicy,["allow_all","allow_visited","reject_all","reject_third_party","reject_trackers","reject_trackers_and_partition_foreign"]],["options.trackingProtectionMode",e.options.trackingProtectionMode,["always","never","private_browsing"]],["options.webRTCPolicy",e.options.webRTCPolicy,["default","default_public_and_private_interfaces","default_public_interface_only","disable_non_proxied_udp"]],["options.webSockets",e.options.webSockets,["allow_all","block_3rd_party","block_all"]]];for(let n=0;n<t.length;n++){let o=this.checkValidOption(t[n][0],t[n][1],t[n][2]);if(o.error)return o;{let o=t[n][0].split("."),i=o[o.length-1],r=o.slice(0,-1).reduce((e,t)=>e[t],a),s=o.slice(0,-1).reduce((e,t)=>e[t],e);r[i]=s[i]}}}if(!e.whitelist)return n=browser.i18n.getMessage("options-import-invalid-whitelist"),{error:!0,msg:n};{let t=new Set;if(e.whitelist.rules.some(e=>t.size===t.add(e.id).size))return n=browser.i18n.getMessage("options-import-invalid-whitelistDupe"),{error:!0,msg:n};this.FF_PROFILES[e.whitelist.defaultProfile]&&(e.whitelist.defaultProfile=this.FF_PROFILES[e.whitelist.defaultProfile]);let o=[["whitelist.enabledContextMenu",e.whitelist.enabledContextMenu,"boolean"],["whitelist.defaultProfile",e.whitelist.defaultProfile,l.concat(["none"])]];for(let t=0;t<o.length;t++){let n=this.checkValidOption(o[t][0],o[t][1],o[t][2]);if(n.error)return n;{let n=o[t][0].split("."),i=n[n.length-1],r=n.slice(0,-1).reduce((e,t)=>e[t],a),s=n.slice(0,-1).reduce((e,t)=>e[t],e);r[i]=s[i]}}for(let t=0;t<e.whitelist.rules.length;t++){this.FF_PROFILES[e.whitelist.rules[t].profile]&&(e.whitelist.rules[t].profile=this.FF_PROFILES[e.whitelist.rules[t].profile]);let a=[["whitelist.rules.lang",e.whitelist.rules[t].lang,p],["whitelist.rules.profile",e.whitelist.rules[t].profile,l.concat(["default","none"])]];for(let e=0;e<a.length;e++){let t=this.checkValidOption(a[e][0],a[e][1],a[e][2]);if(t.error)return t}if(""===e.whitelist.rules[t].name)return n=browser.i18n.getMessage("options-import-invalid-whitelistName"),{error:!0,msg:n};if(!this.REGEX_UUID.test(e.whitelist.rules[t].id))return n=browser.i18n.getMessage("options-import-invalid-whitelistId"),{error:!0,msg:n};if(e.whitelist.rules[t].spoofIP&&!u.a.isValidIP(e.whitelist.rules[t].spoofIP))return n=browser.i18n.getMessage("options-import-invalid-whitelistSpoofIP"),{error:!0,msg:n};if(e.version<"0.22"){let a=Object.keys(e.whitelist.rules[t].options);if(!(4===a.length&&a.includes("name")&&a.includes("ref")&&a.includes("tz")&&a.includes("ws")&&"boolean"==typeof e.whitelist.rules[t].options.name&&"boolean"==typeof e.whitelist.rules[t].options.ref&&"boolean"==typeof e.whitelist.rules[t].options.tz&&"boolean"==typeof e.whitelist.rules[t].options.ws))return n=browser.i18n.getMessage("options-import-invalid-whitelistOpt"),{error:!0,msg:n}}else{let a=Object.keys(e.whitelist.rules[t].options);if(!(8===a.length&&a.includes("audioContext")&&a.includes("clientRects")&&a.includes("cssExfil")&&a.includes("mediaDevices")&&a.includes("name")&&a.includes("ref")&&a.includes("tz")&&a.includes("ws")&&"boolean"==typeof e.whitelist.rules[t].options.audioContext&&"boolean"==typeof e.whitelist.rules[t].options.clientRects&&"boolean"==typeof e.whitelist.rules[t].options.cssExfil&&"boolean"==typeof e.whitelist.rules[t].options.mediaDevices&&"boolean"==typeof e.whitelist.rules[t].options.name&&"boolean"==typeof e.whitelist.rules[t].options.ref&&"boolean"==typeof e.whitelist.rules[t].options.tz&&"boolean"==typeof e.whitelist.rules[t].options.ws))return n=browser.i18n.getMessage("options-import-invalid-whitelistOpt"),{error:!0,msg:n}}}a.whitelist=e.whitelist}return a.config.hasPrivacyPermission||browser.permissions.remove({permissions:["privacy"]}),setTimeout(async()=>{this.settings=a,await this.saveSettings(this.settings),this.settings.config.hasPrivacyPermission&&await this.changeBrowserSettings(),browser.runtime.reload()},2500),n=browser.i18n.getMessage("options-import-success"),{error:!1,msg:n}}}(JSON.parse(JSON.stringify(d.a.state))),b=(e,t,a)=>{if("save"===e.action)g.timeout&&clearTimeout(g.timeout),g.settings=Object.assign(g.settings,e.data),g.timeout=setTimeout(()=>{g.saveSettings(e.data),a("done")},200);else if("implicitSave"===e.action)g.timeout&&clearTimeout(g.timeout),g.timeout=setTimeout(()=>{g.saveSettings(g.settings),a("done")},200);else if("contextMenu"===e.action)g.toggleContextMenu(e.data);else if("toggleBadgeText"===e.action)g.updateBadgeText(e.data);else if("getSettings"===e.action)(async()=>{if(browser.privacy){let e=await browser.privacy.websites.cookieConfig.get({});g.settings.options.cookieNotPersistent=e.value.nonPersistentCookies,g.settings.options.cookiePolicy=e.value.behavior;let t=await browser.privacy.websites.firstPartyIsolate.get({});g.settings.options.firstPartyIsolate=t.value;let a=await browser.privacy.websites.resistFingerprinting.get({});g.settings.options.resistFingerprinting=a.value;let n=await browser.privacy.websites.trackingProtectionMode.get({});g.settings.options.trackingProtectionMode=n.value;let o=await browser.privacy.network.peerConnectionEnabled.get({});g.settings.options.disableWebRTC=!o.value;let i=await browser.privacy.network.webRTCIPHandlingPolicy.get({});g.settings.options.webRTCPolicy=i.value}let e=Object.assign({},g.settings);e.config.hasPrivacyPermission=!!browser.privacy,a(e)})();else if("init"===e.action)browser.runtime.sendMessage({action:"tempStore",data:g.tempStore},e=>{browser.runtime.lastError}),a("done");else if("reloadInjectionScript"===e.action)g.buildInjectionScript(),a("done");else if("reloadIPInfo"===e.action)("ip"===g.settings.options.timeZone||"ip"===g.settings.headers.spoofAcceptLang.value&&g.settings.headers.spoofAcceptLang.enabled)&&(g.updateIPInfo(),a("done"));else if("reloadProfile"===e.action)g.setTimer(e.data),g.buildInjectionScript(),a("done");else if("reloadSpoofIP"===e.action)"headers.spoofIP.enabled"===e.data[0].name?g.settings.headers.spoofIP.enabled=e.data[0].value:"headers.spoofIP.option"===e.data[0].name?g.settings.headers.spoofIP.option=e.data[0].value:"headers.spoofIP.rangeFrom"===e.data[0].name&&(g.settings.headers.spoofIP.rangeFrom=e.data[0].value,g.settings.headers.spoofIP.rangeTo=e.data[1].value),g.updateSpoofIP(),a("done");else if("reset"===e.action)g.reset(),browser.runtime.reload();else if("updateIPRules"===e.action)g.settings.ipRules=e.data,g.timeout=setTimeout(()=>{g.saveSettings(g.settings),a("done")},200);else if("updateProfile"===e.action)g.settings.profile.selected=e.data,g.setTimer(),a("done");else if("updateWhitelist"===e.action)g.settings.whitelist=e.data,g.updateProfileCache(),g.timeout=setTimeout(()=>{g.saveSettings(g.settings),a("done")},200);else if("validateSettings"===e.action){let t=g.validateSettings(e.data);a(t)}return!0};browser.alarms.onAlarm.addListener(()=>{g.run()}),browser.runtime.onMessage.addListener(b),(async()=>{await g.init(await c.a.getSettings(null)),("ip"===g.settings.options.timeZone||"ip"===g.settings.headers.spoofAcceptLang.value&&g.settings.headers.spoofAcceptLang.enabled)&&setTimeout(()=>{g.updateIPInfo()},1e3*g.settings.config.reloadIPStartupDelay),browser.privacy&&await g.changeBrowserSettings(),g.setupHeaderListeners(),g.setTimer(),c.a.enableChameleon(g.settings.config.enabled),g.toggleContextMenu(g.settings.whitelist.enabledContextMenu),browser.runtime.getManifest().version_name.includes("-")&&browser.runtime.onMessageExternal.addListener((e,t,a)=>(b(e,0,a),!0)),"android"!=g.platform.os&&browser.browserAction.setBadgeBackgroundColor({color:"green"})})()},2:function(e,t,a){"use strict";const n=a(10),o=new(a(13)),i=new RegExp("^https?:","i"),r=document.createElement("a");let s=(e,t)=>(Object.entries(t).forEach(([t,a])=>{a&&"object"==typeof a?s(e[t]=e[t]||{},a):e[t]=a}),e),u=()=>{let e=Math.floor(256*Math.random());return 10===e||172===e||192===e?u():e},c=e=>e.split(".").reduce((function(e,t){return(e<<8)+parseInt(t,10)}),0)>>>0,l=e=>/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(e);t.a={deepMerge:s,determineRequestType:(e,t)=>{if(!e)return"none";r.href=e;let a=n.parse(r.hostname);r.href=t;let o=n.parse(r.hostname);return a.domain!=o.domain?"cross-site":a.subdomain===o.subdomain?"same-origin":"same-site"},findWhitelistRule:(e,t,a)=>{for(var n=0;n<e.length;n++)for(var o=0;o<e[n].sites.length;o++){let r=new URL((i.test(e[n].sites[o].domain)?"":"http://")+e[n].sites[o].domain);if(t.includes(r.host.replace(/^(www\.)/,""))&&(!e[n].sites[o].pattern||e[n].sites[o].pattern&&new RegExp(e[n].sites[o].pattern).test(a)))return{id:e[n].id,siteIndex:o,name:e[n].name,lang:e[n].lang,pattern:e[n].sites[o],profile:e[n].profile,options:e[n].options,spoofIP:e[n].spoofIP}}return null},generateIP:()=>`${u()}.${u()}.${u()}.${u()}`,getIPRange:e=>{let t=o.range(e);return null===t?e:`${t.start}-${t.end}`},ipInRange:(e,t)=>{if(1===t.length)return e===t[0];{let a=c(e),n=c(t[0]),o=c(t[1]);return n<=a&&a<=o}},ipToInt:c,ipToString:e=>(e>>>24)+"."+(e>>16&255)+"."+(e>>8&255)+"."+(255&e),isInternalIP:e=>/^localhost$|^127(?:\.[0-9]+){0,2}\.[0-9]+$|^(?:0*\:)*?:?0*1$/.test(e)||/(^192\.168\.([0-9]|[0-9][0-9]|[0-2][0-5][0-5])\.([0-9]|[0-9][0-9]|[0-2][0-5][0-5])$)|(^172\.([1][6-9]|[2][0-9]|[3][0-1])\.([0-9]|[0-9][0-9]|[0-2][0-5][0-5])\.([0-9]|[0-9][0-9]|[0-2][0-5][0-5])$)|(^10\.([0-9]|[0-9][0-9]|[0-2][0-5][0-5])\.([0-9]|[0-9][0-9]|[0-2][0-5][0-5])\.([0-9]|[0-9][0-9]|[0-2][0-5][0-5])$)/.test(e),isValidIP:l,isValidURL:e=>{try{return/^https?:\/\//i.test(e)||(e="http://"+e),new URL(e),!0}catch(e){return!1}},parseURL:e=>{let t=new URL(e),a=n.parse(t.hostname);return{base:t.hostname.split(".").splice(-2).join("."),domain:a.domain,hostname:t.hostname,origin:t.origin,pathname:t.pathname}},validateIPRange:(e,t)=>l(e)&&l(t)&&c(e)<=c(t)}},20:function(e,t,a){var n="undefined"!=typeof window&&window||"undefined"!=typeof self&&self||window,o=Function.prototype.apply;function i(e,t){this._id=e,this._clearFn=t}t.setTimeout=function(){return new i(o.call(setTimeout,n,arguments),clearTimeout)},t.setInterval=function(){return new i(o.call(setInterval,n,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},i.prototype.unref=i.prototype.ref=function(){},i.prototype.close=function(){this._clearFn.call(n,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout((function(){e._onTimeout&&e._onTimeout()}),t))},a(21),t.setImmediate="undefined"!=typeof self&&self.setImmediate||"undefined"!=typeof window&&window.setImmediate||this&&this.setImmediate,t.clearImmediate="undefined"!=typeof self&&self.clearImmediate||"undefined"!=typeof window&&window.clearImmediate||this&&this.clearImmediate},21:function(e,t,a){(function(e){!function(t,a){"use strict";if(!t.setImmediate){var n,o,i,r,s,u=1,c={},l=!1,p=t.document,m=Object.getPrototypeOf&&Object.getPrototypeOf(t);m=m&&m.setTimeout?m:t,"[object process]"==={}.toString.call(t.process)?n=function(t){e.nextTick((function(){h(t)}))}:!function(){if(t.postMessage&&!t.importScripts){var e=!0,a=t.onmessage;return t.onmessage=function(){e=!1},t.postMessage("","*"),t.onmessage=a,e}}()?t.MessageChannel?((i=new MessageChannel).port1.onmessage=function(e){h(e.data)},n=function(e){i.port2.postMessage(e)}):p&&"onreadystatechange"in p.createElement("script")?(o=p.documentElement,n=function(e){var t=p.createElement("script");t.onreadystatechange=function(){h(e),t.onreadystatechange=null,o.removeChild(t),t=null},o.appendChild(t)}):n=function(e){setTimeout(h,0,e)}:(r="setImmediate$"+Math.random()+"$",s=function(e){e.source===t&&"string"==typeof e.data&&0===e.data.indexOf(r)&&h(+e.data.slice(r.length))},t.addEventListener?t.addEventListener("message",s,!1):t.attachEvent("onmessage",s),n=function(e){t.postMessage(r+e,"*")}),m.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),a=0;a<t.length;a++)t[a]=arguments[a+1];var o={callback:e,args:t};return c[u]=o,n(u),u++},m.clearImmediate=f}function f(e){delete c[e]}function h(e){if(l)setTimeout(h,0,e);else{var t=c[e];if(t){l=!0;try{!function(e){var t=e.callback,a=e.args;switch(a.length){case 0:t();break;case 1:t(a[0]);break;case 2:t(a[0],a[1]);break;case 3:t(a[0],a[1],a[2]);break;default:t.apply(void 0,a)}}(t)}finally{f(e),l=!1}}}}}("undefined"==typeof self?"undefined"==typeof window?this:window:self)}).call(this,a(22))},22:function(e,t){var a,n,o=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function r(){throw new Error("clearTimeout has not been defined")}function s(e){if(a===setTimeout)return setTimeout(e,0);if((a===i||!a)&&setTimeout)return a=setTimeout,setTimeout(e,0);try{return a(e,0)}catch(t){try{return a.call(null,e,0)}catch(t){return a.call(this,e,0)}}}!function(){try{a="function"==typeof setTimeout?setTimeout:i}catch(e){a=i}try{n="function"==typeof clearTimeout?clearTimeout:r}catch(e){n=r}}();var u,c=[],l=!1,p=-1;function m(){l&&u&&(l=!1,u.length?c=u.concat(c):p=-1,c.length&&f())}function f(){if(!l){var e=s(m);l=!0;for(var t=c.length;t;){for(u=c,c=[];++p<t;)u&&u[p].run();p=-1,t=c.length}u=null,l=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===r||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function d(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var a=1;a<arguments.length;a++)t[a-1]=arguments[a];c.push(new h(e,t)),1!==c.length||l||s(f)},h.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=d,o.addListener=d,o.once=d,o.off=d,o.removeListener=d,o.removeAllListeners=d,o.emit=d,o.prependListener=d,o.prependOnceListener=d,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},3:function(e,t,a){"use strict";t.a={enableChameleon:e=>{browser.runtime.getPlatformInfo().then(t=>{"android"!=t.os&&(!1===e?browser.browserAction.setIcon({path:"../icons/icon_disabled.svg"}):browser.browserAction.setIcon({path:"../icons/icon.svg"}))})},enableContextMenu:e=>{browser.runtime.sendMessage({action:"contextMenu",data:e})},firstTimeInstall:()=>{browser.runtime.onInstalled.addListener(e=>{e.temporary||"install"!==e.reason||browser.tabs.create({url:"https://sereneblue.github.io/chameleon/?newinstall"})})},getSettings:e=>new Promise(t=>{browser.storage.local.get(e,a=>{t("string"==typeof e?a[e]:a)})}),sendToBackground:e=>{browser.runtime.sendMessage({action:"save",data:e})},setBrowserConfig:async(e,t)=>{if("options.cookiePolicy"===e||"options.cookieNotPersistent"===e){let a=await browser.privacy.websites.cookieConfig.get({});a=a.value,"options.cookiePolicy"===e?a.behavior=t:a.nonPersistentCookies=t,browser.privacy.websites.cookieConfig.set({value:a})}else if(["options.firstPartyIsolate","options.resistFingerprinting","options.trackingProtectionMode"].includes(e)){let a=e.split(".")[1];browser.privacy.websites[a].set({value:t})}else"options.disableWebRTC"===e?browser.privacy.network.peerConnectionEnabled.set({value:!t}):"options.webRTCPolicy"===e&&browser.privacy.network.webRTCIPHandlingPolicy.set({value:t})},setSettings:e=>new Promise(t=>{browser.storage.local.set(e,()=>{t()})})}},4:function(e,t,a){"use strict";a.r(t),a.d(t,"getAllLanguages",(function(){return i})),a.d(t,"getLanguage",(function(){return r}));const n=[{name:"Acholi",value:"ach,en-GB;q=0.8,en-US;q=0.5,en;q=0.3",code:"ach",nav:["ach","en-GB","en-US","en"]},{name:"Afrikaans",value:"af,en-ZA;q=0.8,en-GB;q=0.6,en-US;q=0.4,en;q=0.2",code:"af",nav:["af","en-ZA","en-GB","en"]},{name:"Albanian",value:"sq,sq-AL;q=0.8,en-US;q=0.5,en;q=0.3",code:"sq",nav:["sq","sq-AL","en-US","en"]},{name:"Arabic",value:"ar,en-US;q=0.7,en;q=0.3",code:"ar",nav:["ar","en-US","en"]},{name:"Aragonese",value:"an,es-ES;q=0.8,es;q=0.7,ca;q=0.5,en-US;q=0.3,en;q=0.2",code:"an",nav:["an","es-ES","es","ca","en-US","en"]},{name:"Armenian",value:"hy-AM,hy;q=0.8,en-US;q=0.5,en;q=0.3",code:"hy-AM",nav:["hy-AM","hy","en-US","en"]},{name:"Assamese",value:"as,en-US;q=0.7,en;q=0.3",code:"as",nav:["as","en-US","en"]},{name:"Asturian",value:"ast,es-ES;q=0.8,es;q=0.6,en-US;q=0.4,en;q=0.2",code:"ast",nav:["ast","es-ES","es","en-US","en"]},{name:"Azerbaijani",value:"az-AZ,az;q=0.8,en-US;q=0.5,en;q=0.3",code:"az-AZ",nav:["az-AZ","az","en-US","en"]},{name:"Basque",value:"eu,en-US;q=0.7,en;q=0.3",code:"eu",nav:["eu","en-US","en"]},{name:"Belarusian",value:"be,en-US;q=0.7,en;q=0.3",code:"be",nav:["be","en-US","en"]},{name:"Bengali (Bangladesh)",value:"bn-BD,bn;q=0.8,en-US;q=0.5,en;q=0.3",code:"bn-BD",nav:["bn-BD","bn","en-US","en"]},{name:"Bengali (India)",value:"bn-IN,bn;q=0.8,en-US;q=0.5,en;q=0.3",code:"bn-IN",nav:["bn-IN","bn","en-US","en"]},{name:"Bosnian",value:"bs-BA,bs;q=0.8,en-US;q=0.5,en;q=0.3",code:"bs-BA",nav:["bs-BA","bs","en-US","en"]},{name:"Breton",value:"br,fr-FR;q=0.8,fr;q=0.6,en-US;q=0.4,en;q=0.2",code:"br",nav:["br","fr-FR","fr","en-US","en"]},{name:"Bulgarian",value:"bg,en-US;q=0.7,en;q=0.3",code:"bg",nav:["bg","en-US","en"]},{name:"Burmese",value:"my,en-GB;q=0.7,en;q=0.3",code:"my",nav:["my","en-GB","en"]},{name:"Catalan",value:"ca,en-US;q=0.7,en;q=0.3",code:"ca",nav:["ca","en-US","en"]},{name:"Chinese (Hong Kong)",value:"zh-HK,zh;q=0.8,en-US;q=0.5,en;q=0.3",code:"zh-HK",nav:["zh-HK","zh","en-US","en"]},{name:"Chinese (Simplified)",value:"zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",code:"zh-CN",nav:["zh-CN","zh","zh-TW","zh-HK","en-US","en"]},{name:"Chinese (Traditional)",value:"zh-TW,zh;q=0.8,en-US;q=0.5,en;q=0.3",code:"zh-TW",nav:["zh-TW","zh","en-US","en"]},{name:"Croatian",value:"hr-HR,hr;q=0.8,en-US;q=0.5,en;q=0.3",code:"hr-HR",nav:["hr-HR","hr","en-US","en"]},{name:"Czech",value:"cs,sk;q=0.8,en-US;q=0.5,en;q=0.3",code:"cs",nav:["cs","sk","en-US","en"]},{name:"Danish",value:"da,en-US;q=0.7,en;q=0.3",code:"da",nav:["da","en-US","en"]},{name:"Dutch",value:"nl,en-US;q=0.7,en;q=0.3",code:"nl",nav:["nl","en-US","en"]},{name:"English (Australian)",value:"en-AU,en;q=0.5",code:"en-AU",nav:["en-AU","en"]},{name:"English (British)",value:"en-GB,en;q=0.5",code:"en-GB",nav:["en-GB","en"]},{name:"English (Canadian)",value:"en-CA,en-US;q=0.7,en;q=0.3",code:"en-CA",nav:["en-CA","en-US","en"]},{name:"English (South African)",value:"en-ZA,en-GB;q=0.8,en-US;q=0.5,en;q=0.3",code:"en-ZA",nav:["en-ZA","en-GB","en-US","en"]},{name:"English (US)",value:"en-US,en;q=0.5",code:"en-US",nav:["en-US","en"]},{name:"Esperanto",value:"eo,en-US;q=0.7,en;q=0.3",code:"eo",nav:["eo","en-US","en"]},{name:"Estonian",value:"et,et-EE;q=0.8,en-US;q=0.5,en;q=0.3",code:"et",nav:["et","et-EE","en-US","en"]},{name:"Finnish",value:"fi-FI,fi;q=0.8,en-US;q=0.5,en;q=0.3",code:"fi-FI",nav:["fi-FI","fi","en-US","en"]},{name:"French",value:"fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3",code:"fr",nav:["fr","fr-FR","en-US","en"]},{name:"Frisian",value:"fy-NL,fy;q=0.8,nl;q=0.6,en-US;q=0.4,en;q=0.2",code:"fy-NL",nav:["fy-NL","fy","nl","en-US","en"]},{name:"Fulah",value:"ff,fr-FR;q=0.8,fr;q=0.7,en-GB;q=0.5,en-US;q=0.3,en;q=0.2",code:"ff",nav:["ff","fr-FR","fr","en-GB","en-US","en"]},{name:"Gaelic (Scotland)",value:"gd-GB,gd;q=0.8,en-GB;q=0.6,en-US;q=0.4,en;q=0.2",code:"gd-GB",nav:["gd-GB","gd","en-GB","en-US","en"]},{name:"Galician",value:"gl-GL,gl;q=0.8,en-US;q=0.5,en;q=0.3",code:"gl-GL",nav:["gl-GL","gl","en-US","en"]},{name:"Georgian",value:"ka,ka-GE;q=0.7,en;q=0.3",code:"ka",nav:["ka","ka-GE","en"]},{name:"German",value:"de,en-US;q=0.7,en;q=0.3",code:"de",nav:["de","en-US","en"]},{name:"German (Switzerland)",value:"de-CH,de;q=0.8,en-US;q=0.5,en;q=0.3",code:"de-CH",nav:["de-CH","de","en-US","en"]},{name:"Greek",value:"el-GR,el;q=0.8,en-US;q=0.5,en;q=0.3",code:"el-GR",nav:["el-GR","el","en-US","en"]},{name:"Guarani",value:"gn,es;q=0.8,en;q=0.5,en-US;q=0.3",code:"gn",nav:["gn","es","en","en-US"]},{name:"Gujarati (India)",value:"gu-IN,gu;q=0.8,en-US;q=0.5,en;q=0.3",code:"gu-IN",nav:["gu-IN","gu","en-US","en"]},{name:"Hebrew",value:"he,he-IL;q=0.8,en-US;q=0.5,en;q=0.3",code:"he",nav:["he","he-IL","en-US","en"]},{name:"Hindi (India)",value:"hi-IN,hi;q=0.8,en-US;q=0.5,en;q=0.3",code:"hi-IN",nav:["hi-IN","hi","en-US","en"]},{name:"Hungarian",value:"hu-HU,hu;q=0.8,en-US;q=0.5,en;q=0.3",code:"hu-HU",nav:["hu-HU","hu","en-US","en"]},{name:"Icelandic",value:"is,en-US;q=0.7,en;q=0.3",code:"is",nav:["is","en-US","en"]},{name:"Indonesian",value:"id,en-US;q=0.7,en;q=0.3",code:"id",nav:["id","en-US","en"]},{name:"Interlingua",value:"ia,en-US;q=0.7,en;q=0.3",code:"ia",nav:["ia","en-US","en"]},{name:"Irish",value:"ga-IE,ga;q=0.8,en-IE;q=0.7,en-GB;q=0.5,en-US;q=0.3,en;q=0.2",code:"ga-IE",nav:["ga-IE","ga","en-IE","en-GB","en-US","en"]},{name:"Italian",value:"it-IT,it;q=0.8,en-US;q=0.5,en;q=0.3",code:"it-IT",nav:["it-IT","it","en-US","en"]},{name:"Japanese",value:"ja,en-US;q=0.7,en;q=0.3",code:"ja",nav:["ja","en-US","en"]},{name:"Kabyle",value:"kab-DZ,kab;q=0.8,fr-FR;q=0.7,fr;q=0.5,en-US;q=0.3,en;q=0.2",code:"kab-DZ",nav:["kab-DZ","kab","fr-FR","fr","en-US","en"]},{name:"Kannada",value:"kn-IN,kn;q=0.8,en-US;q=0.5,en;q=0.3",code:"kn-IN",nav:["kn-IN","kn","en-US","en"]},{name:"Kaqchikel",value:"cak,kaq;q=0.8,es;q=0.6,en-US;q=0.4,en;q=0.2",code:"cak",nav:["cak","kaq","es","en-US","en"]},{name:"Kazakh",value:"kk,ru;q=0.8,ru-RU;q=0.6,en-US;q=0.4,en;q=0.2",code:"kk",nav:["kk","ru","ru-RU","en-US","en"]},{name:"Khmer",value:"km,en-US;q=0.7,en;q=0.3",code:"km",nav:["km","en-US","en"]},{name:"Korean",value:"ko-KR,ko;q=0.8,en-US;q=0.5,en;q=0.3",code:"ko-KR",nav:["ko-KR","ko","en-US","en"]},{name:"Latvian",value:"lv,en-US;q=0.7,en;q=0.3",code:"lv",nav:["lv","en-US","en"]},{name:"Ligurian",value:"lij,it;q=0.8,en-US;q=0.5,en;q=0.3",code:"lij",nav:["lij","it","en-US","en"]},{name:"Lithuanian",value:"lt,en-US;q=0.8,en;q=0.6,ru;q=0.4,pl;q=0.2",code:"lt",nav:["lt","en-US","en","ru","pl"]},{name:"Lower Sorbian",value:"dsb,hsb;q=0.8,de;q=0.6,en-US;q=0.4,en;q=0.2",code:"dsb",nav:["dsb","hsb","de","en-US","en"]},{name:"Macedonian",value:"mk-MK,mk;q=0.8,en-US;q=0.5,en;q=0.3",code:"mk-MK",nav:["mk-MK","mk","en-US","en"]},{name:"Maithili",value:"mai,hi-IN;q=0.7,en;q=0.3",code:"mai",nav:["mai","hi-IN","en"]},{name:"Malay",value:"ms,en-US;q=0.7,en;q=0.3",code:"ms",nav:["ms","en-US","en"]},{name:"Malayalam",value:"ml-IN,ml;q=0.8,en-US;q=0.5,en;q=0.3",code:"ml-IN",nav:["ml-IN","ml","en-US","en"]},{name:"Marathi",value:"mr-IN,mr;q=0.8,en-US;q=0.5,en;q=0.3",code:"mr-IN",nav:["mr-IN","mr","en-US","en"]},{name:"Nepali",value:"ne-NP,ne;q=0.8,en-US;q=0.5,en;q=0.3",code:"ne-NP",nav:["ne-NP","ne","en-US","en"]},{name:"Norwegian (Bokmål)",value:"nb-NO,nb;q=0.9,no-NO;q=0.8,no;q=0.6,nn-NO;q=0.5,nn;q=0.4,en-US;q=0.3,en;q=0.1",code:"nb-NO",nav:["nb-NO","nb","no-NO","no","nn-NO","nn","en-US","en"]},{name:"Norwegian (Nynorsk)",value:"nn-NO,nn;q=0.9,no-NO;q=0.8,no;q=0.6,nb-NO;q=0.5,nb;q=0.4,en-US;q=0.3,en;q=0.1",code:"nn-NO",nav:["nn-NO","nn","no-NO","no","nb-NO","nb","en-US","en"]},{name:"Occitan (Lengadocian)",value:"oc-OC,oc;q=0.9,ca;q=0.8,fr;q=0.6,es;q=0.5,it;q=0.4,en-US;q=0.3,en;q=0.1",code:"oc-OC",nav:["oc-OC","oc","ca","fr","es","it","en-US","en"]},{name:"Odia",value:"or,en-US;q=0.7,en;q=0.3",code:"or",nav:["or","en-US","en"]},{name:"Persian",value:"fa-IR,fa;q=0.8,en-US;q=0.5,en;q=0.3",code:"fa-IR",nav:["fa-IR","fa","en-US","en"]},{name:"Polish",value:"pl,en-US;q=0.7,en;q=0.3",code:"pl",nav:["pl","en-US","en"]},{name:"Portuguese (Brazilian)",value:"pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3",code:"pt-BR",nav:["pt-BR","pt","en-US","en"]},{name:"Portuguese (Portugal)",value:"pt-PT,pt;q=0.8,en;q=0.5,en-US;q=0.3",code:"pt-PT",nav:["pt-PT","pt","en-US","en"]},{name:"Punjabi (India)",value:"pa,pa-IN;q=0.8,en-US;q=0.5,en;q=0.3",code:"pa",nav:["pa","pa-IN","en-US","en"]},{name:"Romanian",value:"ro-RO,ro;q=0.8,en-US;q=0.6,en-GB;q=0.4,en;q=0.2",code:"ro-RO",nav:["ro-RO","ro","en-US","en-GB","en"]},{name:"Romansh",value:"rm,rm-CH;q=0.8,de-CH;q=0.7,de;q=0.5,en-US;q=0.3,en;q=0.2",code:"rm",nav:["rm","rm-CH","de-CH","de","en-US","en"]},{name:"Russian",value:"ru-RU,ru;q=0.8,en-US;q=0.5,en;q=0.3",code:"ru-RU",nav:["ru-RU","ru","en-US","en"]},{name:"Serbian",value:"sr,sr-RS;q=0.8,sr-CS;q=0.6,en-US;q=0.4,en;q=0.2",code:"sr",nav:["sr","sr-RS","sr-CS","en-US","en"]},{name:"Sinhala",value:"si,si-LK;q=0.8,en-GB;q=0.5,en;q=0.3",code:"si",nav:["si","si-LK","en-GB","en"]},{name:"Slovak",value:"sk,cs;q=0.8,en-US;q=0.5,en;q=0.3",code:"sk",nav:["sk","cs","en-US","en"]},{name:"Slovenian",value:"sl,en-GB;q=0.7,en;q=0.3",code:"sl",nav:["sl","en-GB","en"]},{name:"Songhai",value:"son,son-ML;q=0.8,fr;q=0.6,en-US;q=0.4,en;q=0.2",code:"son",nav:["son","son-ML","fr","en-US","en"]},{name:"Spanish (Argentina)",value:"es-AR,es;q=0.8,en-US;q=0.5,en;q=0.3",code:"es-AR",nav:["es-AR","es","en-US","en"]},{name:"Spanish (Chile)",value:"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3",code:"es-CL",nav:["es-CL","es","en-US","en"]},{name:"Spanish (Mexico)",value:"es-MX,es;q=0.8,en-US;q=0.5,en;q=0.3",code:"es-MX",nav:["es-MX","es","en-US","en"]},{name:"Spanish (Spain)",value:"es-ES,es;q=0.8,en-US;q=0.5,en;q=0.3",code:"es-ES",nav:["es-ES","es","en-US","en"]},{name:"Swedish",value:"sv-SE,sv;q=0.8,en-US;q=0.5,en;q=0.3",code:"sv-SE",nav:["sv-SE","sv","en-US","en"]},{name:"Tamil",value:"ta-IN,ta;q=0.8,en-US;q=0.5,en;q=0.3",code:"ta-IN",nav:["ta-IN","ta","en-US","en"]},{name:"Telugu",value:"te-IN,te;q=0.8,en-US;q=0.5,en;q=0.3",code:"te-IN",nav:["te-IN","te","en-US","en"]},{name:"Thai",value:"th,en-US;q=0.7,en;q=0.3",code:"th",nav:["th","en-US","en"]},{name:"Turkish",value:"tr-TR,tr;q=0.8,en-US;q=0.5,en;q=0.3",code:"tr-TR",nav:["tr-TR","tr","en-US","en"]},{name:"Ukranian",value:"uk,ru;q=0.8,en-US;q=0.5,en;q=0.3",code:"uk",nav:["uk","ru","en-US","en"]},{name:"Upper Sorbian",value:"hsb,dsb;q=0.8,de;q=0.6,en-US;q=0.4,en;q=0.2",code:"hsb",nav:["hsb","dsb","de","en-US","en"]},{name:"Urdu",value:"ur-PK,ur;q=0.8,en-US;q=0.5,en;q=0.3",code:"ur-PK",nav:["ur-PK","ur","en-US","en"]},{name:"Uzbek",value:"uz,ru;q=0.8,en;q=0.5,en-US;q=0.3",code:"uz",nav:["uz","ru","en","en-US"]},{name:"Vietnamese",value:"vi-VN,vi;q=0.8,en-US;q=0.5,en;q=0.3",code:"vi-VN",nav:["vi-VN","vi","en-US","en"]},{name:"Welsh",value:"cy-GB,cy;q=0.8,en-US;q=0.5,en;q=0.3",code:"cy-GB",nav:["cy-GB","cy","en-US","en"]},{name:"Xhosa",value:"xh-ZA,xh;q=0.8,en-US;q=0.5,en;q=0.3",code:"xh-ZA",nav:["xh-ZA","xh","en-US","en"]}],o=n.reduce((e,t)=>(e[t.code]={name:t.name,nav:t.nav,value:t.value},e),{});let i=()=>n,r=e=>o[e]},5:function(e,t,a){"use strict";a.d(t,"a",(function(){return l}));const n=a(19),o={edg:{desktop:"139.0.0.0",desktopChrome:"139.0.0.0",deprecated:"109.0.1518.55",deprecatedChrome:"109.0.0.0",android:"139.0.0.0",androidChrome:"139.0.0.0"},esr:{desktop:"140"},esr2:{desktop:"128"},ff:{desktop:"142",mobile:"142"},gcr:{desktop:"139.0.0.0",deprecated:"109.0.0.0",ios:"139.0.7258.76",android:"139.0.0.0"},sf:{desktop:"18.5",ios1:"16.7.10",ios2:"17.7.2",ios3:"18.4"}},i=["1366x768","1440x900","1600x900","1920x1080","1920x1200","2560x1440","2560x1600","3840x2160"],r=["1920x1080","2560x1600","4096x2304","5120x2880"],s=["win1","win2","win3","win4","lin1","lin2","lin3"];let u=e=>"edg"===e||"edgm"===e?"EDG":"esr"===e||"esr2"===e?"ESR":"ff"===e||"ffm"===e||"fft"===e?"FF":"gcr"===e||"gcrm"===e||"gcrt"===e?"GC":"ie"===e?"IE":"sf"===e||"sft"===e||"sfm"===e?"SAF":void 0,c=(e,t)=>{let a;if("edg"===t)switch(e){case"Win 7":case"Win 8":case"Win 8.1":return`${e} - Edge ${o.edg.deprecated.split(".")[0]}`;default:return`${e} - Edge ${o.edg.desktop.split(".")[0]}`}else{if("edgm"===t)return`${e} - Edge ${o.edg.android.split(".")[0]} (Phone)`;if("esr"===t)return`${e} - Firefox ${o.esr.desktop} ESR`;if("esr2"===t)return`${e} - Firefox ${o.esr2.desktop} ESR`;if("ff"===t)return`${e} - Firefox ${o.ff.desktop}`;if("ffm"===t)return`${e} - Firefox ${o.ff.mobile} (Phone)`;if("fft"===t)return`${e} - Firefox ${o.ff.mobile} (Tablet)`;if("gcr"===t)switch(e){case"Win 7":case"Win 8":case"Win 8.1":return`${e} - Chrome ${o.gcr.deprecated.split(".")[0]}`;default:return`${e} - Chrome ${o.gcr.desktop.split(".")[0]}`}else{if("gcrm"===t){let t="i"===e.charAt(0)?"ios":"android";return`${e} - Chrome ${o.gcr[t].split(".")[0]} (Phone)`}if("gcrt"===t){let t="i"===e.charAt(0)?"ios":"android";return`${e} - Chrome ${o.gcr[t].split(".")[0]} (Tablet)`}if("ie"===t)return e+" - Internet Explorer 11";if("sf"===t)return`${e} - Safari ${o.sf.desktop.split(".")[0]}`;if("sfm"===t){switch(e){case"iOS 16":a="ios1";break;case"iOS 17":a="ios2";break;case"iOS 18":a="ios3"}return`${e} - Safari ${o.sf[a].split(".")[0]} (iPhone)`}if("sft"===t){switch(e){case"iOS 16":a="ios1";break;case"iOS 17":a="ios2";break;case"iOS 18":a="ios3"}return`${e} - Safari ${o.sf[a].split(".")[0]} (iPad)`}}}};class l{generateProfiles(e){let t=[],a=this.profiles[e];for(let e=0;e<a.length;e++)for(let n=0;n<a[e].browsers.length;n++){let o=`${a[e].id}-${a[e].browsers[n]}`;t.push({id:o,badge:u(a[e].browsers[n]),name:c(a[e].name,a[e].browsers[n])})}return t}constructor(e=[]){this.browsers={edg:e=>{let t,a,n;switch(e.id){case"win1":case"win2":case"win3":t=o.edg.deprecated,a=o.edg.deprecatedChrome;break;default:t=o.edg.desktop,a=o.edg.desktopChrome}switch(e.id){case"win1":case"win2":case"win3":case"win4":n=e.nav.oscpu;break;case"mac1":case"mac2":case"mac3":case"lin1":case"lin2":n=e.uaPlatform;break;case"lin3":n="X11; Linux x86_64"}let u=e.id.includes("mac")?r:i,c=u[Math.floor(Math.random()*u.length)].split("x").map(Number),l=`Mozilla/5.0 (${n}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${a} Safari/537.36 Edg/${t}`,p=s.includes(e.id)?Math.random()>.5?4:2:4;return{accept:{header:"text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"},osId:e.id,browser:"edge",navigator:{appMinorVersion:null,appVersion:l.split("Mozilla/")[1],buildID:null,cpuClass:null,deviceMemory:Math.random()>.5?4:8,hardwareConcurrency:p,mimeTypes:[{type:"application/pdf",suffixes:"pdf",description:"Portable Document Format"},{type:"text/pdf",suffixes:"pdf",description:"Portable Document Format"}],maxTouchPoints:0,oscpu:null,platform:n,plugins:[{name:"PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Chrome PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Chromium PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Microsoft Edge PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"WebKit built-in PDF",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]}],productSub:"20030107",userAgent:l,vendor:"Google Inc.",vendorSub:""},screen:{width:c[0],height:c[1],availHeight:c[1]+e.screenOffset}}},edgm:e=>{let t=o.edg;const a=n.getDevice("mobile",e.id);let i=a.viewport.split("x").map(Number),r=`Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${t.androidChrome} Mobile Safari/537.36 EdgA/${t.android}`;return{accept:{header:"text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"},osId:e.id,browser:"edge",navigator:{appMinorVersion:null,appVersion:r.split("Mozilla/")[1],buildID:null,cpuClass:null,deviceMemory:a.memory,hardwareConcurrency:a.hw,mimeTypes:[],maxTouchPoints:5,oscpu:null,platform:"Linux aarch64",plugins:[],productSub:"20030107",userAgent:r,vendor:"Google Inc.",vendorSub:""},screen:{width:i[0],height:i[1],availHeight:i[1],deviceScaleFactor:a.deviceScaleFactor}}},esr:e=>{let t,a,n=o.esr.desktop,u=e.id.includes("mac")?r:i,c=u[Math.floor(Math.random()*u.length)].split("x").map(Number),l=s.includes(e.id)?Math.random()>.5?4:2:4;switch(e.id){case"win1":case"win2":case"win3":case"win4":a=e.nav.oscpu,t="5.0 (Windows)";break;case"mac1":case"mac2":case"mac3":a="Macintosh; "+e.nav.oscpu,t="5.0 (Macintosh)";break;case"lin1":case"lin2":case"lin3":a=e.uaPlatform,t="5.0 (X11)"}let p=`Mozilla/5.0 (${a}; rv:${n}.0) Gecko/20100101 Firefox/${n}.0`;return{accept:{header:"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"},osId:e.id,browser:"firefox",navigator:{appMinorVersion:null,appVersion:t,buildID:"20181001000000",cpuClass:null,deviceMemory:null,hardwareConcurrency:l,mimeTypes:[],maxTouchPoints:0,oscpu:e.nav.oscpu,platform:e.nav.platform,plugins:[],productSub:"20100101",userAgent:p,vendor:"",vendorSub:""},screen:{width:c[0],height:c[1],availHeight:c[1]+e.screenOffset}}},esr2:e=>{let t,a,n=o.esr2.desktop,u=e.id.includes("mac")?r:i,c=u[Math.floor(Math.random()*u.length)].split("x").map(Number),l=s.includes(e.id)?Math.random()>.5?4:2:4;switch(e.id){case"win1":case"win2":case"win3":case"win4":a=e.nav.oscpu,t="5.0 (Windows)";break;case"mac1":case"mac2":case"mac3":a="Macintosh; "+e.nav.oscpu,t="5.0 (Macintosh)";break;case"lin1":case"lin2":case"lin3":a=e.uaPlatform,t="5.0 (X11)"}let p=`Mozilla/5.0 (${a}; rv:109.0) Gecko/20100101 Firefox/${n}.0`;return{accept:{header:"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"},osId:e.id,browser:"firefox",navigator:{appMinorVersion:null,appVersion:t,buildID:"20181001000000",cpuClass:null,deviceMemory:null,hardwareConcurrency:l,mimeTypes:[],maxTouchPoints:0,oscpu:e.nav.oscpu,platform:e.nav.platform,plugins:[],productSub:"20100101",userAgent:p,vendor:"",vendorSub:""},screen:{width:c[0],height:c[1],availHeight:c[1]+e.screenOffset}}},ff:e=>{let t,a,n=o.ff.desktop,u=e.id.includes("mac")?r:i,c=u[Math.floor(Math.random()*u.length)].split("x").map(Number),l=s.includes(e.id)?Math.random()>.5?4:2:4;switch(e.id){case"win1":case"win2":case"win3":case"win4":t="5.0 (Windows)",a=e.nav.oscpu;break;case"mac1":case"mac2":case"mac3":t="5.0 (Macintosh)",a="Macintosh; "+e.nav.oscpu,u=[];break;case"lin1":case"lin2":case"lin3":t="5.0 (X11)",a=e.uaPlatform}let p=`Mozilla/5.0 (${a}; rv:${n}.0) Gecko/20100101 Firefox/${n}.0`;return{accept:{header:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"},osId:e.id,browser:"firefox",navigator:{appMinorVersion:null,appVersion:t,buildID:"20181001000000",cpuClass:null,deviceMemory:null,hardwareConcurrency:l,mimeTypes:[{type:"application/pdf",suffixes:"pdf",description:"Portable Document Format"},{type:"text/pdf",suffixes:"pdf",description:"Portable Document Format"}],maxTouchPoints:0,oscpu:e.nav.oscpu,platform:e.nav.platform,plugins:[{name:"PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Chrome PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Chromium PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Microsoft Edge PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"WebKit built-in PDF",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]}],productSub:"20100101",userAgent:p,vendor:"",vendorSub:""},screen:{width:c[0],height:c[1],availHeight:c[1]+("lin2"===e.id?0:e.screenOffset)}}},ffm:e=>{let t,a=o.ff.mobile;const i=n.getDevice("mobile",e.id);let r=i.viewport.split("x").map(Number);return t=`Mozilla/5.0 (${e.uaPlatform}; Mobile; rv:${a}.0) Gecko/${a}.0 Firefox/${a}.0`,{accept:{header:"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"},osId:e.id,browser:"firefox",navigator:{appMinorVersion:null,appVersion:`5.0 (${e.uaPlatform})`,buildID:"20181001000000",cpuClass:null,deviceMemory:null,hardwareConcurrency:i.hw,mimeTypes:[{type:"application/pdf",suffixes:"pdf",description:"Portable Document Format"},{type:"text/pdf",suffixes:"pdf",description:"Portable Document Format"}],maxTouchPoints:5,oscpu:"Linux armv81",platform:"Linux armv81",plugins:[{name:"PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Chrome PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Chromium PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Microsoft Edge PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"WebKit built-in PDF",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]}],productSub:"20100101",userAgent:t,vendor:"",vendorSub:""},screen:{width:r[0],height:r[1],availHeight:r[1],deviceScaleFactor:i.deviceScaleFactor}}},fft:e=>{let t,a=o.ff.mobile;const i=n.getDevice("tablet",e.id);let r=i.viewport.split("x").map(Number);return t=`Mozilla/5.0 (${e.uaPlatform}; Tablet; rv:${a}.0) Gecko/109.0 Firefox/${a}.0`,{accept:{header:"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"},osId:e.id,browser:"firefox",navigator:{appMinorVersion:null,appVersion:`5.0 (${e.uaPlatform})`,buildID:"20181001000000",cpuClass:null,deviceMemory:null,hardwareConcurrency:i.hw,mimeTypes:[{type:"application/pdf",suffixes:"pdf",description:"Portable Document Format"},{type:"text/pdf",suffixes:"pdf",description:"Portable Document Format"}],maxTouchPoints:5,oscpu:"Linux aarch64",platform:"Linux aarch64",plugins:[{name:"PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Chrome PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Chromium PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Microsoft Edge PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"WebKit built-in PDF",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]}],productSub:"20100101",userAgent:t,vendor:"",vendorSub:""},screen:{width:r[0],height:r[1],availHeight:r[1],deviceScaleFactor:i.deviceScaleFactor}}},gcr:e=>{let t,a,n=e.id.includes("mac")?r:i,u=n[Math.floor(Math.random()*n.length)].split("x").map(Number);switch(e.id){case"win1":case"win2":case"win3":t=o.gcr.deprecated;break;default:t=o.gcr.desktop}switch(e.id){case"win1":case"win2":case"win3":case"win4":a="Windows NT 10.0; Win64; x64";break;case"mac1":case"mac2":case"mac3":a="Macintosh; Intel Mac OS X 10_15_7";break;case"lin1":case"lin2":case"lin3":a="X11; Linux x86_64"}let c=`Mozilla/5.0 (${a}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${t} Safari/537.36`,l=s.includes(e.id)?Math.random()>.5?4:2:4;return{accept:{header:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"},osId:e.id,browser:"chrome",navigator:{appMinorVersion:null,appVersion:c.split("Mozilla/")[1],buildID:null,cpuClass:null,deviceMemory:Math.random()>.5?4:8,hardwareConcurrency:l,mimeTypes:[{type:"application/pdf",suffixes:"pdf",description:"Portable Document Format"},{type:"text/pdf",suffixes:"pdf",description:"Portable Document Format"}],maxTouchPoints:0,oscpu:null,platform:e.nav.platform,plugins:[{name:"PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Chrome PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Chromium PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"Microsoft Edge PDF Viewer",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]},{name:"WebKit built-in PDF",filename:"internal-pdf-viewer",description:"Portable Document Format",__mimeTypes:["application/pdf","text/pdf"]}],productSub:"20030107",userAgent:c,vendor:"Google Inc.",vendorSub:""},screen:{width:u[0],height:u[1],availHeight:u[1]+e.screenOffset}}},gcrm:e=>{let t,a,i,r=o.gcr;const s=n.getDevice("mobile",e.id);let u=s.viewport.split("x").map(Number);return e.id.includes("ios")?(t=`Mozilla/5.0 (iPhone; CPU iPhone OS ${e.uaPlatform} like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/${r.ios} Mobile/15E148 Safari/604.1`,i={appMinorVersion:null,appVersion:t.split("Mozilla/")[1],buildID:null,cpuClass:null,deviceMemory:null,hardwareConcurrency:8,mimeTypes:[],maxTouchPoints:5,oscpu:null,platform:"iPhone",plugins:[],productSub:"20030107",userAgent:t,vendor:"Apple Computer, Inc.",vendorSub:""},a={header:"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"}):(t=`Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${r.android} Mobile Safari/537.36`,i={appMinorVersion:null,appVersion:t.split("Mozilla/")[1],buildID:null,cpuClass:null,deviceMemory:s.mem,hardwareConcurrency:s.hw,mimeTypes:[],maxTouchPoints:5,oscpu:null,platform:"Linux armv81",plugins:[],productSub:"20030107",userAgent:t,vendor:"Google Inc.",vendorSub:""},a={header:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"}),{accept:a,osId:e.id,browser:"chrome",navigator:i,screen:{width:u[0],height:u[1],availHeight:u[1],deviceScaleFactor:s.deviceScaleFactor}}},gcrt:e=>{let t,a,i,r=o.gcr;const s=n.getDevice("tablet",e.id);let u=s.viewport.split("x").map(Number);return e.id.includes("ios")?(t=`Mozilla/5.0 (iPad; CPU OS ${e.uaPlatform} like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/${r.ios} Mobile/15E148 Safari/604.1`,i={appMinorVersion:null,appVersion:t.split("Mozilla/")[1],buildID:null,cpuClass:null,deviceMemory:null,hardwareConcurrency:8,mimeTypes:[],maxTouchPoints:5,oscpu:null,platform:"iPad",plugins:[],productSub:"20030107",userAgent:t,vendor:"Apple Computer, Inc.",vendorSub:""},a={header:"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"}):(t=`Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${r.android} Safari/537.36`,i={appMinorVersion:null,appVersion:t.split("Mozilla/")[1],buildID:null,cpuClass:null,deviceMemory:s.mem,hardwareConcurrency:s.hw,mimeTypes:[],maxTouchPoints:5,oscpu:null,platform:"Linux armv8l",plugins:[],productSub:"20030107",userAgent:t,vendor:"Google Inc.",vendorSub:""},a={header:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"}),{accept:a,osId:e.id,browser:"chrome",navigator:i,screen:{width:u[0],height:u[1],availHeight:u[1],deviceScaleFactor:s.deviceScaleFactor}}},ie:e=>{let t,a=i.slice(0,4),n=a[Math.floor(Math.random()*a.length)].split("x").map(Number);switch(e.id){case"win1":t=n[1]-40;break;case"win2":case"win3":t=n[1]-40;break;case"win4":t=n[1]-30}return{accept:{header:"text/html, application/xhtml+xml, */*",encodingHTTP:"gzip",encodingHTTPS:"gzip, deflate"},osId:e.id,browser:"ie",navigator:{appMinorVersion:"0",appVersion:`5.0 (${e.nav.oscpu.split(";")[0]}; Trident/7.0; .NET4.0C; .NET4.0E; rv:11.0) like Gecko`,buildID:null,cpuClass:"x64",deviceMemory:null,hardwareConcurrency:null,mimeTypes:[],maxTouchPoints:0,oscpu:null,platform:"Win32",plugins:[],productSub:null,userAgent:`Mozilla/5.0 (${e.nav.oscpu.split(";")[0]}; WOW64; Trident/7.0; rv:11.0) like Gecko`,vendor:"",vendorSub:null},screen:{width:n[0],height:n[1],availHeight:t}}},sf:e=>{let t=o.sf.desktop,a=`Mozilla/5.0 (${e.uaPlatform}) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/${t} Safari/605.1.15`,n=r[Math.floor(Math.random()*r.length)].split("x").map(Number),i=Math.random()>.5?4:8;return{accept:{header:"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"},osId:e.id,browser:"safari",navigator:{appMinorVersion:null,appVersion:a.split("Mozilla/")[1],buildID:null,cpuClass:null,deviceMemory:null,hardwareConcurrency:i,mimeTypes:[{type:"application/pdf",suffixes:"pdf",description:"Portable Document Format"},{type:"text/pdf",suffixes:"pdf",description:"Portable Document Format"},{type:"application/postscript",suffixes:"ps",description:"PostScript"}],maxTouchPoints:0,oscpu:null,platform:"MacIntel",plugins:[{name:"WebKit built-in PDF",filename:"",description:"",__mimeTypes:["application/pdf","text/pdf","application/postscript"]}],productSub:"20030107",userAgent:a,vendor:"Apple Computer, Inc.",vendorSub:""},screen:{width:n[0],height:n[1],availHeight:n[1]+e.screenOffset}}},sfm:e=>{let t=o.sf[e.id];const a=n.getDevice("mobile",e.id);let i=a.viewport.split("x").map(Number),r=`Mozilla/5.0 (iPhone; CPU iPhone OS ${e.uaPlatform} like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/${t} Mobile/15E148 Safari/604.1`;return{accept:{header:"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"},osId:e.id,browser:"safari",navigator:{appMinorVersion:null,appVersion:r.split("Mozilla/")[1],buildID:null,cpuClass:null,deviceMemory:null,hardwareConcurrency:null,mimeTypes:[],maxTouchPoints:5,oscpu:null,platform:"iPhone",plugins:[],productSub:"20030107",userAgent:r,vendor:"Apple Computer, Inc.",vendorSub:""},screen:{width:i[0],height:i[1],availHeight:i[1],deviceScaleFactor:a.deviceScaleFactor}}},sft:e=>{let t=o.sf[e.id];const a=n.getDevice("tablet",e.id);let i=a.viewport.split("x").map(Number),r=`Mozilla/5.0 (iPad; CPU OS ${e.uaPlatform} like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/${t} Mobile/15E148 Safari/604.1`;return{accept:{header:"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",encodingHTTP:"gzip, deflate",encodingHTTPS:"gzip, deflate, br"},osId:e.id,browser:"safari",navigator:{appMinorVersion:null,appVersion:r.split("Mozilla/")[1],buildID:null,cpuClass:null,deviceMemory:null,hardwareConcurrency:null,mimeTypes:[],maxTouchPoints:5,oscpu:null,platform:"MacIntel",plugins:[],productSub:"20030107",userAgent:r,vendor:"Apple Computer, Inc.",vendorSub:""},screen:{width:i[0],height:i[1],availHeight:i[1],deviceScaleFactor:a.deviceScaleFactor}}}},this.profiles={windows:[{id:"win1",name:"Win 7",nav:{version:"5.0 (Windows)",oscpu:"Windows NT 6.1; Win64; x64",platform:"Win32"},screenOffset:-40,browsers:["edg","esr2","gcr","ie"]},{id:"win2",name:"Win 8",nav:{version:"5.0 (Windows)",oscpu:"Windows NT 6.2; Win64; x64",platform:"Win32"},screenOffset:-40,browsers:["edg","esr2","gcr","ie"]},{id:"win3",name:"Win 8.1",nav:{version:"5.0 (Windows)",oscpu:"Windows NT 6.3; Win64; x64",platform:"Win32"},screenOffset:-40,browsers:["edg","esr2","gcr","ie"]},{id:"win4",name:"Win 10",nav:{version:"5.0 (Windows)",oscpu:"Windows NT 10.0; Win64; x64",platform:"Win32"},screenOffset:-30,browsers:["edg","esr","esr2","ff","gcr","ie"]}],macOS:[{id:"mac1",name:"macOS 12",browsers:["edg","esr","esr2","ff","gcr","sf"],nav:{version:"",oscpu:"Intel Mac OS X 10.15",platform:"MacIntel"},screenOffset:-23,uaPlatform:"Macintosh; Intel Mac OS X 10_15_7"},{id:"mac2",name:"macOS 13",browsers:["edg","esr","esr2","ff","gcr","sf"],nav:{version:"",oscpu:"Intel Mac OS X 10.15",platform:"MacIntel"},screenOffset:-23,uaPlatform:"Macintosh; Intel Mac OS X 10_15_7"},{id:"mac3",name:"macOS 14",browsers:["edg","esr","esr2","ff","gcr","sf"],nav:{version:"",oscpu:"Intel Mac OS X 10.15",platform:"MacIntel"},screenOffset:-23,uaPlatform:"Macintosh; Intel Mac OS X 10_15_7"}],linux:[{id:"lin1",name:"Linux",browsers:["edg","esr","esr2","ff","gcr"],nav:{version:"5.0 (X11)",oscpu:"Linux x86_64",platform:"Linux x86_64"},screenOffset:-45,uaPlatform:"X11; Linux x86_64"},{id:"lin2",name:"Fedora Linux",browsers:["edg","esr","esr2","ff","gcr"],nav:{version:"5.0 (X11)",oscpu:"Linux x86_64",platform:"Linux x86_64"},screenOffset:-27,uaPlatform:"X11; Fedora; Linux x86_64"},{id:"lin3",name:"Ubuntu Linux",browsers:["edg","esr","esr2","ff","gcr"],nav:{version:"5.0 (X11)",oscpu:"Linux x86_64",platform:"Linux x86_64"},screenOffset:-27,uaPlatform:"X11; Linux x86_64"}],iOS:[{id:"ios1",name:"iOS 16",browsers:["gcrm","gcrt","sfm","sft"],uaPlatform:"16_7_10"},{id:"ios2",name:"iOS 17",browsers:["gcrm","gcrt","sfm","sft"],uaPlatform:"17_7_2"},{id:"ios3",name:"iOS 18",browsers:["gcrm","gcrt","sfm","sft"],uaPlatform:"18_3"}],android:[{id:"and1",name:"Android 11",browsers:["edgm","ffm","fft","gcrm","gcrt"],uaPlatform:"Android 11"},{id:"and2",name:"Android 12",browsers:["edgm","ffm","fft","gcrm","gcrt"],uaPlatform:"Android 12"},{id:"and3",name:"Android 13",browsers:["edgm","ffm","fft","gcrm","gcrt"],uaPlatform:"Android 13"},{id:"and4",name:"Android 14",browsers:["edgm","ffm","fft","gcrm","gcrt"],uaPlatform:"Android 14"}]},this.allProfiles={windows:[],macOS:[],linux:[],iOS:[],android:[]},this.profileIds={desktop:[],mobile:[]},this.excludedProfiles=e;for(let e of Object.keys(this.profiles))this.allProfiles[e]=this.generateProfiles(e);this.profileIds.desktop=this.profileIds.desktop.concat(this.allProfiles.windows.map(e=>e.id),this.allProfiles.macOS.map(e=>e.id),this.allProfiles.linux.map(e=>e.id)),this.profileIds.mobile=this.profileIds.mobile.concat(this.allProfiles.iOS.map(e=>e.id),this.allProfiles.android.map(e=>e.id))}getAllProfiles(){return this.allProfiles}getProfile(e){let t,a=e.split("-");return a[0].includes("win")?t="windows":a[0].includes("mac")?t="macOS":a[0].includes("lin")?t="linux":a[0].includes("ios")?t="iOS":a[0].includes("and")&&(t="android"),this.browsers[a[1]](this.profiles[t].find(e=>e.id===a[0]))}getRandomByDevice(e){let t;return t="random"===e?this.profileIds.desktop.concat(this.profileIds.mobile).filter(e=>!this.excludedProfiles.includes(e)):"randomDesktop"===e?this.profileIds.desktop.filter(e=>!this.excludedProfiles.includes(e)):this.profileIds.mobile.filter(e=>!this.excludedProfiles.includes(e)),t.length>0?t[Math.floor(Math.random()*t.length)]:"none"}getRandomByOS(e){let t;return"windows"===e?t=this.profileIds.desktop.filter(e=>e.includes("win")&&!this.excludedProfiles.includes(e)):"macOS"===e?t=this.profileIds.desktop.filter(e=>e.includes("mac")&&!this.excludedProfiles.includes(e)):"linux"===e?t=this.profileIds.desktop.filter(e=>e.includes("lin")&&!this.excludedProfiles.includes(e)):"iOS"===e?t=this.profileIds.mobile.filter(e=>e.includes("ios")&&!this.excludedProfiles.includes(e)):"android"===e&&(t=this.profileIds.mobile.filter(e=>e.includes("and")&&!this.excludedProfiles.includes(e))),t.length>0?t[Math.floor(Math.random()*t.length)]:"none"}}},6:function(e,t,a){"use strict";function n(){return[{zone:"Etc/GMT+12",offset:"-12:00"},{zone:"Etc/GMT+11",offset:"-11:00"},{zone:"Pacific/Midway",offset:"-11:00"},{zone:"Pacific/Niue",offset:"-11:00"},{zone:"Pacific/Pago_Pago",offset:"-11:00"},{zone:"Pacific/Samoa",offset:"-11:00"},{zone:"US/Samoa",offset:"-11:00"},{zone:"America/Adak",offset:"-10:00"},{zone:"America/Atka",offset:"-10:00"},{zone:"Etc/GMT+10",offset:"-10:00"},{zone:"HST",offset:"-10:00"},{zone:"Pacific/Honolulu",offset:"-10:00"},{zone:"Pacific/Johnston",offset:"-10:00"},{zone:"Pacific/Rarotonga",offset:"-10:00"},{zone:"Pacific/Tahiti",offset:"-10:00"},{zone:"US/Aleutian",offset:"-10:00"},{zone:"US/Hawaii",offset:"-10:00"},{zone:"Pacific/Marquesas",offset:"-09:30"},{zone:"America/Anchorage",offset:"-09:00"},{zone:"America/Juneau",offset:"-09:00"},{zone:"America/Metlakatla",offset:"-09:00"},{zone:"America/Nome",offset:"-09:00"},{zone:"America/Sitka",offset:"-09:00"},{zone:"America/Yakutat",offset:"-09:00"},{zone:"Etc/GMT+9",offset:"-09:00"},{zone:"Pacific/Gambier",offset:"-09:00"},{zone:"US/Alaska",offset:"-09:00"},{zone:"America/Dawson",offset:"-08:00"},{zone:"America/Ensenada",offset:"-08:00"},{zone:"America/Los_Angeles",offset:"-08:00"},{zone:"America/Santa_Isabel",offset:"-08:00"},{zone:"America/Tijuana",offset:"-08:00"},{zone:"America/Vancouver",offset:"-08:00"},{zone:"America/Whitehorse",offset:"-08:00"},{zone:"Canada/Pacific",offset:"-08:00"},{zone:"Canada/Yukon",offset:"-08:00"},{zone:"Etc/GMT+8",offset:"-08:00"},{zone:"Mexico/BajaNorte",offset:"-08:00"},{zone:"Pacific/Pitcairn",offset:"-08:00"},{zone:"US/Pacific",offset:"-08:00"},{zone:"America/Boise",offset:"-07:00"},{zone:"America/Cambridge_Bay",offset:"-07:00"},{zone:"America/Chihuahua",offset:"-07:00"},{zone:"America/Creston",offset:"-07:00"},{zone:"America/Dawson_Creek",offset:"-07:00"},{zone:"America/Denver",offset:"-07:00"},{zone:"America/Edmonton",offset:"-07:00"},{zone:"America/Fort_Nelson",offset:"-07:00"},{zone:"America/Hermosillo",offset:"-07:00"},{zone:"America/Inuvik",offset:"-07:00"},{zone:"America/Mazatlan",offset:"-07:00"},{zone:"America/Ojinaga",offset:"-07:00"},{zone:"America/Phoenix",offset:"-07:00"},{zone:"America/Shiprock",offset:"-07:00"},{zone:"America/Yellowknife",offset:"-07:00"},{zone:"Canada/Mountain",offset:"-07:00"},{zone:"Etc/GMT+7",offset:"-07:00"},{zone:"MST",offset:"-07:00"},{zone:"Mexico/BajaSur",offset:"-07:00"},{zone:"US/Arizona",offset:"-07:00"},{zone:"US/Mountain",offset:"-07:00"},{zone:"America/Bahia_Banderas",offset:"-06:00"},{zone:"America/Belize",offset:"-06:00"},{zone:"America/Chicago",offset:"-06:00"},{zone:"America/Costa_Rica",offset:"-06:00"},{zone:"America/El_Salvador",offset:"-06:00"},{zone:"America/Guatemala",offset:"-06:00"},{zone:"America/Indiana/Knox",offset:"-06:00"},{zone:"America/Indiana/Tell_City",offset:"-06:00"},{zone:"America/Knox_IN",offset:"-06:00"},{zone:"America/Managua",offset:"-06:00"},{zone:"America/Matamoros",offset:"-06:00"},{zone:"America/Menominee",offset:"-06:00"},{zone:"America/Merida",offset:"-06:00"},{zone:"America/Mexico_City",offset:"-06:00"},{zone:"America/Monterrey",offset:"-06:00"},{zone:"America/North_Dakota/Beulah",offset:"-06:00"},{zone:"America/North_Dakota/Center",offset:"-06:00"},{zone:"America/North_Dakota/New_Salem",offset:"-06:00"},{zone:"America/Rainy_River",offset:"-06:00"},{zone:"America/Rankin_Inlet",offset:"-06:00"},{zone:"America/Regina",offset:"-06:00"},{zone:"America/Resolute",offset:"-06:00"},{zone:"America/Swift_Current",offset:"-06:00"},{zone:"America/Tegucigalpa",offset:"-06:00"},{zone:"America/Winnipeg",offset:"-06:00"},{zone:"Canada/Central",offset:"-06:00"},{zone:"Canada/Saskatchewan",offset:"-06:00"},{zone:"Etc/GMT+6",offset:"-06:00"},{zone:"Mexico/General",offset:"-06:00"},{zone:"Pacific/Galapagos",offset:"-06:00"},{zone:"US/Central",offset:"-06:00"},{zone:"US/Indiana-Starke",offset:"-06:00"},{zone:"America/Atikokan",offset:"-05:00"},{zone:"America/Bogota",offset:"-05:00"},{zone:"America/Cancun",offset:"-05:00"},{zone:"America/Cayman",offset:"-05:00"},{zone:"America/Coral_Harbour",offset:"-05:00"},{zone:"America/Detroit",offset:"-05:00"},{zone:"America/Eirunepe",offset:"-05:00"},{zone:"America/Fort_Wayne",offset:"-05:00"},{zone:"America/Grand_Turk",offset:"-05:00"},{zone:"America/Guayaquil",offset:"-05:00"},{zone:"America/Havana",offset:"-05:00"},{zone:"America/Indiana/Indianapolis",offset:"-05:00"},{zone:"America/Indiana/Marengo",offset:"-05:00"},{zone:"America/Indiana/Petersburg",offset:"-05:00"},{zone:"America/Indiana/Vevay",offset:"-05:00"},{zone:"America/Indiana/Vincennes",offset:"-05:00"},{zone:"America/Indiana/Winamac",offset:"-05:00"},{zone:"America/Indianapolis",offset:"-05:00"},{zone:"America/Iqaluit",offset:"-05:00"},{zone:"America/Jamaica",offset:"-05:00"},{zone:"America/Kentucky/Louisville",offset:"-05:00"},{zone:"America/Kentucky/Monticello",offset:"-05:00"},{zone:"America/Lima",offset:"-05:00"},{zone:"America/Louisville",offset:"-05:00"},{zone:"America/Montreal",offset:"-05:00"},{zone:"America/Nassau",offset:"-05:00"},{zone:"America/New_York",offset:"-05:00"},{zone:"America/Nipigon",offset:"-05:00"},{zone:"America/Panama",offset:"-05:00"},{zone:"America/Pangnirtung",offset:"-05:00"},{zone:"America/Port-au-Prince",offset:"-05:00"},{zone:"America/Porto_Acre",offset:"-05:00"},{zone:"America/Rio_Branco",offset:"-05:00"},{zone:"America/Thunder_Bay",offset:"-05:00"},{zone:"America/Toronto",offset:"-05:00"},{zone:"Brazil/Acre",offset:"-05:00"},{zone:"Canada/Eastern",offset:"-05:00"},{zone:"Chile/EasterIsland",offset:"-05:00"},{zone:"EST",offset:"-05:00"},{zone:"Etc/GMT+5",offset:"-05:00"},{zone:"Pacific/Easter",offset:"-05:00"},{zone:"US/East-Indiana",offset:"-05:00"},{zone:"US/Eastern",offset:"-05:00"},{zone:"US/Michigan",offset:"-05:00"},{zone:"America/Anguilla",offset:"-04:00"},{zone:"America/Antigua",offset:"-04:00"},{zone:"America/Aruba",offset:"-04:00"},{zone:"America/Barbados",offset:"-04:00"},{zone:"America/Blanc-Sablon",offset:"-04:00"},{zone:"America/Boa_Vista",offset:"-04:00"},{zone:"America/Campo_Grande",offset:"-04:00"},{zone:"America/Caracas",offset:"-04:00"},{zone:"America/Cuiaba",offset:"-04:00"},{zone:"America/Curacao",offset:"-04:00"},{zone:"America/Dominica",offset:"-04:00"},{zone:"America/Glace_Bay",offset:"-04:00"},{zone:"America/Goose_Bay",offset:"-04:00"},{zone:"America/Grenada",offset:"-04:00"},{zone:"America/Guadeloupe",offset:"-04:00"},{zone:"America/Guyana",offset:"-04:00"},{zone:"America/Halifax",offset:"-04:00"},{zone:"America/Kralendijk",offset:"-04:00"},{zone:"America/La_Paz",offset:"-04:00"},{zone:"America/Lower_Princes",offset:"-04:00"},{zone:"America/Manaus",offset:"-04:00"},{zone:"America/Marigot",offset:"-04:00"},{zone:"America/Martinique",offset:"-04:00"},{zone:"America/Moncton",offset:"-04:00"},{zone:"America/Montserrat",offset:"-04:00"},{zone:"America/Port_of_Spain",offset:"-04:00"},{zone:"America/Porto_Velho",offset:"-04:00"},{zone:"America/Puerto_Rico",offset:"-04:00"},{zone:"America/Santo_Domingo",offset:"-04:00"},{zone:"America/St_Barthelemy",offset:"-04:00"},{zone:"America/St_Kitts",offset:"-04:00"},{zone:"America/St_Lucia",offset:"-04:00"},{zone:"America/St_Thomas",offset:"-04:00"},{zone:"America/St_Vincent",offset:"-04:00"},{zone:"America/Thule",offset:"-04:00"},{zone:"America/Tortola",offset:"-04:00"},{zone:"America/Virgin",offset:"-04:00"},{zone:"Atlantic/Bermuda",offset:"-04:00"},{zone:"Brazil/West",offset:"-04:00"},{zone:"Canada/Atlantic",offset:"-04:00"},{zone:"Etc/GMT+4",offset:"-04:00"},{zone:"America/St_Johns",offset:"-03:30"},{zone:"Canada/Newfoundland",offset:"-03:30"},{zone:"America/Araguaina",offset:"-03:00"},{zone:"America/Argentina/Buenos_Aires",offset:"-03:00"},{zone:"America/Argentina/Catamarca",offset:"-03:00"},{zone:"America/Argentina/ComodRivadavia",offset:"-03:00"},{zone:"America/Argentina/Cordoba",offset:"-03:00"},{zone:"America/Argentina/Jujuy",offset:"-03:00"},{zone:"America/Argentina/La_Rioja",offset:"-03:00"},{zone:"America/Argentina/Mendoza",offset:"-03:00"},{zone:"America/Argentina/Rio_Gallegos",offset:"-03:00"},{zone:"America/Argentina/Salta",offset:"-03:00"},{zone:"America/Argentina/San_Juan",offset:"-03:00"},{zone:"America/Argentina/San_Luis",offset:"-03:00"},{zone:"America/Argentina/Tucuman",offset:"-03:00"},{zone:"America/Argentina/Ushuaia",offset:"-03:00"},{zone:"America/Asuncion",offset:"-03:00"},{zone:"America/Bahia",offset:"-03:00"},{zone:"America/Belem",offset:"-03:00"},{zone:"America/Buenos_Aires",offset:"-03:00"},{zone:"America/Catamarca",offset:"-03:00"},{zone:"America/Cayenne",offset:"-03:00"},{zone:"America/Cordoba",offset:"-03:00"},{zone:"America/Fortaleza",offset:"-03:00"},{zone:"America/Godthab",offset:"-03:00"},{zone:"America/Jujuy",offset:"-03:00"},{zone:"America/Maceio",offset:"-03:00"},{zone:"America/Mendoza",offset:"-03:00"},{zone:"America/Miquelon",offset:"-03:00"},{zone:"America/Montevideo",offset:"-03:00"},{zone:"America/Paramaribo",offset:"-03:00"},{zone:"America/Punta_Arenas",offset:"-03:00"},{zone:"America/Recife",offset:"-03:00"},{zone:"America/Rosario",offset:"-03:00"},{zone:"America/Santarem",offset:"-03:00"},{zone:"America/Santiago",offset:"-03:00"},{zone:"America/Sao_Paulo",offset:"-03:00"},{zone:"Antarctica/Palmer",offset:"-03:00"},{zone:"Antarctica/Rothera",offset:"-03:00"},{zone:"Atlantic/Stanley",offset:"-03:00"},{zone:"Brazil/East",offset:"-03:00"},{zone:"Chile/Continental",offset:"-03:00"},{zone:"Etc/GMT+3",offset:"-03:00"},{zone:"America/Noronha",offset:"-02:00"},{zone:"Atlantic/South_Georgia",offset:"-02:00"},{zone:"Brazil/DeNoronha",offset:"-02:00"},{zone:"Etc/GMT+2",offset:"-02:00"},{zone:"America/Scoresbysund",offset:"-01:00"},{zone:"Atlantic/Azores",offset:"-01:00"},{zone:"Atlantic/Cape_Verde",offset:"-01:00"},{zone:"Etc/GMT+1",offset:"-01:00"},{zone:"Africa/Abidjan",offset:"0:00"},{zone:"Africa/Accra",offset:"0:00"},{zone:"Africa/Bamako",offset:"0:00"},{zone:"Africa/Banjul",offset:"0:00"},{zone:"Africa/Bissau",offset:"0:00"},{zone:"Africa/Conakry",offset:"0:00"},{zone:"Africa/Dakar",offset:"0:00"},{zone:"Africa/Freetown",offset:"0:00"},{zone:"Africa/Lome",offset:"0:00"},{zone:"Africa/Monrovia",offset:"0:00"},{zone:"Africa/Nouakchott",offset:"0:00"},{zone:"Africa/Ouagadougou",offset:"0:00"},{zone:"Africa/Sao_Tome",offset:"0:00"},{zone:"Africa/Timbuktu",offset:"0:00"},{zone:"America/Danmarkshavn",offset:"0:00"},{zone:"Antarctica/Troll",offset:"0:00"},{zone:"Atlantic/Canary",offset:"0:00"},{zone:"Atlantic/Faeroe",offset:"0:00"},{zone:"Atlantic/Faroe",offset:"0:00"},{zone:"Atlantic/Madeira",offset:"0:00"},{zone:"Atlantic/Reykjavik",offset:"0:00"},{zone:"Atlantic/St_Helena",offset:"0:00"},{zone:"Etc/GMT",offset:"0:00"},{zone:"Etc/Greenwich",offset:"0:00"},{zone:"Etc/UCT",offset:"0:00"},{zone:"Etc/UTC",offset:"0:00"},{zone:"Etc/Universal",offset:"0:00"},{zone:"Etc/Zulu",offset:"0:00"},{zone:"Europe/Belfast",offset:"0:00"},{zone:"Europe/Dublin",offset:"0:00"},{zone:"Europe/Guernsey",offset:"0:00"},{zone:"Europe/Isle_of_Man",offset:"0:00"},{zone:"Europe/Jersey",offset:"0:00"},{zone:"Europe/Lisbon",offset:"0:00"},{zone:"Europe/London",offset:"0:00"},{zone:"GMT",offset:"0:00"},{zone:"WET",offset:"0:00"},{zone:"Africa/Algiers",offset:"+01:00"},{zone:"Africa/Bangui",offset:"+01:00"},{zone:"Africa/Brazzaville",offset:"+01:00"},{zone:"Africa/Casablanca",offset:"+01:00"},{zone:"Africa/Ceuta",offset:"+01:00"},{zone:"Africa/Douala",offset:"+01:00"},{zone:"Africa/El_Aaiun",offset:"+01:00"},{zone:"Africa/Kinshasa",offset:"+01:00"},{zone:"Africa/Lagos",offset:"+01:00"},{zone:"Africa/Libreville",offset:"+01:00"},{zone:"Africa/Luanda",offset:"+01:00"},{zone:"Africa/Malabo",offset:"+01:00"},{zone:"Africa/Ndjamena",offset:"+01:00"},{zone:"Africa/Niamey",offset:"+01:00"},{zone:"Africa/Porto-Novo",offset:"+01:00"},{zone:"Africa/Tunis",offset:"+01:00"},{zone:"Arctic/Longyearbyen",offset:"+01:00"},{zone:"Atlantic/Jan_Mayen",offset:"+01:00"},{zone:"CET",offset:"+01:00"},{zone:"Etc/GMT-1",offset:"+01:00"},{zone:"Europe/Amsterdam",offset:"+01:00"},{zone:"Europe/Andorra",offset:"+01:00"},{zone:"Europe/Belgrade",offset:"+01:00"},{zone:"Europe/Berlin",offset:"+01:00"},{zone:"Europe/Bratislava",offset:"+01:00"},{zone:"Europe/Brussels",offset:"+01:00"},{zone:"Europe/Budapest",offset:"+01:00"},{zone:"Europe/Busingen",offset:"+01:00"},{zone:"Europe/Copenhagen",offset:"+01:00"},{zone:"Europe/Gibraltar",offset:"+01:00"},{zone:"Europe/Ljubljana",offset:"+01:00"},{zone:"Europe/Luxembourg",offset:"+01:00"},{zone:"Europe/Madrid",offset:"+01:00"},{zone:"Europe/Malta",offset:"+01:00"},{zone:"Europe/Monaco",offset:"+01:00"},{zone:"Europe/Oslo",offset:"+01:00"},{zone:"Europe/Paris",offset:"+01:00"},{zone:"Europe/Podgorica",offset:"+01:00"},{zone:"Europe/Prague",offset:"+01:00"},{zone:"Europe/Rome",offset:"+01:00"},{zone:"Europe/San_Marino",offset:"+01:00"},{zone:"Europe/Sarajevo",offset:"+01:00"},{zone:"Europe/Skopje",offset:"+01:00"},{zone:"Europe/Stockholm",offset:"+01:00"},{zone:"Europe/Tirane",offset:"+01:00"},{zone:"Europe/Vaduz",offset:"+01:00"},{zone:"Europe/Vatican",offset:"+01:00"},{zone:"Europe/Vienna",offset:"+01:00"},{zone:"Europe/Warsaw",offset:"+01:00"},{zone:"Europe/Zagreb",offset:"+01:00"},{zone:"Europe/Zurich",offset:"+01:00"},{zone:"MET",offset:"+01:00"},{zone:"Africa/Blantyre",offset:"+02:00"},{zone:"Africa/Bujumbura",offset:"+02:00"},{zone:"Africa/Cairo",offset:"+02:00"},{zone:"Africa/Gaborone",offset:"+02:00"},{zone:"Africa/Harare",offset:"+02:00"},{zone:"Africa/Johannesburg",offset:"+02:00"},{zone:"Africa/Khartoum",offset:"+02:00"},{zone:"Africa/Kigali",offset:"+02:00"},{zone:"Africa/Lubumbashi",offset:"+02:00"},{zone:"Africa/Lusaka",offset:"+02:00"},{zone:"Africa/Maputo",offset:"+02:00"},{zone:"Africa/Maseru",offset:"+02:00"},{zone:"Africa/Mbabane",offset:"+02:00"},{zone:"Africa/Tripoli",offset:"+02:00"},{zone:"Africa/Windhoek",offset:"+02:00"},{zone:"Asia/Amman",offset:"+02:00"},{zone:"Asia/Beirut",offset:"+02:00"},{zone:"Asia/Damascus",offset:"+02:00"},{zone:"Asia/Famagusta",offset:"+02:00"},{zone:"Asia/Gaza",offset:"+02:00"},{zone:"Asia/Hebron",offset:"+02:00"},{zone:"Asia/Jerusalem",offset:"+02:00"},{zone:"Asia/Nicosia",offset:"+02:00"},{zone:"Asia/Tel_Aviv",offset:"+02:00"},{zone:"EET",offset:"+02:00"},{zone:"Etc/GMT-2",offset:"+02:00"},{zone:"Europe/Athens",offset:"+02:00"},{zone:"Europe/Bucharest",offset:"+02:00"},{zone:"Europe/Chisinau",offset:"+02:00"},{zone:"Europe/Helsinki",offset:"+02:00"},{zone:"Europe/Kaliningrad",offset:"+02:00"},{zone:"Europe/Kiev",offset:"+02:00"},{zone:"Europe/Mariehamn",offset:"+02:00"},{zone:"Europe/Nicosia",offset:"+02:00"},{zone:"Europe/Riga",offset:"+02:00"},{zone:"Europe/Sofia",offset:"+02:00"},{zone:"Europe/Tallinn",offset:"+02:00"},{zone:"Europe/Tiraspol",offset:"+02:00"},{zone:"Europe/Uzhgorod",offset:"+02:00"},{zone:"Europe/Vilnius",offset:"+02:00"},{zone:"Europe/Zaporozhye",offset:"+02:00"},{zone:"Africa/Addis_Ababa",offset:"+03:00"},{zone:"Africa/Asmara",offset:"+03:00"},{zone:"Africa/Asmera",offset:"+03:00"},{zone:"Africa/Dar_es_Salaam",offset:"+03:00"},{zone:"Africa/Djibouti",offset:"+03:00"},{zone:"Africa/Juba",offset:"+03:00"},{zone:"Africa/Kampala",offset:"+03:00"},{zone:"Africa/Mogadishu",offset:"+03:00"},{zone:"Africa/Nairobi",offset:"+03:00"},{zone:"Antarctica/Syowa",offset:"+03:00"},{zone:"Asia/Aden",offset:"+03:00"},{zone:"Asia/Baghdad",offset:"+03:00"},{zone:"Asia/Bahrain",offset:"+03:00"},{zone:"Asia/Istanbul",offset:"+03:00"},{zone:"Asia/Kuwait",offset:"+03:00"},{zone:"Asia/Qatar",offset:"+03:00"},{zone:"Asia/Riyadh",offset:"+03:00"},{zone:"Etc/GMT-3",offset:"+03:00"},{zone:"Europe/Istanbul",offset:"+03:00"},{zone:"Europe/Kirov",offset:"+03:00"},{zone:"Europe/Minsk",offset:"+03:00"},{zone:"Europe/Moscow",offset:"+03:00"},{zone:"Europe/Simferopol",offset:"+03:00"},{zone:"Indian/Antananarivo",offset:"+03:00"},{zone:"Indian/Comoro",offset:"+03:00"},{zone:"Indian/Mayotte",offset:"+03:00"},{zone:"Asia/Tehran",offset:"+03:30"},{zone:"Asia/Baku",offset:"+04:00"},{zone:"Asia/Dubai",offset:"+04:00"},{zone:"Asia/Muscat",offset:"+04:00"},{zone:"Asia/Tbilisi",offset:"+04:00"},{zone:"Asia/Yerevan",offset:"+04:00"},{zone:"Etc/GMT-4",offset:"+04:00"},{zone:"Europe/Astrakhan",offset:"+04:00"},{zone:"Europe/Samara",offset:"+04:00"},{zone:"Europe/Saratov",offset:"+04:00"},{zone:"Europe/Ulyanovsk",offset:"+04:00"},{zone:"Europe/Volgograd",offset:"+04:00"},{zone:"Indian/Mahe",offset:"+04:00"},{zone:"Indian/Mauritius",offset:"+04:00"},{zone:"Indian/Reunion",offset:"+04:00"},{zone:"Asia/Kabul",offset:"+04:30"},{zone:"Antarctica/Mawson",offset:"+05:00"},{zone:"Asia/Aqtau",offset:"+05:00"},{zone:"Asia/Aqtobe",offset:"+05:00"},{zone:"Asia/Ashgabat",offset:"+05:00"},{zone:"Asia/Ashkhabad",offset:"+05:00"},{zone:"Asia/Atyrau",offset:"+05:00"},{zone:"Asia/Dushanbe",offset:"+05:00"},{zone:"Asia/Karachi",offset:"+05:00"},{zone:"Asia/Oral",offset:"+05:00"},{zone:"Asia/Qyzylorda",offset:"+05:00"},{zone:"Asia/Samarkand",offset:"+05:00"},{zone:"Asia/Tashkent",offset:"+05:00"},{zone:"Asia/Yekaterinburg",offset:"+05:00"},{zone:"Etc/GMT-5",offset:"+05:00"},{zone:"Indian/Kerguelen",offset:"+05:00"},{zone:"Indian/Maldives",offset:"+05:00"},{zone:"Asia/Calcutta",offset:"+05:30"},{zone:"Asia/Colombo",offset:"+05:30"},{zone:"Asia/Kolkata",offset:"+05:30"},{zone:"Asia/Kathmandu",offset:"+05:45"},{zone:"Asia/Katmandu",offset:"+05:45"},{zone:"Antarctica/Vostok",offset:"+06:00"},{zone:"Asia/Almaty",offset:"+06:00"},{zone:"Asia/Bishkek",offset:"+06:00"},{zone:"Asia/Dacca",offset:"+06:00"},{zone:"Asia/Dhaka",offset:"+06:00"},{zone:"Asia/Kashgar",offset:"+06:00"},{zone:"Asia/Omsk",offset:"+06:00"},{zone:"Asia/Qostanay",offset:"+06:00"},{zone:"Asia/Thimbu",offset:"+06:00"},{zone:"Asia/Thimphu",offset:"+06:00"},{zone:"Asia/Urumqi",offset:"+06:00"},{zone:"Etc/GMT-6",offset:"+06:00"},{zone:"Indian/Chagos",offset:"+06:00"},{zone:"Asia/Rangoon",offset:"+06:30"},{zone:"Asia/Yangon",offset:"+06:30"},{zone:"Indian/Cocos",offset:"+06:30"},{zone:"Antarctica/Davis",offset:"+07:00"},{zone:"Asia/Bangkok",offset:"+07:00"},{zone:"Asia/Barnaul",offset:"+07:00"},{zone:"Asia/Ho_Chi_Minh",offset:"+07:00"},{zone:"Asia/Hovd",offset:"+07:00"},{zone:"Asia/Jakarta",offset:"+07:00"},{zone:"Asia/Krasnoyarsk",offset:"+07:00"},{zone:"Asia/Novokuznetsk",offset:"+07:00"},{zone:"Asia/Novosibirsk",offset:"+07:00"},{zone:"Asia/Phnom_Penh",offset:"+07:00"},{zone:"Asia/Pontianak",offset:"+07:00"},{zone:"Asia/Saigon",offset:"+07:00"},{zone:"Asia/Tomsk",offset:"+07:00"},{zone:"Asia/Vientiane",offset:"+07:00"},{zone:"Etc/GMT-7",offset:"+07:00"},{zone:"Indian/Christmas",offset:"+07:00"},{zone:"Antarctica/Casey",offset:"+08:00"},{zone:"Asia/Brunei",offset:"+08:00"},{zone:"Asia/Choibalsan",offset:"+08:00"},{zone:"Asia/Chongqing",offset:"+08:00"},{zone:"Asia/Chungking",offset:"+08:00"},{zone:"Asia/Harbin",offset:"+08:00"},{zone:"Asia/Hong_Kong",offset:"+08:00"},{zone:"Asia/Irkutsk",offset:"+08:00"},{zone:"Asia/Kuala_Lumpur",offset:"+08:00"},{zone:"Asia/Kuching",offset:"+08:00"},{zone:"Asia/Macao",offset:"+08:00"},{zone:"Asia/Macau",offset:"+08:00"},{zone:"Asia/Makassar",offset:"+08:00"},{zone:"Asia/Manila",offset:"+08:00"},{zone:"Asia/Shanghai",offset:"+08:00"},{zone:"Asia/Singapore",offset:"+08:00"},{zone:"Asia/Taipei",offset:"+08:00"},{zone:"Asia/Ujung_Pandang",offset:"+08:00"},{zone:"Asia/Ulaanbaatar",offset:"+08:00"},{zone:"Asia/Ulan_Bator",offset:"+08:00"},{zone:"Australia/Perth",offset:"+08:00"},{zone:"Australia/West",offset:"+08:00"},{zone:"Etc/GMT-8",offset:"+08:00"},{zone:"Australia/Eucla",offset:"+08:45"},{zone:"Asia/Chita",offset:"+09:00"},{zone:"Asia/Dili",offset:"+09:00"},{zone:"Asia/Jayapura",offset:"+09:00"},{zone:"Asia/Khandyga",offset:"+09:00"},{zone:"Asia/Pyongyang",offset:"+09:00"},{zone:"Asia/Seoul",offset:"+09:00"},{zone:"Asia/Tokyo",offset:"+09:00"},{zone:"Asia/Yakutsk",offset:"+09:00"},{zone:"Etc/GMT-9",offset:"+09:00"},{zone:"Pacific/Palau",offset:"+09:00"},{zone:"Australia/Darwin",offset:"+09:30"},{zone:"Australia/North",offset:"+09:30"},{zone:"Antarctica/DumontDUrville",offset:"+10:00"},{zone:"Asia/Ust-Nera",offset:"+10:00"},{zone:"Asia/Vladivostok",offset:"+10:00"},{zone:"Australia/Brisbane",offset:"+10:00"},{zone:"Australia/Lindeman",offset:"+10:00"},{zone:"Australia/Queensland",offset:"+10:00"},{zone:"Etc/GMT-10",offset:"+10:00"},{zone:"Pacific/Chuuk",offset:"+10:00"},{zone:"Pacific/Guam",offset:"+10:00"},{zone:"Pacific/Port_Moresby",offset:"+10:00"},{zone:"Pacific/Saipan",offset:"+10:00"},{zone:"Pacific/Truk",offset:"+10:00"},{zone:"Pacific/Yap",offset:"+10:00"},{zone:"Australia/Adelaide",offset:"+10:30"},{zone:"Australia/Broken_Hill",offset:"+10:30"},{zone:"Australia/South",offset:"+10:30"},{zone:"Australia/Yancowinna",offset:"+10:30"},{zone:"Antarctica/Macquarie",offset:"+11:00"},{zone:"Asia/Magadan",offset:"+11:00"},{zone:"Asia/Sakhalin",offset:"+11:00"},{zone:"Asia/Srednekolymsk",offset:"+11:00"},{zone:"Australia/ACT",offset:"+11:00"},{zone:"Australia/Canberra",offset:"+11:00"},{zone:"Australia/Currie",offset:"+11:00"},{zone:"Australia/Hobart",offset:"+11:00"},{zone:"Australia/LHI",offset:"+11:00"},{zone:"Australia/Lord_Howe",offset:"+11:00"},{zone:"Australia/Melbourne",offset:"+11:00"},{zone:"Australia/NSW",offset:"+11:00"},{zone:"Australia/Sydney",offset:"+11:00"},{zone:"Australia/Tasmania",offset:"+11:00"},{zone:"Australia/Victoria",offset:"+11:00"},{zone:"Etc/GMT-11",offset:"+11:00"},{zone:"Pacific/Bougainville",offset:"+11:00"},{zone:"Pacific/Efate",offset:"+11:00"},{zone:"Pacific/Guadalcanal",offset:"+11:00"},{zone:"Pacific/Kosrae",offset:"+11:00"},{zone:"Pacific/Norfolk",offset:"+11:00"},{zone:"Pacific/Noumea",offset:"+11:00"},{zone:"Pacific/Pohnpei",offset:"+11:00"},{zone:"Pacific/Ponape",offset:"+11:00"},{zone:"Asia/Anadyr",offset:"+12:00"},{zone:"Asia/Kamchatka",offset:"+12:00"},{zone:"Etc/GMT-12",offset:"+12:00"},{zone:"Pacific/Fiji",offset:"+12:00"},{zone:"Pacific/Funafuti",offset:"+12:00"},{zone:"Pacific/Kwajalein",offset:"+12:00"},{zone:"Pacific/Majuro",offset:"+12:00"},{zone:"Pacific/Nauru",offset:"+12:00"},{zone:"Pacific/Tarawa",offset:"+12:00"},{zone:"Pacific/Wake",offset:"+12:00"},{zone:"Pacific/Wallis",offset:"+12:00"},{zone:"Antarctica/McMurdo",offset:"+13:00"},{zone:"Antarctica/South_Pole",offset:"+13:00"},{zone:"Etc/GMT-13",offset:"+13:00"},{zone:"Pacific/Auckland",offset:"+13:00"},{zone:"Pacific/Enderbury",offset:"+13:00"},{zone:"Pacific/Fakaofo",offset:"+13:00"},{zone:"Pacific/Tongatapu",offset:"+13:00"},{zone:"Pacific/Chatham",offset:"+13:45"},{zone:"Etc/GMT-14",offset:"+14:00"},{zone:"Pacific/Apia",offset:"+14:00"},{zone:"Pacific/Kiritimati",offset:"+14:00"}]}a.d(t,"a",(function(){return n}))},7:function(e,t,a){"use strict";var n={};a.r(n),a.d(n,"changeProfile",(function(){return L})),a.d(n,"changeSetting",(function(){return N})),a.d(n,"excludeProfile",(function(){return R})),a.d(n,"initialize",(function(){return q})),a.d(n,"toggleChameleon",(function(){return B})),a.d(n,"toggleNotifications",(function(){return U})),a.d(n,"toggleTheme",(function(){return $}));var o=a(1);
/*!
 * vuex v3.6.2
 * (c) 2021 Evan You
 * @license MIT
 */var i=("undefined"!=typeof window||"undefined"!=typeof window?window:{}).__VUE_DEVTOOLS_GLOBAL_HOOK__;function r(e,t){if(void 0===t&&(t=[]),null===e||"object"!=typeof e)return e;var a,n=(a=function(t){return t.original===e},t.filter(a)[0]);if(n)return n.copy;var o=Array.isArray(e)?[]:{};return t.push({original:e,copy:o}),Object.keys(e).forEach((function(a){o[a]=r(e[a],t)})),o}function s(e,t){Object.keys(e).forEach((function(a){return t(e[a],a)}))}function u(e){return null!==e&&"object"==typeof e}var c=function(e,t){this.runtime=t,this._children=Object.create(null),this._rawModule=e;var a=e.state;this.state=("function"==typeof a?a():a)||{}},l={namespaced:{configurable:!0}};l.namespaced.get=function(){return!!this._rawModule.namespaced},c.prototype.addChild=function(e,t){this._children[e]=t},c.prototype.removeChild=function(e){delete this._children[e]},c.prototype.getChild=function(e){return this._children[e]},c.prototype.hasChild=function(e){return e in this._children},c.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},c.prototype.forEachChild=function(e){s(this._children,e)},c.prototype.forEachGetter=function(e){this._rawModule.getters&&s(this._rawModule.getters,e)},c.prototype.forEachAction=function(e){this._rawModule.actions&&s(this._rawModule.actions,e)},c.prototype.forEachMutation=function(e){this._rawModule.mutations&&s(this._rawModule.mutations,e)},Object.defineProperties(c.prototype,l);var p=function(e){this.register([],e,!1)};p.prototype.get=function(e){return e.reduce((function(e,t){return e.getChild(t)}),this.root)},p.prototype.getNamespace=function(e){var t=this.root;return e.reduce((function(e,a){return e+((t=t.getChild(a)).namespaced?a+"/":"")}),"")},p.prototype.update=function(e){!function e(t,a,n){0;if(a.update(n),n.modules)for(var o in n.modules){if(!a.getChild(o))return void 0;e(t.concat(o),a.getChild(o),n.modules[o])}}([],this.root,e)},p.prototype.register=function(e,t,a){var n=this;void 0===a&&(a=!0);var o=new c(t,a);0===e.length?this.root=o:this.get(e.slice(0,-1)).addChild(e[e.length-1],o);t.modules&&s(t.modules,(function(t,o){n.register(e.concat(o),t,a)}))},p.prototype.unregister=function(e){var t=this.get(e.slice(0,-1)),a=e[e.length-1],n=t.getChild(a);n&&n.runtime&&t.removeChild(a)},p.prototype.isRegistered=function(e){var t=this.get(e.slice(0,-1)),a=e[e.length-1];return!!t&&t.hasChild(a)};var m;var f=function(e){var t=this;void 0===e&&(e={}),!m&&"undefined"!=typeof window&&window.Vue&&j(window.Vue);var a=e.plugins;void 0===a&&(a=[]);var n=e.strict;void 0===n&&(n=!1),this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new p(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._watcherVM=new m,this._makeLocalGettersCache=Object.create(null);var o=this,r=this.dispatch,s=this.commit;this.dispatch=function(e,t){return r.call(o,e,t)},this.commit=function(e,t,a){return s.call(o,e,t,a)},this.strict=n;var u=this._modules.root.state;k(this,u,[],this._modules.root),b(this,u),a.forEach((function(e){return e(t)})),(void 0!==e.devtools?e.devtools:m.config.devtools)&&function(e){i&&(e._devtoolHook=i,i.emit("vuex:init",e),i.on("vuex:travel-to-state",(function(t){e.replaceState(t)})),e.subscribe((function(e,t){i.emit("vuex:mutation",e,t)}),{prepend:!0}),e.subscribeAction((function(e,t){i.emit("vuex:action",e,t)}),{prepend:!0}))}(this)},h={state:{configurable:!0}};function d(e,t,a){return t.indexOf(e)<0&&(a&&a.prepend?t.unshift(e):t.push(e)),function(){var a=t.indexOf(e);a>-1&&t.splice(a,1)}}function g(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var a=e.state;k(e,a,[],e._modules.root,!0),b(e,a,t)}function b(e,t,a){var n=e._vm;e.getters={},e._makeLocalGettersCache=Object.create(null);var o=e._wrappedGetters,i={};s(o,(function(t,a){i[a]=function(e,t){return function(){return e(t)}}(t,e),Object.defineProperty(e.getters,a,{get:function(){return e._vm[a]},enumerable:!0})}));var r=m.config.silent;m.config.silent=!0,e._vm=new m({data:{$$state:t},computed:i}),m.config.silent=r,e.strict&&function(e){e._vm.$watch((function(){return this._data.$$state}),(function(){0}),{deep:!0,sync:!0})}(e),n&&(a&&e._withCommit((function(){n._data.$$state=null})),m.nextTick((function(){return n.$destroy()})))}function k(e,t,a,n,o){var i=!a.length,r=e._modules.getNamespace(a);if(n.namespaced&&(e._modulesNamespaceMap[r],e._modulesNamespaceMap[r]=n),!i&&!o){var s=v(t,a.slice(0,-1)),u=a[a.length-1];e._withCommit((function(){m.set(s,u,n.state)}))}var c=n.context=function(e,t,a){var n=""===t,o={dispatch:n?e.dispatch:function(a,n,o){var i=y(a,n,o),r=i.payload,s=i.options,u=i.type;return s&&s.root||(u=t+u),e.dispatch(u,r)},commit:n?e.commit:function(a,n,o){var i=y(a,n,o),r=i.payload,s=i.options,u=i.type;s&&s.root||(u=t+u),e.commit(u,r,s)}};return Object.defineProperties(o,{getters:{get:n?function(){return e.getters}:function(){return function(e,t){if(!e._makeLocalGettersCache[t]){var a={},n=t.length;Object.keys(e.getters).forEach((function(o){if(o.slice(0,n)===t){var i=o.slice(n);Object.defineProperty(a,i,{get:function(){return e.getters[o]},enumerable:!0})}})),e._makeLocalGettersCache[t]=a}return e._makeLocalGettersCache[t]}(e,t)}},state:{get:function(){return v(e.state,a)}}}),o}(e,r,a);n.forEachMutation((function(t,a){!function(e,t,a,n){(e._mutations[t]||(e._mutations[t]=[])).push((function(t){a.call(e,n.state,t)}))}(e,r+a,t,c)})),n.forEachAction((function(t,a){var n=t.root?a:r+a,o=t.handler||t;!function(e,t,a,n){(e._actions[t]||(e._actions[t]=[])).push((function(t){var o,i=a.call(e,{dispatch:n.dispatch,commit:n.commit,getters:n.getters,state:n.state,rootGetters:e.getters,rootState:e.state},t);return(o=i)&&"function"==typeof o.then||(i=Promise.resolve(i)),e._devtoolHook?i.catch((function(t){throw e._devtoolHook.emit("vuex:error",t),t})):i}))}(e,n,o,c)})),n.forEachGetter((function(t,a){!function(e,t,a,n){if(e._wrappedGetters[t])return void 0;e._wrappedGetters[t]=function(e){return a(n.state,n.getters,e.state,e.getters)}}(e,r+a,t,c)})),n.forEachChild((function(n,i){k(e,t,a.concat(i),n,o)}))}function v(e,t){return t.reduce((function(e,t){return e[t]}),e)}function y(e,t,a){return u(e)&&e.type&&(a=t,t=e,e=e.type),{type:e,payload:t,options:a}}function j(e){m&&e===m||function(e){if(Number(e.version.split(".")[0])>=2)e.mixin({beforeCreate:a});else{var t=e.prototype._init;e.prototype._init=function(e){void 0===e&&(e={}),e.init=e.init?[a].concat(e.init):a,t.call(this,e)}}function a(){var e=this.$options;e.store?this.$store="function"==typeof e.store?e.store():e.store:e.parent&&e.parent.$store&&(this.$store=e.parent.$store)}}(m=e)}h.state.get=function(){return this._vm._data.$$state},h.state.set=function(e){0},f.prototype.commit=function(e,t,a){var n=this,o=y(e,t,a),i=o.type,r=o.payload,s=(o.options,{type:i,payload:r}),u=this._mutations[i];u&&(this._withCommit((function(){u.forEach((function(e){e(r)}))})),this._subscribers.slice().forEach((function(e){return e(s,n.state)})))},f.prototype.dispatch=function(e,t){var a=this,n=y(e,t),o=n.type,i=n.payload,r={type:o,payload:i},s=this._actions[o];if(s){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(r,a.state)}))}catch(e){0}var u=s.length>1?Promise.all(s.map((function(e){return e(i)}))):s[0](i);return new Promise((function(e,t){u.then((function(t){try{a._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(r,a.state)}))}catch(e){0}e(t)}),(function(e){try{a._actionSubscribers.filter((function(e){return e.error})).forEach((function(t){return t.error(r,a.state,e)}))}catch(e){0}t(e)}))}))}},f.prototype.subscribe=function(e,t){return d(e,this._subscribers,t)},f.prototype.subscribeAction=function(e,t){return d("function"==typeof e?{before:e}:e,this._actionSubscribers,t)},f.prototype.watch=function(e,t,a){var n=this;return this._watcherVM.$watch((function(){return e(n.state,n.getters)}),t,a)},f.prototype.replaceState=function(e){var t=this;this._withCommit((function(){t._vm._data.$$state=e}))},f.prototype.registerModule=function(e,t,a){void 0===a&&(a={}),"string"==typeof e&&(e=[e]),this._modules.register(e,t),k(this,this.state,e,this._modules.get(e),a.preserveState),b(this,this.state)},f.prototype.unregisterModule=function(e){var t=this;"string"==typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit((function(){var a=v(t.state,e.slice(0,-1));m.delete(a,e[e.length-1])})),g(this)},f.prototype.hasModule=function(e){return"string"==typeof e&&(e=[e]),this._modules.isRegistered(e)},f.prototype.hotUpdate=function(e){this._modules.update(e),g(this,!0)},f.prototype._withCommit=function(e){var t=this._committing;this._committing=!0,e(),this._committing=t},Object.defineProperties(f.prototype,h);var w=S((function(e,t){var a={};return z(t).forEach((function(t){var n=t.key,o=t.val;a[n]=function(){var t=this.$store.state,a=this.$store.getters;if(e){var n=C(this.$store,"mapState",e);if(!n)return;t=n.context.state,a=n.context.getters}return"function"==typeof o?o.call(this,t,a):t[o]},a[n].vuex=!0})),a})),_=S((function(e,t){var a={};return z(t).forEach((function(t){var n=t.key,o=t.val;a[n]=function(){for(var t=[],a=arguments.length;a--;)t[a]=arguments[a];var n=this.$store.commit;if(e){var i=C(this.$store,"mapMutations",e);if(!i)return;n=i.context.commit}return"function"==typeof o?o.apply(this,[n].concat(t)):n.apply(this.$store,[o].concat(t))}})),a})),A=S((function(e,t){var a={};return z(t).forEach((function(t){var n=t.key,o=t.val;o=e+o,a[n]=function(){if(!e||C(this.$store,"mapGetters",e))return this.$store.getters[o]},a[n].vuex=!0})),a})),x=S((function(e,t){var a={};return z(t).forEach((function(t){var n=t.key,o=t.val;a[n]=function(){for(var t=[],a=arguments.length;a--;)t[a]=arguments[a];var n=this.$store.dispatch;if(e){var i=C(this.$store,"mapActions",e);if(!i)return;n=i.context.dispatch}return"function"==typeof o?o.apply(this,[n].concat(t)):n.apply(this.$store,[o].concat(t))}})),a}));function z(e){return function(e){return Array.isArray(e)||u(e)}(e)?Array.isArray(e)?e.map((function(e){return{key:e,val:e}})):Object.keys(e).map((function(t){return{key:t,val:e[t]}})):[]}function S(e){return function(t,a){return"string"!=typeof t?(a=t,t=""):"/"!==t.charAt(t.length-1)&&(t+="/"),e(t,a)}}function C(e,t,a){return e._modulesNamespaceMap[a]}function E(e,t,a){var n=a?e.groupCollapsed:e.group;try{n.call(e,t)}catch(a){e.log(t)}}function T(e){try{e.groupEnd()}catch(t){e.log("—— log end ——")}}function I(){var e=new Date;return" @ "+P(e.getHours(),2)+":"+P(e.getMinutes(),2)+":"+P(e.getSeconds(),2)+"."+P(e.getMilliseconds(),3)}function P(e,t){return a="0",n=t-e.toString().length,new Array(n+1).join(a)+e;var a,n}var M={Store:f,install:j,version:"3.6.2",mapState:w,mapMutations:_,mapGetters:A,mapActions:x,createNamespacedHelpers:function(e){return{mapState:w.bind(null,e),mapGetters:A.bind(null,e),mapMutations:_.bind(null,e),mapActions:x.bind(null,e)}},createLogger:function(e){void 0===e&&(e={});var t=e.collapsed;void 0===t&&(t=!0);var a=e.filter;void 0===a&&(a=function(e,t,a){return!0});var n=e.transformer;void 0===n&&(n=function(e){return e});var o=e.mutationTransformer;void 0===o&&(o=function(e){return e});var i=e.actionFilter;void 0===i&&(i=function(e,t){return!0});var s=e.actionTransformer;void 0===s&&(s=function(e){return e});var u=e.logMutations;void 0===u&&(u=!0);var c=e.logActions;void 0===c&&(c=!0);var l=e.logger;return void 0===l&&(l=console),function(e){var p=r(e.state);void 0!==l&&(u&&e.subscribe((function(e,i){var s=r(i);if(a(e,p,s)){var u=I(),c=o(e),m="mutation "+e.type+u;E(l,m,t),l.log("%c prev state","color: #9E9E9E; font-weight: bold",n(p)),l.log("%c mutation","color: #03A9F4; font-weight: bold",c),l.log("%c next state","color: #4CAF50; font-weight: bold",n(s)),T(l)}p=s})),c&&e.subscribeAction((function(e,a){if(i(e,a)){var n=I(),o=s(e),r="action "+e.type+n;E(l,r,t),l.log("%c action","color: #03A9F4; font-weight: bold",o),T(l)}})))}}};const O=function(e,t,a,n){if("[object Object]"===Object.prototype.toString.call(t)&&(null==a||e.hasOwnProperty(a))){const o=null==a?e:e[a];if(null!=o){for(var i in t)O(o,t[i],i,n);return}}n&&null===t||o.a.set(e,a,t)};var F={CHANGE_PROFILE(e,t){e.profile.selected=t},CHANGE_SETTING(e,t){for(let a=0;a<t.length;a++){let n=t[a].name.split("."),o=n.slice(0,-1).reduce((e,t)=>e[t],e);"boolean"!=typeof t[a].value&&(isNaN(Number(t[a].value))||(t[a].value=parseInt(t[a].value,10))),o[n.slice(-1).pop()]=t[a].value}},INITIALIZE(e,t){O(e,t)},TOGGLE_CHAMELEON(e,t){e.config.enabled=t},TOGGLE_NOTIFICATIONS(e,t){e.config.notificationsEnabled=!e.config.notificationsEnabled},TOGGLE_THEME(e,t){e.config.theme="light"==e.config.theme?"dark":"light"},UPDATE_EXCLUSIONS(e,t){e.excluded=t}},D=a(3);const L=({commit:e},t)=>{e("CHANGE_PROFILE",t),browser.runtime.sendMessage({action:"updateProfile",data:t})},N=({commit:e},t)=>{e("CHANGE_SETTING",t),"whitelist.enabledContextMenu"===t[0].name?D.a.enableContextMenu(t[0].value):"profile.interval.option"===t[0].name?browser.runtime.sendMessage({action:"reloadProfile",data:t[0].value}):"profile.showProfileOnIcon"===t[0].name?browser.runtime.sendMessage({action:"toggleBadgeText",data:t[0].value}):["headers.spoofAcceptLang.enabled","headers.spoofAcceptLang.value","options.blockMediaDevices","options.spoofMediaDevices","options.blockCSSExfil","options.limitHistory","options.protectKBFingerprint.enabled","options.protectKBFingerprint.delay","options.protectWinName","options.spoofAudioContext","options.spoofClientRects","options.spoofFontFingerprint","options.screenSize","options.timeZone"].includes(t[0].name)?window.setTimeout(async()=>{"headers.spoofAcceptLang.enabled"===t[0].name||["headers.spoofAcceptLang.value","options.timeZone"].includes(t[0].name)&&"ip"===t[0].value?await browser.runtime.sendMessage({action:"reloadIPInfo",data:!1}):await browser.runtime.sendMessage({action:"reloadInjectionScript"})},350):["headers.spoofIP.enabled","headers.spoofIP.option","headers.spoofIP.rangeFrom"].includes(t[0].name)?browser.runtime.sendMessage({action:"reloadSpoofIP",data:t}):["options.cookiePolicy","options.cookieNotPersistent","options.disableWebRTC","options.firstPartyIsolate","options.resistFingerprinting","options.trackingProtectionMode","options.webRTCPolicy"].includes(t[0].name)&&D.a.setBrowserConfig(t[0].name,t[0].value)},R=({commit:e,state:t},a)=>{if("string"==typeof a){let e=t.excluded.indexOf(a);e>-1?t.excluded.splice(e,1):t.excluded.push(a)}else{let e=a.map(e=>t.excluded.indexOf(e));e.sort((e,t)=>t-e);for(let a=0;a<e.length;a++)e[a]>-1&&t.excluded.splice(e[a],1);e.includes(-1)&&(t.excluded=t.excluded.concat(a))}e("UPDATE_EXCLUSIONS",t.excluded)},q=async({commit:e})=>{let t=await D.a.getSettings(null);browser.runtime.sendMessage({action:"init"}),e("INITIALIZE",t)},B=({commit:e},t)=>{e("TOGGLE_CHAMELEON",t),D.a.enableChameleon(t)},U=({commit:e},t)=>{e("TOGGLE_NOTIFICATIONS")},$=({commit:e},t)=>{e("TOGGLE_THEME")};o.a.use(M);t.a=new M.Store({state:{config:{enabled:!0,notificationsEnabled:!1,theme:"light",reloadIPStartupDelay:0},excluded:[],headers:{blockEtag:!1,enableDNT:!1,referer:{disabled:!1,xorigin:0,trimming:0},spoofAcceptLang:{enabled:!1,value:"default"},spoofIP:{enabled:!1,option:0,rangeFrom:"",rangeTo:""}},ipRules:[],profile:{selected:"none",interval:{option:0,min:1,max:1},showProfileOnIcon:!0},options:{cookieNotPersistent:!1,cookiePolicy:"allow_all",blockMediaDevices:!1,blockCSSExfil:!1,disableWebRTC:!1,firstPartyIsolate:!1,limitHistory:!1,protectKBFingerprint:{enabled:!1,delay:1},protectWinName:!1,resistFingerprinting:!1,screenSize:"default",spoofAudioContext:!1,spoofClientRects:!1,spoofFontFingerprint:!1,spoofMediaDevices:!1,timeZone:"default",trackingProtectionMode:"always",webRTCPolicy:"default",webSockets:"allow_all"},whitelist:{enabledContextMenu:!1,defaultProfile:"none",rules:[]}},mutations:F,actions:n})},8:function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},9:function(e,t,a){"use strict";t.a=["https://www.google.com/recaptcha/api2","https://accounts.google.com","https://docs.google.com","https://drive.google.com","https://accounts.youtube.com","https://disqus.com/embed/comments/"]}});