{"extDescription": {"message": "Podrabiaj profil przeglądarki. Zawiera kilka opcji poprawy prywatności."}, "notifications-profileChange": {"message": "Zmieniono profil:"}, "notifications-unableToGetIPInfo": {"message": "Nie można uzyskać informacji o IP"}, "notifications-usingIPInfo": {"message": "Informacje o używanym IP:"}, "notifications-usingIPRule": {"message": "Reguła używanego IP:"}, "options-about-issueTracker": {"message": "Śledzenie problemów"}, "options-about-knownIssues": {"message": "<PERSON><PERSON><PERSON> problemy"}, "options-about-wiki": {"message": "Wiki"}, "options-about-support": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "options-about-sourceCode": {"message": "Kod źródłowy"}, "options-about-translate": {"message": "Pomóż w tłumaczeniu"}, "options-import-couldNotImport": {"message": "Nie można zaimpo<PERSON>ować pliku"}, "options-import-invalid-config": {"message": "Nieprawidłowe ustawienia: brak konfiguracji"}, "options-import-invalid-excluded": {"message": "Nieprawidłowe ustawienia: brak wykluczenia"}, "options-import-invalid-excludedProfile": {"message": "Nieprawidłowe ustawienia: wykluczono nieprawidłowy profil"}, "options-import-invalid-headers": {"message": "Nieprawidłowe ustawienia: brakujące nagłówki"}, "options-import-invalid-ipRuleId": {"message": "Nieprawidłowe ustawienia: nieprawidłowy identyfikator reguły IP"}, "options-import-invalid-ipRuleName": {"message": "Nieprawidłowe ustawienia: brak nazwy reguły IP"}, "options-import-invalid-ipRuleRange": {"message": "Nieprawidłowe ustawienia: nieprawidłowy zakres IP reguły"}, "options-import-invalid-ipRules": {"message": "Nieprawidłowe ustawienia: brak reguł IP"}, "options-import-invalid-ipRulesDupe": {"message": "Nieprawidłowe ustawienia: znaleziono duplikat identyfikatora reguły IP"}, "options-import-invalid-options": {"message": "Nieprawidłowe ustawienia: brakujące opcje"}, "options-import-invalid-profile": {"message": "Nieprawidłowe ustawienia: brakuj<PERSON>cy profil"}, "options-import-invalid-setting": {"message": "Nieprawidłowa wartość ustawienia:"}, "options-import-invalid-settings": {"message": "Nieprawidłowe ustawienia: brakujące ustawienia"}, "options-import-invalid-spoofIP": {"message": "Nieprawidłowe ustawienia: zakres podrabiania nagłówka IP jest nieprawidłowy"}, "options-import-invalid-version": {"message": "Nieprawidłowe ustawienia: wersja nie jest zaakceptowana"}, "options-import-invalid-whitelist": {"message": "Nieprawidłowe ustawienia: brakująca biała lista"}, "options-import-invalid-whitelistDupe": {"message": "Nieprawidłowe ustawienia: znaleziono duplikat reguły w białej liście"}, "options-import-invalid-whitelistId": {"message": "Nieprawidłowe ustawienia: nieprawidłowy identyfikator reguły białej listy"}, "options-import-invalid-whitelistName": {"message": "Nieprawidłowe ustawienia: brakuje nazwy reguły w białej liście"}, "options-import-invalid-whitelistOpt": {"message": "Nieprawidłowe ustawienia: nieprawidłowe opcje reguły białej listy"}, "options-import-invalid-whitelistSpoofIP": {"message": "Nieprawidłowe ustawienia: niepoprawna reguła podrabiania IP dla białej listy"}, "options-import-success": {"message": "Pomyślnie zaimportowano ustawienia. Ponowne wczytywanie rozszerzenia..."}, "options-ipRules-editorTitle": {"message": "<PERSON><PERSON><PERSON>"}, "options-ipRules-ipRule": {"message": "Reguła IP"}, "options-ipRules-reload": {"message": "Odśwież informacje o IP"}, "options-ipRules-textareaLabel": {"message": "Zakresy/adresy IP"}, "options-ipRules-textareaPlaceholder": {"message": "Jeden zakres/adres IP na linię"}, "options-modal-askDelete": {"message": "<PERSON>zy na pewno chcesz usunąć tę regułę?"}, "options-modal-askReset": {"message": "<PERSON>zy na pewno chcesz zresetować ustawienia?"}, "options-modal-confirmDelete": {"message": "<PERSON><PERSON>, usuń to!"}, "options-modal-confirmReset": {"message": "Ta<PERSON>, z<PERSON>etuj moje ustawienia!"}, "options-settings": {"message": "Ustawienia"}, "options-settings-import": {"message": "Import<PERSON>j"}, "options-settings-importing": {"message": "Importowanie ustawień"}, "options-settings-export": {"message": "Eksportuj"}, "options-settings-reset": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options-settings-permissions": {"message": "Chameleon może kontrolować niektóre ustawienia Firefoxa, takie jak zapobieganie fingerprintingu lub włączenie ochrony przed śledzeniem. <PERSON>że to powodować konflikty z innym rozszerzeniem. <PERSON><PERSON><PERSON><PERSON>rezygnować z kontrolowania tych preferencji przez Chameleon, usuwając uprawnienie prywatności. <PERSON><PERSON><PERSON><PERSON>, że usunięcie tego uprawnienia spowoduje zresetowanie tych preferencji. <PERSON><PERSON><PERSON><PERSON> o to uprawnienie, jeśli nie jest ono przyznane."}, "options-settings-permissions-legacy": {"message": "<PERSON><PERSON><PERSON> z<PERSON> specjalną wersj<PERSON>eon, aby wł<PERSON><PERSON>yć uprawnienia do ustawień prywatności. Jest to spowodowane pewnymi komplikacjami z obsługiwanymi uprawnieniami dla międzyplatformowej/nowej wersji tego rozszerzenia. Więcej szczegółów można znaleźć na wiki podłączonej poniżej."}, "options-settings-permissions-legacy-wiki": {"message": "Więcej informacji na wiki"}, "options-settings-permissions-request": {"message": "Poproś o uprawnienia prywatności"}, "options-settings-permissions-remove": {"message": "Usuń uprawnienia prywatności"}, "options-tab-about": {"message": "O programie"}, "options-tab-ipRules": {"message": "Reguły IP"}, "options-whitelist-acceptLang": {"message": "Akceptowany język"}, "options-whitelist-editorTitle": {"message": "<PERSON><PERSON><PERSON>y"}, "options-whitelist-headerIPLabel": {"message": "Nagłówek IP (Via & X-Forwarded-For)"}, "options-whitelist-options-audioContext": {"message": "Włącz podrabianie audio context"}, "options-whitelist-options-clientRects": {"message": "Włącz podrabianie client rects"}, "options-whitelist-options-cssExfil": {"message": "Zablokuj CSS Exfil"}, "options-whitelist-options-mediaDevices": {"message": "Blokuj urządzenia multimedialne"}, "options-whitelist-options-name": {"message": "Włącz ochronę nazwy okna"}, "options-whitelist-options-tz": {"message": "Włącz podrabianie strefy czasowej"}, "options-whitelist-options-ws": {"message": "Wyłącz WebSocket"}, "options-whitelist-rule": {"message": "Reguła białej listy"}, "options-whitelist-sitesTip": {"message": "Jedna reguła na linię: domena@@[opcjonalny wzorzec regex]"}, "options-whitelist-textareaLabel": {"message": "Zakresy/adresy IP"}, "options-whitelist-textareaPlaceholder": {"message": "Jeden zakres IP/IP na linię"}, "popup-home-change": {"message": "zmień"}, "popup-home-currentProfile": {"message": "Bieżący profil"}, "popup-home-currentProfile-defaultLanguage": {"message": "Domyślny język"}, "popup-home-currentProfile-defaultScreen": {"message": "Domyślny ekran"}, "popup-home-currentProfile-defaultTimezone": {"message": "Domyślna strefa c<PERSON>owa"}, "popup-home-currentProfile-gettingTimezone": {"message": "Uzyskiwanie informacji o IP"}, "popup-home-currentProfile-screenProfile": {"message": "<PERSON><PERSON><PERSON> (profil)"}, "popup-home-disabled": {"message": "Cha<PERSON><PERSON> jest wył<PERSON>czony"}, "popup-home-enabled": {"message": "Cha<PERSON><PERSON> jest włączony"}, "popup-home-notification-disabled": {"message": "Powiadomienia wyłączone"}, "popup-home-notification-enabled": {"message": "Powiadomienia włączone"}, "popup-home-onThisPage": {"message": "na tej stronie"}, "popup-home-theme-dark": {"message": "Ciemny"}, "popup-home-theme-light": {"message": "<PERSON><PERSON><PERSON>"}, "popup-profile-changePeriodically": {"message": "Zmień okresowo"}, "popup-profile-devicePhone": {"message": "Telefon"}, "popup-profile-deviceTablet": {"message": "Tablet"}, "popup-profile-interval-no": {"message": "Żaden"}, "popup-profile-interval-custom": {"message": "Własny interwał"}, "popup-profile-interval-customMax": {"message": "<PERSON><PERSON><PERSON><PERSON> (minuty)"}, "popup-profile-interval-customMin": {"message": "Minimum (minuty)"}, "popup-profile-interval-minute": {"message": "Co minutę"}, "popup-profile-interval-5minutes": {"message": "Co 5 minut"}, "popup-profile-interval-10minutes": {"message": "Co 10 minut"}, "popup-profile-interval-20minutes": {"message": "Co 20 minut"}, "popup-profile-interval-30minutes": {"message": "Co 30 minut"}, "popup-profile-interval-40minutes": {"message": "Co 40 minut"}, "popup-profile-interval-50minutes": {"message": "Co 50 minut"}, "popup-profile-interval-hour": {"message": "Co godzinę"}, "popup-profile-exclude": {"message": "W<PERSON><PERSON>cz"}, "popup-profile-randomAndroid": {"message": "Losowe przeglądarki z Androida"}, "popup-profile-randomIOS": {"message": "Losowe przeglądarki z iOS"}, "popup-profile-randomMacOS": {"message": "Losowe przeglądarki z macOS"}, "popup-profile-randomLinux": {"message": "Losowe przeglądarki z Linuxa"}, "popup-profile-randomWindows": {"message": "Losowe przeglądarki z Windowsa"}, "popup-profile-random": {"message": "<PERSON><PERSON><PERSON>"}, "popup-profile-randomDesktopProfile": {"message": "<PERSON><PERSON> (Desktop)"}, "popup-profile-randomMobileProfile": {"message": "<PERSON><PERSON> (Mobile)"}, "popup-profile-showProfileOnIcon": {"message": "Pokaż profil przeglądarki na ikonie"}, "popup-headers": {"message": "Nagłówki"}, "popup-headers-enableDNT": {"message": "Włącz DNT (Nie Śledź)"}, "popup-headers-preventEtag": {"message": "Zapobiegaj <PERSON>led<PERSON>"}, "popup-headers-refererWarning": {"message": "<PERSON><PERSON> mody<PERSON><PERSON>j us<PERSON>wi<PERSON> about:config d<PERSON> p<PERSON><PERSON><PERSON><PERSON> op<PERSON>."}, "popup-headers-referer-trimming": {"message": "Polityka Wycinania Referer"}, "popup-headers-referer-trimming-sendFullURI": {"message": "Wyślij pełny URI (domyślnie)"}, "popup-headers-referer-trimming-schemeHostPortPath": {"message": "<PERSON><PERSON><PERSON>, host, port + ścieżka"}, "popup-headers-referer-trimming-schemeHostPort": {"message": "<PERSON><PERSON><PERSON>, host + port"}, "popup-headers-referer-xorigin": {"message": "Zasady pochodzenia Referer X"}, "popup-headers-referer-xorigin-alwaysSend": {"message": "Z<PERSON>ze wysyłaj (domyślnie)"}, "popup-headers-referer-xorigin-matchBaseDomain": {"message": "Do<PERSON><PERSON>j <PERSON> bazową"}, "popup-headers-referer-xorigin-matchHost": {"message": "Dopasuj hosta"}, "popup-headers-spoofAcceptLang": {"message": "Podrabiaj używany język"}, "popup-headers-spoofIP": {"message": "Podrabiaj X-Forwarded-For/Via IP"}, "popup-headers-spoofIP-random": {"message": "Losowe IP"}, "popup-headers-spoofIP-custom": {"message": "Własny adres IP"}, "popup-headers-spoofIP-rangeFrom": {"message": "Zakres od"}, "popup-headers-spoofIP-rangeTo": {"message": "Zakres do"}, "popup-options": {"message": "Ustawienia"}, "popup-options-grantPermissions": {"message": "Udziel uprawnień do modyfikowania ustawień"}, "popup-options-injection": {"message": "Wstrzyknięcie"}, "popup-options-injection-limitTabHistory": {"message": "Ogranicz historię kart"}, "popup-options-injection-protectWinName": {"message": "Chroń nazwę okna"}, "popup-options-injection-audioContext": {"message": "Podrabiaj audio context"}, "popup-options-injection-clientRects": {"message": "Podrabiaj client rects"}, "popup-options-injection-protectKBFingerprint": {"message": "Chroń przed identyfikacją klawiatury"}, "popup-options-injection-protectKBFingerprintDelay": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> (ms)"}, "popup-options-injection-screen": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "popup-options-injection-spoofFontFingerprint": {"message": "Zapobiegaj fingerprintingowi przez czcionki"}, "popup-options-standard": {"message": "Standardowy"}, "popup-options-standard-blockMediaDevices": {"message": "Blokuj urządzenia multimedialne"}, "popup-options-standard-blockCSSExfil": {"message": "Zablokuj CSS Exfil"}, "popup-options-standard-disableWebRTC": {"message": "Wyłącz WebRTC"}, "popup-options-standard-firstPartyIsolation": {"message": "Włącz izolację stron <PERSON>ch"}, "popup-options-standard-resistFingerprinting": {"message": "Włącz ochronę przed fingerprintingiem"}, "popup-options-standard-spoofMediaDevices": {"message": "Podrabiaj urządzenia multimedialne"}, "popup-options-standard-trackingProtection": {"message": "Tryb ochrony przed śledzeniem"}, "popup-options-standard-trackingProtection-on": {"message": "Włączony"}, "popup-options-standard-trackingProtection-off": {"message": "Wyłączony"}, "popup-options-standard-trackingProtection-privateBrowsing": {"message": "Włączone w prywatnych kartach"}, "popup-options-standard-webRTCPolicy": {"message": "Polityka WebRTC"}, "popup-options-standard-webRTCPolicy-nonProxified": {"message": "Wyłącz nieupoważniony UDP"}, "popup-options-standard-webRTCPolicy-public": {"message": "Używaj tylko publicznego interfejsu (najlepsze)"}, "popup-options-standard-webRTCPolicy-publicPrivate": {"message": "Użyj publicznego i prywatnego interfejsu"}, "popup-options-standard-webSockets-blockAll": {"message": "Zablokuj wszystko"}, "popup-options-standard-webSockets-blockThirdParty": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>rz<PERSON>"}, "popup-options-cookie": {"message": "<PERSON><PERSON>"}, "popup-options-cookieNotPersistent": {"message": "Usuń pliki cookie i dane witryn po zamknięciu okna"}, "popup-options-cookiePolicy": {"message": "Reguła"}, "popup-options-cookiePolicy-allowVisited": {"message": "Zezwól na odwiedzone"}, "popup-options-cookiePolicy-rejectAll": {"message": "<PERSON><PERSON><PERSON><PERSON> wszystko"}, "popup-options-cookiePolicy-rejectThirdParty": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>rz<PERSON>"}, "popup-options-cookiePolicy-rejectTrackers": {"message": "Odrzuć trackery"}, "popup-options-cookiePolicy-rejectTrackersPartitionForeign": {"message": "Odr<PERSON>ć skrypty śledzące i partycjonuj pliki cookie stron trzecich"}, "popup-whitelist-contextMenu": {"message": "Dodaj element menu kontekstowego, aby otworzyć domenę bieżącej karty na białej liście"}, "popup-whitelist-defaultProfileLabel": {"message": "Domyślny profil"}, "popup-whitelist-enable": {"message": "Włącz białą listę"}, "popup-whitelist-isNotWhitelisted": {"message": "nie jest na białej liście"}, "popup-whitelist-isWhitelisted": {"message": "jest na białej liście"}, "popup-whitelist-open": {"message": "Otwórz na białej liście"}, "text-addToRule": {"message": "Dodaj do reguły: $RULE_NAME$", "placeholders": {"rule_name": {"content": "$1", "example": "Firefox whitelist rule profile"}}}, "text-allowAll": {"message": "Zezwól wszystkim"}, "text-cancel": {"message": "<PERSON><PERSON><PERSON>"}, "text-createNewRule": {"message": "Utwórz nową regułę"}, "text-default": {"message": "Domyślny"}, "text-defaultWhitelistProfile": {"message": "Domyślny profil białej listy"}, "text-disableReferer": {"message": "Wyłącz referer"}, "text-language": {"message": "Język"}, "text-name": {"message": "Nazwa"}, "text-profile": {"message": "Profil"}, "text-realProfile": {"message": "<PERSON><PERSON>"}, "text-removeFromRule": {"message": "Usuń z reguły: $RULE_NAME$", "placeholders": {"rule_name": {"content": "$1", "example": "Firefox whitelist rule profile"}}}, "text-save": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "text-screen": {"message": "<PERSON><PERSON><PERSON>"}, "text-searchRules": {"message": "Reguły wyszukiwania"}, "text-startupDelay": {"message": "Opóźnienie uruchomienia (sek)"}, "text-timezone": {"message": "<PERSON><PERSON><PERSON>"}, "text-whitelist": {"message": "Biała lista"}}