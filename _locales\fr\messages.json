{"extDescription": {"message": "Falsifiez le profil de votre navigateur. Cette extension inclut également quelques options pour améliorer votre vie privée."}, "notifications-profileChange": {"message": "Profil modifié :"}, "notifications-unableToGetIPInfo": {"message": "Impossible d'obtenir des informations IP"}, "notifications-usingIPInfo": {"message": "Utiliser l'information IP :"}, "notifications-usingIPRule": {"message": "Utiliser la règle IP :"}, "options-about-issueTracker": {"message": "Suivi des problèmes"}, "options-about-knownIssues": {"message": "Problèmes connus"}, "options-about-wiki": {"message": "Wiki"}, "options-about-support": {"message": "Support"}, "options-about-sourceCode": {"message": "Code source"}, "options-about-translate": {"message": "Aider à traduire l'extension"}, "options-import-couldNotImport": {"message": "Le fichier n'a pas pu être importé"}, "options-import-invalid-config": {"message": "Paramètres invalides : configuration manquante"}, "options-import-invalid-excluded": {"message": "Paramètres invalides : sites exclus manquants"}, "options-import-invalid-excludedProfile": {"message": "Paramètres invalides : un site exclu possède un profil invalide"}, "options-import-invalid-headers": {"message": "Paramètres invalides : en-tê<PERSON> manquants"}, "options-import-invalid-ipRuleId": {"message": "Paramètres invalides : identifiant de règle IP invalide"}, "options-import-invalid-ipRuleName": {"message": "Paramètres invalides : nom de règle IP manquant"}, "options-import-invalid-ipRuleRange": {"message": "Paramètres invalides : plage d'adresses IP de règle IP invalide"}, "options-import-invalid-ipRules": {"message": "Paramètres invalides : r<PERSON>gles IP manquantes"}, "options-import-invalid-ipRulesDupe": {"message": "Paramètres invalides : une règle IP a été trouvée en double"}, "options-import-invalid-options": {"message": "Paramètres invalides : options manquantes"}, "options-import-invalid-profile": {"message": "Paramètres invalides : profil manquant"}, "options-import-invalid-setting": {"message": "<PERSON><PERSON> <PERSON> param<PERSON><PERSON> invalide :"}, "options-import-invalid-settings": {"message": "Paramètres invalides : paramètres manquants"}, "options-import-invalid-spoofIP": {"message": "Paramètres invalides : la plage d'adresses IP de l'en-tête de falsification est invalide"}, "options-import-invalid-version": {"message": "Paramètres invalides : la version n'est pas prise en charge"}, "options-import-invalid-whitelist": {"message": "Paramètres invalides : liste blanche manquante"}, "options-import-invalid-whitelistDupe": {"message": "Paramètres invalides : une règle de liste blanche a été trouvée en double"}, "options-import-invalid-whitelistId": {"message": "Paramètres invalides : identifiant de règle de la liste blanche invalide"}, "options-import-invalid-whitelistName": {"message": "Paramètres invalides : nom de règle de la liste blanche manquant"}, "options-import-invalid-whitelistOpt": {"message": "Paramètres invalides : options de règle de la liste blanche invalides"}, "options-import-invalid-whitelistSpoofIP": {"message": "Paramètres invalides : adresse IP de falsification pour la règle de liste blanche invalide"}, "options-import-success": {"message": "Réglages importés avec succès. Rechargement de l'extension..."}, "options-ipRules-editorTitle": {"message": "É<PERSON>eur de règle <PERSON>"}, "options-ipRules-ipRule": {"message": "Règle IP"}, "options-ipRules-reload": {"message": "Actualiser les informations IP"}, "options-ipRules-textareaLabel": {"message": "Plages IP / Adresses IP"}, "options-ipRules-textareaPlaceholder": {"message": "Une adresse IP/plage d'adresses IP par ligne"}, "options-modal-askDelete": {"message": "Êtes-vous sûr de vouloir supprimer cette règle ?"}, "options-modal-askReset": {"message": "Êtes-vous sûr de vouloir réinitialiser les paramètres ?"}, "options-modal-confirmDelete": {"message": "<PERSON><PERSON>, supprimer"}, "options-modal-confirmReset": {"message": "<PERSON><PERSON>, réinitialiser mes paramètres"}, "options-settings": {"message": "Paramètres"}, "options-settings-import": {"message": "Importer"}, "options-settings-importing": {"message": "Importation des paramètres"}, "options-settings-export": {"message": "Exporter"}, "options-settings-reset": {"message": "Réinitialiser les paramètres par défaut"}, "options-settings-permissions": {"message": "Chameleon peut contrôler certaines des préférences de Firefox, comme résister au fingerprinting ou activer la protection contre le pistage, ce qui peut causer des problèmes avec d'autres extensions.\nCependant, vous pouvez refuser que Chameleon contrôle ces préférences en désactivant l'autorisation \"Lire et modifier les paramètres de confidentialité\". Dans ce cas, les paramètres correspondants seront automatiquement réinitialisés.\nÀ l'inverse, vous pouvez activer cette autorisation si celle-ci est actuellement désactivée."}, "options-settings-permissions-legacy": {"message": "Vous devrez installer une version spécifique de Chameleon pour activer les autorisations de confidentialité. Cela est dû à certaines complications avec les autorisations prises en charge pour une plateforme croisée ou une nouvelle version de cette extension. Vous trouverez plus de détails sur le wiki accessible en cliquant sur le bouton ci-dessous."}, "options-settings-permissions-legacy-wiki": {"message": "Plus d'informations sur le wiki"}, "options-settings-permissions-request": {"message": "Activer l'autorisation \"Lire et modifier les paramètres de confidentialité\""}, "options-settings-permissions-remove": {"message": "Désactiver l'autorisation \"Lire et modifier les paramètres de confidentialité\""}, "options-tab-about": {"message": "À propos"}, "options-tab-ipRules": {"message": "Règles IP"}, "options-whitelist-acceptLang": {"message": "<PERSON><PERSON> (en-tête \"Accept-Language\")"}, "options-whitelist-editorTitle": {"message": "Éditeur de règles de la liste blanche"}, "options-whitelist-headerIPLabel": {"message": "En-tête IP (Via & X-Forwarded For)"}, "options-whitelist-options-audioContext": {"message": "Activer la falsification du contexte audio"}, "options-whitelist-options-clientRects": {"message": "Activer la falsification des clients rects"}, "options-whitelist-options-cssExfil": {"message": "Bloquer les attaques d'exfiltration de données CSS"}, "options-whitelist-options-mediaDevices": {"message": "Bloquer les périphériques multimédias"}, "options-whitelist-options-name": {"message": "Activer la protection du nom de la fenêtre"}, "options-whitelist-options-tz": {"message": "Activer la falsification du fuseau horaire"}, "options-whitelist-options-ws": {"message": "Désactiver WebSocket"}, "options-whitelist-rule": {"message": "<PERSON><PERSON><PERSON> de liste blanche"}, "options-whitelist-sitesTip": {"message": "Une règle par ligne : domaine@@[modèle facultatif d'expression régulière]"}, "options-whitelist-textareaLabel": {"message": "Plages IP / Adresses IP"}, "options-whitelist-textareaPlaceholder": {"message": "Une adresse IP ou plage IP par ligne"}, "popup-home-change": {"message": "Modifier"}, "popup-home-currentProfile": {"message": "Profil actuel"}, "popup-home-currentProfile-defaultLanguage": {"message": "Langue par défaut"}, "popup-home-currentProfile-defaultScreen": {"message": "Résolution d'écran par défaut"}, "popup-home-currentProfile-defaultTimezone": {"message": "Fuseau horaire par défaut"}, "popup-home-currentProfile-gettingTimezone": {"message": "Récupération des informations IP"}, "popup-home-currentProfile-screenProfile": {"message": "Écran (Profil)"}, "popup-home-disabled": {"message": "Chameleon est désactivé"}, "popup-home-enabled": {"message": "Chameleon est activé"}, "popup-home-notification-disabled": {"message": "Notifications désactivées"}, "popup-home-notification-enabled": {"message": "Notifications activées"}, "popup-home-onThisPage": {"message": "sur cette page"}, "popup-home-theme-dark": {"message": "Sombre"}, "popup-home-theme-light": {"message": "<PERSON>"}, "popup-profile-changePeriodically": {"message": "Modifier périodiquement"}, "popup-profile-devicePhone": {"message": "Téléphone"}, "popup-profile-deviceTablet": {"message": "Tablette"}, "popup-profile-interval-no": {"message": "Non"}, "popup-profile-interval-custom": {"message": "<PERSON><PERSON><PERSON>"}, "popup-profile-interval-customMax": {"message": "Maximum (minutes)"}, "popup-profile-interval-customMin": {"message": "Minimum (minutes)"}, "popup-profile-interval-minute": {"message": "Toutes les minutes"}, "popup-profile-interval-5minutes": {"message": "Toutes les 5 minutes"}, "popup-profile-interval-10minutes": {"message": "Toutes les 10 minutes"}, "popup-profile-interval-20minutes": {"message": "Toutes les 20 minutes"}, "popup-profile-interval-30minutes": {"message": "Toutes les 30 minutes"}, "popup-profile-interval-40minutes": {"message": "Toutes les 40 minutes"}, "popup-profile-interval-50minutes": {"message": "Toutes les 50 minutes"}, "popup-profile-interval-hour": {"message": "Toutes les heures"}, "popup-profile-exclude": {"message": "Exclure"}, "popup-profile-randomAndroid": {"message": "Navigateurs Android aléatoires"}, "popup-profile-randomIOS": {"message": "Navigateurs iOS aléatoires"}, "popup-profile-randomMacOS": {"message": "Navigateurs macOS aléatoires"}, "popup-profile-randomLinux": {"message": "Navigateurs Linux aléatoires"}, "popup-profile-randomWindows": {"message": "Navigateurs Windows aléatoires"}, "popup-profile-random": {"message": "Aléatoire"}, "popup-profile-randomDesktopProfile": {"message": "<PERSON><PERSON> (Ordinateur)"}, "popup-profile-randomMobileProfile": {"message": "<PERSON><PERSON> (Mobile)"}, "popup-profile-showProfileOnIcon": {"message": "Afficher le profil du navigateur sur l'icône de l'extension"}, "popup-headers": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "popup-headers-enableDNT": {"message": "Activer l'en-tête DNT (Ne pas suivre)"}, "popup-headers-preventEtag": {"message": "Empêcher le pistage par l'ETag"}, "popup-headers-refererWarning": {"message": "Ne modifiez pas les paramètres des options ci-dessous dans l’éditeur de configuration (page about:config)."}, "popup-headers-referer-trimming": {"message": "Politique de réduction du référent"}, "popup-headers-referer-trimming-sendFullURI": {"message": "Envoyer l'URI complète (par défaut)"}, "popup-headers-referer-trimming-schemeHostPortPath": {"message": "<PERSON><PERSON><PERSON><PERSON>, hô<PERSON>, port + chemin"}, "popup-headers-referer-trimming-schemeHostPort": {"message": "Schéma, hôte + port"}, "popup-headers-referer-xorigin": {"message": "Politique d'origine croisée du référent"}, "popup-headers-referer-xorigin-alwaysSend": {"message": "Toujours envoyer (par défaut)"}, "popup-headers-referer-xorigin-matchBaseDomain": {"message": "Faire correspondre au domaine de base"}, "popup-headers-referer-xorigin-matchHost": {"message": "Faire correspondre à l'hôte"}, "popup-headers-spoofAcceptLang": {"message": "Falsifier l'en-tête \"Accept-Language\""}, "popup-headers-spoofIP": {"message": "Falsifier l'en-tête \"X-Forwarded-For/Via IP\""}, "popup-headers-spoofIP-random": {"message": "IP aléatoire"}, "popup-headers-spoofIP-custom": {"message": "IP personnalisé"}, "popup-headers-spoofIP-rangeFrom": {"message": "De"}, "popup-headers-spoofIP-rangeTo": {"message": "À"}, "popup-options": {"message": "Options"}, "popup-options-grantPermissions": {"message": "Accorder la permission de modifier les paramètres"}, "popup-options-injection": {"message": "Injection"}, "popup-options-injection-limitTabHistory": {"message": "Limiter l'historique des onglets"}, "popup-options-injection-protectWinName": {"message": "<PERSON>té<PERSON> le nom des fenêtres"}, "popup-options-injection-audioContext": {"message": "Falsifier le contexte audio"}, "popup-options-injection-clientRects": {"message": "Falsifier les client rects"}, "popup-options-injection-protectKBFingerprint": {"message": "Protéger les empreintes digitales du clavier"}, "popup-options-injection-protectKBFingerprintDelay": {"message": "<PERSON><PERSON><PERSON> (millisecondes)"}, "popup-options-injection-screen": {"message": "Taille de l'écran"}, "popup-options-injection-spoofFontFingerprint": {"message": "Falsifier l'empreinte des polices"}, "popup-options-standard": {"message": "Standard"}, "popup-options-standard-blockMediaDevices": {"message": "Bloquer les périphériques multimédias"}, "popup-options-standard-blockCSSExfil": {"message": "Bloquer CSS Exfil"}, "popup-options-standard-disableWebRTC": {"message": "Désactiver WebRTC"}, "popup-options-standard-firstPartyIsolation": {"message": "Activer l'isolation de la première partie"}, "popup-options-standard-resistFingerprinting": {"message": "Activer la protection contre le fingerprinting"}, "popup-options-standard-spoofMediaDevices": {"message": "Falsifier les périphériques multimédias"}, "popup-options-standard-trackingProtection": {"message": "Mode de protection contre le pistage"}, "popup-options-standard-trackingProtection-on": {"message": "Activé"}, "popup-options-standard-trackingProtection-off": {"message": "Désactivé"}, "popup-options-standard-trackingProtection-privateBrowsing": {"message": "Activé en navigation privée"}, "popup-options-standard-webRTCPolicy": {"message": "Politique WebRTC"}, "popup-options-standard-webRTCPolicy-nonProxified": {"message": "Désactiver \"Non-proxified UDP\""}, "popup-options-standard-webRTCPolicy-public": {"message": "Utiliser uniquement l'interface publique (recommandé)"}, "popup-options-standard-webRTCPolicy-publicPrivate": {"message": "Utiliser l'interface publique et privée"}, "popup-options-standard-webSockets-blockAll": {"message": "<PERSON>ut bloquer"}, "popup-options-standard-webSockets-blockThirdParty": {"message": "Bloquer les tiers"}, "popup-options-cookie": {"message": "Cookies"}, "popup-options-cookieNotPersistent": {"message": "Supprimer les cookies et les données de site à la fermeture de la fenêtre"}, "popup-options-cookiePolicy": {"message": "Politique"}, "popup-options-cookiePolicy-allowVisited": {"message": "Autoriser les sites visités"}, "popup-options-cookiePolicy-rejectAll": {"message": "<PERSON><PERSON> rejeter"}, "popup-options-cookiePolicy-rejectThirdParty": {"message": "Rejeter les tiers"}, "popup-options-cookiePolicy-rejectTrackers": {"message": "Rejeter les traqueurs"}, "popup-options-cookiePolicy-rejectTrackersPartitionForeign": {"message": "Rejeter les traqueurs et les cookies tiers"}, "popup-whitelist-contextMenu": {"message": "Ajouter un élément de menu contextuel pour ouvrir le domaine de l'onglet actuel dans la liste blanche"}, "popup-whitelist-defaultProfileLabel": {"message": "Profil par défaut"}, "popup-whitelist-enable": {"message": "<PERSON>r la liste blanche"}, "popup-whitelist-isNotWhitelisted": {"message": "ne figure pas dans la liste blanche"}, "popup-whitelist-isWhitelisted": {"message": "figure dans la liste blanche"}, "popup-whitelist-open": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans la liste blanche"}, "text-addToRule": {"message": "Ajouter à la règle : $RULE_NAME$", "placeholders": {"rule_name": {"content": "$1", "example": "Firefox whitelist rule profile"}}}, "text-allowAll": {"message": "Tout autoriser"}, "text-cancel": {"message": "Annuler"}, "text-createNewRule": {"message": "<PERSON><PERSON><PERSON> une nouvelle règle"}, "text-default": {"message": "<PERSON><PERSON> <PERSON><PERSON>"}, "text-defaultWhitelistProfile": {"message": "Profil par défaut de la liste blanche"}, "text-disableReferer": {"message": "Désactiver le référent"}, "text-language": {"message": "<PERSON><PERSON>"}, "text-name": {"message": "Nom"}, "text-profile": {"message": "Profil"}, "text-realProfile": {"message": "<PERSON><PERSON>"}, "text-removeFromRule": {"message": "Retirer de la règle : $RULE_NAME$", "placeholders": {"rule_name": {"content": "$1", "example": "Firefox whitelist rule profile"}}}, "text-save": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "text-screen": {"message": "É<PERSON>ran"}, "text-searchRules": {"message": "Rechercher des règles"}, "text-startupDelay": {"message": "<PERSON><PERSON><PERSON> (en secondes)"}, "text-timezone": {"message": "<PERSON><PERSON> ho<PERSON>"}, "text-whitelist": {"message": "Liste blanche"}}