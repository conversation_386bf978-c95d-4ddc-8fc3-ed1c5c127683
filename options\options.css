html {
  font-size: 16px;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.ps {
  height: 100%;
}
.ps__rail-y {
  opacity: 0.6 !important;
}

/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */

/* Document
   ========================================================================== */

/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */

html {
  line-height: 1.15; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
}

/* Sections
   ========================================================================== */

/**
 * Remove the margin in all browsers.
 */

body {
  margin: 0;
}

/**
 * Render the `main` element consistently in IE.
 */

main {
  display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */

/* Grouping content
   ========================================================================== */

/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */

/* Text-level semantics
   ========================================================================== */

/**
 * Remove the gray background on active links in IE 10.
 */

a {
  background-color: transparent;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */

code {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/**
 * Add the correct font size in all browsers.
 */

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */

/* Embedded content
   ========================================================================== */

/**
 * Remove the border on images inside links in IE 10.
 */

img {
  border-style: none;
}

/* Forms
   ========================================================================== */

/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */

button,
input,
select,
textarea {
  font-family: inherit; /* 1 */
  font-size: 100%; /* 1 */
  line-height: 1.15; /* 1 */
  margin: 0; /* 2 */
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */

button,
input { /* 1 */
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */

button,
select { /* 1 */
  text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */

button,
[type="button"],
[type="reset"] {
  -webkit-appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */

/**
 * Remove the default vertical scrollbar in IE 10+.
 */

textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */

[type="checkbox"],
[type="radio"] {
  box-sizing: border-box; /* 1 */
  padding: 0; /* 2 */
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */

/* Interactive
   ========================================================================== */

/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */

/*
 * Add the correct display in all browsers.
 */

/* Misc
   ========================================================================== */

/**
 * Add the correct display in IE 10+.
 */

template {
  display: none;
}

/**
 * Add the correct display in IE 10.
 */

[hidden] {
  display: none;
}

/**
 * Manually forked from SUIT CSS Base: https://github.com/suitcss/base
 * A thin layer on top of normalize.css that provides a starting point more
 * suitable for web applications.
 */

/**
 * Removes the default spacing and border for appropriate elements.
 */


p {
  margin: 0;
}

button {
  background-color: transparent;
  background-image: none;
}

/**
 * Work around a Firefox/IE bug where the transparent `button` background
 * results in a loss of the default `button` focus styles.
 */

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}


ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

/**
 * Tailwind custom reset styles
 */

/**
 * 1. Use the user's configured `sans` font-family (with Tailwind's default
 *    sans-serif font stack as a fallback) as a sane default.
 * 2. Use Tailwind's default "normal" line-height so the user isn't forced
 *    to override it to ensure consistency even when using the default theme.
 */

html {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 1 */
  line-height: 1.5; /* 2 */
}

/**
 * 1. Prevent padding and border from affecting element width.
 *
 *    We used to set this in the html element and inherit from
 *    the parent element for everything else. This caused issues
 *    in shadow-dom-enhanced elements like <details> where the content
 *    is wrapped by a div with box-sizing set to `content-box`.
 *
 *    https://github.com/mozdevs/cssremedy/issues/4
 *
 *
 * 2. Allow adding a border to an element by just adding a border-width.
 *
 *    By default, the way the browser specifies that an element should have no
 *    border is by setting it's border-style to `none` in the user-agent
 *    stylesheet.
 *
 *    In order to easily add borders to elements by just setting the `border-width`
 *    property, we change the default border-style for all elements to `solid`, and
 *    use border-width to hide them instead. This way our `border` utilities only
 *    need to set the `border-width` property instead of the entire `border`
 *    shorthand, making our border utilities much more straightforward to compose.
 *
 *    https://github.com/tailwindcss/tailwindcss/pull/116
 */

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e2e8f0; /* 2 */
}

/*
 * Ensure horizontal rules are visible by default
 */

/**
 * Undo the `border-style: none` reset that Normalize applies to images so that
 * our `border-{width}` utilities have the expected effect.
 *
 * The Normalize reset is unnecessary for us since we default the border-width
 * to 0 on all elements.
 *
 * https://github.com/tailwindcss/tailwindcss/issues/362
 */

img {
  border-style: solid;
}

textarea {
  resize: vertical;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #a0aec0;
}

input::placeholder,
textarea::placeholder {
  color: #a0aec0;
}

button {
  cursor: pointer;
}

table {
  border-collapse: collapse;
}

/**
 * Reset links to optimize for opt-in styling instead of
 * opt-out.
 */

a {
  color: inherit;
  text-decoration: inherit;
}

/**
 * Reset form element properties that are easy to forget to
 * style explicitly so you don't inadvertently introduce
 * styles that deviate from your design system. These styles
 * supplement a partial reset that is already applied by
 * normalize.css.
 */

button,
input,
select,
textarea {
  padding: 0;
  line-height: inherit;
  color: inherit;
}

/**
 * Use the configured 'mono' font family for elements that
 * are expected to be rendered with a monospace font, falling
 * back to the system monospace stack if there is no configured
 * 'mono' font family.
 */


code {
  font-family: Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

/**
 * Make replaced elements `display: block` by default as that's
 * the behavior you want almost all of the time. Inspired by
 * CSS Remedy, with `svg` added as well.
 *
 * https://github.com/mozdevs/cssremedy/issues/14
 */

img,
svg,
object {
  display: block;
  vertical-align: middle;
}

/**
 * Constrain images and videos to the parent width and preserve
 * their instrinsic aspect ratio.
 *
 * https://github.com/mozdevs/cssremedy/issues/14
 */

img {
  max-width: 100%;
  height: auto;
}

.form-input {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #a0aec0;
  border-width: 1px;
  border-radius: 0.25rem;
  padding-top: 0.25rem;
  padding-right: 0.5rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 1rem;
  line-height: 1.5;
}

.form-input::-moz-placeholder {
  color: #a0aec0;
  opacity: 1;
}

.form-input::placeholder {
  color: #a0aec0;
  opacity: 1;
}

.form-input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(107, 142, 35, 0.5);
  border-color: #6b8e23;
}

.form-textarea {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #e2e8f0;
  border-width: 1px;
  border-radius: 0.25rem;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
}

.form-textarea::-moz-placeholder {
  color: #a0aec0;
  opacity: 1;
}

.form-textarea::placeholder {
  color: #a0aec0;
  opacity: 1;
}

.form-textarea:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
  border-color: #63b3ed;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23a0aec0'%3e%3cpath d='M15.3 9.3a1 1 0 0 1 1.4 1.4l-4 4a1 1 0 0 1-1.4 0l-4-4a1 1 0 0 1 1.4-1.4l3.3 3.29 3.3-3.3z'/%3e%3c/svg%3e");
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  -webkit-print-color-adjust: exact;
          color-adjust: exact;
  background-repeat: no-repeat;
  background-color: #fff;
  border-color: #a0aec0;
  border-width: 1px;
  border-radius: 0.25rem;
  padding-top: 0.25rem;
  padding-right: 0.5rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 1rem;
  line-height: 1.5;
  background-position: right 0.5rem center;
  background-size: 1.5em 1.5em;
}

.form-select::-ms-expand {
  color: #a0aec0;
  border: none;
}

@media not print {
  .form-select::-ms-expand {
    display: none;
  }
}

@media print and (-ms-high-contrast: active), print and (-ms-high-contrast: none) {
  .form-select {
    padding-right: 0.5rem;
  }
}

.form-select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(107, 142, 35, 0.5);
  border-color: #6b8e23;
}

.form-checkbox:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M5.707 7.293a1 1 0 0 0-1.414 1.414l2 2a1 1 0 0 0 1.414 0l4-4a1 1 0 0 0-1.414-1.414L7 8.586 5.707 7.293z'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media not print {
  .form-checkbox::-ms-check {
    border-width: 1px;
    color: transparent;
    background: inherit;
    border-color: inherit;
    border-radius: inherit;
  }
}

.form-checkbox {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  -webkit-print-color-adjust: exact;
          color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1em;
  width: 1em;
  color: #4299e1;
  background-color: #fff;
  border-color: #a0aec0;
  border-width: 1px;
  border-radius: 0.25rem;
}

.form-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(107, 142, 35, 0.5);
  border-color: #6b8e23;
}

.form-radio:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media not print {
  .form-radio::-ms-check {
    border-width: 1px;
    color: transparent;
    background: inherit;
    border-color: inherit;
    border-radius: inherit;
  }
}

.form-radio {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  -webkit-print-color-adjust: exact;
          color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  border-radius: 100%;
  height: 1em;
  width: 1em;
  color: #6b8e23;
  background-color: #fff;
  border-color: #a0aec0;
  border-width: 1px;
}

.form-radio:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(107, 142, 35, 0.5);
  border-color: #6b8e23;
}

@font-face {
  font-family: 'Noto Sans';

  src: url(/fonts/notosans.ttf) format('truetype');
}

* {
  margin: 0;
  font-family: 'Noto Sans', sans-serif !important;
}

.bg-light {
  --text-opacity: 1;
  color: #33313b;
  color: rgba(51, 49, 59, var(--text-opacity));
}

.bg-light .fg {
  --bg-opacity: 1;
  background-color: #e5e4e4;
  background-color: rgba(229, 228, 228, var(--bg-opacity));
}

.bg-dark {
  --text-opacity: 1;
  color: #fbfbfb;
  color: rgba(251, 251, 251, var(--text-opacity));
}

.bg-dark .fg {
  --bg-opacity: 1;
  background-color: #403E48;
  background-color: rgba(64, 62, 72, var(--bg-opacity));
}

.bg-dark .cursor-pointer.fg:hover,
.bg-dark .group.fg:hover {
  --bg-opacity: 1;
  background-color: #4D4B55;
  background-color: rgba(77, 75, 85, var(--bg-opacity));
}

.bg-light .cursor-pointer.fg:hover,
.bg-light .group.fg:hover {
  --bg-opacity: 1;
  background-color: #CECDCD;
  background-color: rgba(206, 205, 205, var(--bg-opacity));
}

.group {
  flex-grow: 1;
  align-items: center;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  margin-bottom: 0.25rem;
}

.group.active {
  --bg-opacity: 1;
  background-color: #6b8e23;
  background-color: rgba(107, 142, 35, var(--bg-opacity));
  --text-opacity: 1;
  color: #fbfbfb;
  color: rgba(251, 251, 251, var(--text-opacity));
}

.group.active.fg:hover {
  --bg-opacity: 1;
  background-color: #6b8e23;
  background-color: rgba(107, 142, 35, var(--bg-opacity));
  --text-opacity: 1;
  color: #fbfbfb;
  color: rgba(251, 251, 251, var(--text-opacity));
}

.group-options {
  display: flex;
  flex: 1 1 0%;
  justify-content: center;
  cursor: pointer;
}

.group-options.fg:hover {
  --bg-opacity: 1;
  background-color: #4D4B55;
  background-color: rgba(77, 75, 85, var(--bg-opacity));
}

.group-options.fg:hover {
  --bg-opacity: 1;
  background-color: #CECDCD;
  background-color: rgba(206, 205, 205, var(--bg-opacity));
}

.profile-item {
  margin-left: -0.75rem;
  margin-right: -0.75rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  display: flex;
  justify-content: space-between;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.tab {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.5rem;
  padding-bottom: 0.25rem;
  cursor: pointer;
  --text-opacity: 1;
  color: #fbfbfb;
  color: rgba(251, 251, 251, var(--text-opacity));
}

.app.bg-light .tab.active {
  --text-opacity: 1;
  color: #6b8e23;
  color: rgba(107, 142, 35, var(--text-opacity));
}

.app.bg-dark .tab.active {
  --text-opacity: 1;
  color: #fbfbfb;
  color: rgba(251, 251, 251, var(--text-opacity));
}

.options-tab {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  cursor: pointer;
  --text-opacity: 1;
  color: #fbfbfb;
  color: rgba(251, 251, 251, var(--text-opacity));
}

.bg-light .options-tab.active {
  --text-opacity: 1;
  color: #6b8e23;
  color: rgba(107, 142, 35, var(--text-opacity));
  background-color: #fbfbfb;
}

.bg-dark .options-tab.active {
  --text-opacity: 1;
  color: #fbfbfb;
  color: rgba(251, 251, 251, var(--text-opacity));
  background-color: #33313b;
}

.transparent-btn {
  background-color: transparent;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  border-width: 1px;
  --border-opacity: 1;
  border-color: #7aa329;
  border-color: rgba(122, 163, 41, var(--border-opacity));
  border-radius: 0.25rem;
  display: block;
  margin-bottom: 1rem;
  margin-right: 1rem;
}

.transparent-btn:hover {
  --bg-opacity: 1;
  background-color: #7aa329;
  background-color: rgba(122, 163, 41, var(--bg-opacity));
  --text-opacity: 1;
  color: #fff;
  color: rgba(255, 255, 255, var(--text-opacity));
  border-color: transparent;
}

input,
select {
  --text-opacity: 1;
  color: #33313b;
  color: rgba(51, 49, 59, var(--text-opacity));
}

.bg-dark input.error,
.bg-dark textarea.error {
  --bg-opacity: 1;
  background-color: #feb2b2;
  background-color: rgba(254, 178, 178, var(--bg-opacity));
}

.bg-light input.error,
.bg-light textarea.error {
  --bg-opacity: 1;
  background-color: #fed7d7;
  background-color: rgba(254, 215, 215, var(--bg-opacity));
}

.bg-dark button.bg-transparent {
  --text-opacity: 1;
  color: #fbfbfb;
  color: rgba(251, 251, 251, var(--text-opacity));
}

.bg-light button.bg-transparent {
  --text-opacity: 1;
  color: #6b8e23;
  color: rgba(107, 142, 35, var(--text-opacity));
}

.bg-light button.bg-transparent:hover {
  --text-opacity: 1;
  color: #fbfbfb;
  color: rgba(251, 251, 251, var(--text-opacity));
}

.bg-dark #iprules tbody tr {
  --border-opacity: 1;
  border-color: #4D4B55;
  border-color: rgba(77, 75, 85, var(--border-opacity));
}

.bg-dark #iprules tbody tr:hover {
  --bg-opacity: 1;
  background-color: #403E48;
  background-color: rgba(64, 62, 72, var(--bg-opacity));
}

.bg-light #iprules tbody tr {
  --border-opacity: 1;
  border-color: #CECDCD;
  border-color: rgba(206, 205, 205, var(--border-opacity));
}

.bg-light #iprules tbody tr:hover {
  --bg-opacity: 1;
  background-color: #e5e4e4;
  background-color: rgba(229, 228, 228, var(--bg-opacity));
}

.modal {
  margin: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --text-opacity: 1;
  color: #33313b;
  color: rgba(51, 49, 59, var(--text-opacity));
  border-radius: 0.5rem;
}

/* cancel button */

.modal button.bg-transparent {
  --text-opacity: 1;
  color: #718096;
  color: rgba(113, 128, 150, var(--text-opacity));
}

.modal button.bg-transparent:hover {
  --text-opacity: 1;
  color: #4a5568;
  color: rgba(74, 85, 104, var(--text-opacity));
}

.bg-dark .modal {
  --bg-opacity: 1;
  background-color: #cbd5e0;
  background-color: rgba(203, 213, 224, var(--bg-opacity));
}

.bg-light .modal {
  background-color: #fbfbfb;
}

/* Checkbox switch */

.form-switch {
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  width: 3rem;
  margin-right: 0.25rem;
  line-height: 1.5;
}

.form-switch-checkbox {
  display: none;
}

.form-switch-label {
  display: block;
  overflow: hidden;
  cursor: pointer;
  --bg-opacity: 1;
  background-color: #e2e8f0;
  background-color: rgba(226, 232, 240, var(--bg-opacity));
  border-width: 1px;
  border-radius: 9999px;
  height: 1.5rem;
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  transition: background-color 0.2s ease-in;
}

.form-switch-label:before {
  position: absolute;
  display: block;
  --bg-opacity: 1;
  background-color: #fff;
  background-color: rgba(255, 255, 255, var(--bg-opacity));
  top: 0;
  bottom: 0;
  width: 1.5rem;
  border-width: 2px;
  border-radius: 9999px;
  margin-left: -0.25rem;
  right: 50%;
  content: '';
  transition: all 0.2s ease-in;
}

.form-switch-checkbox:checked + .form-switch-label,
.form-switch-checkbox:checked + .form-switch-label:before {
}

.form-switch-checkbox:checked + .form-switch-label {
  --bg-opacity: 1;
  background-color: #6b8e23;
  background-color: rgba(107, 142, 35, var(--bg-opacity));
  box-shadow: none;
}

.form-switch-checkbox:checked + .form-switch-label:before {
  top: 0;
  bottom: 0;
  right: 0;
}

.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.bg-transparent {
  background-color: transparent;
}

.bg-gray-300 {
  --bg-opacity: 1;
  background-color: #e2e8f0;
  background-color: rgba(226, 232, 240, var(--bg-opacity));
}

.bg-red-500 {
  --bg-opacity: 1;
  background-color: #f56565;
  background-color: rgba(245, 101, 101, var(--bg-opacity));
}

.bg-green-500 {
  --bg-opacity: 1;
  background-color: #48bb78;
  background-color: rgba(72, 187, 120, var(--bg-opacity));
}

.bg-primary {
  --bg-opacity: 1;
  background-color: #6b8e23;
  background-color: rgba(107, 142, 35, var(--bg-opacity));
}

.bg-dark {
  --bg-opacity: 1;
  background-color: #33313b;
  background-color: rgba(51, 49, 59, var(--bg-opacity));
}

.bg-dark-modal {
  background-color: rgba(51, 49, 59, .8);
}

.bg-light {
  --bg-opacity: 1;
  background-color: #fbfbfb;
  background-color: rgba(251, 251, 251, var(--bg-opacity));
}

.hover\:bg-red-600:hover {
  --bg-opacity: 1;
  background-color: #e53e3e;
  background-color: rgba(229, 62, 62, var(--bg-opacity));
}

.hover\:bg-green-600:hover {
  --bg-opacity: 1;
  background-color: #38a169;
  background-color: rgba(56, 161, 105, var(--bg-opacity));
}

.hover\:bg-primary-soft:hover {
  --bg-opacity: 1;
  background-color: #7aa329;
  background-color: rgba(122, 163, 41, var(--bg-opacity));
}

.focus\:bg-white:focus {
  --bg-opacity: 1;
  background-color: #fff;
  background-color: rgba(255, 255, 255, var(--bg-opacity));
}

.border-gray-300 {
  --border-opacity: 1;
  border-color: #e2e8f0;
  border-color: rgba(226, 232, 240, var(--border-opacity));
}

.border-red-500 {
  --border-opacity: 1;
  border-color: #f56565;
  border-color: rgba(245, 101, 101, var(--border-opacity));
}

.border-green-500 {
  --border-opacity: 1;
  border-color: #48bb78;
  border-color: rgba(72, 187, 120, var(--border-opacity));
}

.border-primary {
  --border-opacity: 1;
  border-color: #6b8e23;
  border-color: rgba(107, 142, 35, var(--border-opacity));
}

.focus\:border-primary:focus {
  --border-opacity: 1;
  border-color: #6b8e23;
  border-color: rgba(107, 142, 35, var(--border-opacity));
}

.rounded-sm {
  border-radius: 0.125rem;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-r-sm {
  border-top-right-radius: 0.125rem;
  border-bottom-right-radius: 0.125rem;
}

.rounded-l-sm {
  border-top-left-radius: 0.125rem;
  border-bottom-left-radius: 0.125rem;
}

.border-2 {
  border-width: 2px;
}

.border {
  border-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.cursor-pointer {
  cursor: pointer;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.flex-none {
  flex: none;
}

.flex-grow {
  flex-grow: 1;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.h-1 {
  height: 0.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-20 {
  height: 5rem;
}

.h-screen {
  height: 100vh;
}

.text-xs {
  font-size: 0.75rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-base {
  font-size: 1rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.text-mini {
  font-size: .84rem;
}

.leading-tight {
  line-height: 1.25;
}

.m-3 {
  margin: 0.75rem;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mr-8 {
  margin-right: 2rem;
}

.mr-16 {
  margin-right: 4rem;
}

.max-w-xs {
  max-width: 20rem;
}

.max-w-lg {
  max-width: 32rem;
}

.min-h-screen {
  min-height: 100vh;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-75 {
  opacity: 0.75;
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.overflow-y-auto {
  overflow-y: auto;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.pl-1 {
  padding-left: 0.25rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pb-8 {
  padding-bottom: 2rem;
}

.pt-12 {
  padding-top: 3rem;
}

.pb-12 {
  padding-bottom: 3rem;
}

.fixed {
  position: fixed;
}

.top-0 {
  top: 0;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-white {
  --text-opacity: 1;
  color: #fff;
  color: rgba(255, 255, 255, var(--text-opacity));
}

.text-gray-800 {
  --text-opacity: 1;
  color: #2d3748;
  color: rgba(45, 55, 72, var(--text-opacity));
}

.text-red-400 {
  --text-opacity: 1;
  color: #fc8181;
  color: rgba(252, 129, 129, var(--text-opacity));
}

.text-red-500 {
  --text-opacity: 1;
  color: #f56565;
  color: rgba(245, 101, 101, var(--text-opacity));
}

.text-red-800 {
  --text-opacity: 1;
  color: #9b2c2c;
  color: rgba(155, 44, 44, var(--text-opacity));
}

.text-green-500 {
  --text-opacity: 1;
  color: #48bb78;
  color: rgba(72, 187, 120, var(--text-opacity));
}

.text-primary {
  --text-opacity: 1;
  color: #6b8e23;
  color: rgba(107, 142, 35, var(--text-opacity));
}

.text-dark {
  --text-opacity: 1;
  color: #33313b;
  color: rgba(51, 49, 59, var(--text-opacity));
}

.text-light {
  --text-opacity: 1;
  color: #fbfbfb;
  color: rgba(251, 251, 251, var(--text-opacity));
}

.hover\:text-red-400:hover {
  --text-opacity: 1;
  color: #fc8181;
  color: rgba(252, 129, 129, var(--text-opacity));
}

.hover\:text-primary-soft:hover {
  --text-opacity: 1;
  color: #7aa329;
  color: rgba(122, 163, 41, var(--text-opacity));
}

.uppercase {
  text-transform: uppercase;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

.align-middle {
  vertical-align: middle;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.w-2\/5 {
  width: 40%;
}

.w-4\/5 {
  width: 80%;
}

.w-full {
  width: 100%;
}

.w-screen {
  width: 100vw;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-30 {
  z-index: 30;
}

.gap-x-2 {
  grid-column-gap: 0.5rem;
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}

.gap-y-2 {
  grid-row-gap: 0.5rem;
  row-gap: 0.5rem;
}

.grid-flow-row {
  grid-auto-flow: row;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.auto-rows-max {
  grid-auto-rows: max-content;
}

.transition {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}

@media (min-width: 640px) {

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 768px) {

  .md\:table-row {
    display: table-row;
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:mb-0 {
    margin-bottom: 0;
  }

  .md\:mr-2 {
    margin-right: 0.5rem;
  }

  .md\:ml-2 {
    margin-left: 0.5rem;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }
}

@media (min-width: 1024px) {

  .lg\:flex {
    display: flex;
  }
}

@media (min-width: 1280px) {

  .xl\:flex-row {
    flex-direction: row;
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.ps{overflow:hidden!important;overflow-anchor:none;-ms-overflow-style:none;touch-action:auto;-ms-touch-action:auto}.ps__rail-x{height:15px;bottom:0}.ps__rail-x,.ps__rail-y{display:none;opacity:0;transition:background-color .2s linear,opacity .2s linear;-webkit-transition:background-color .2s linear,opacity .2s linear;position:absolute}.ps__rail-y{width:15px;right:0}.ps--active-x>.ps__rail-x,.ps--active-y>.ps__rail-y{display:block;background-color:transparent}.ps--focus>.ps__rail-x,.ps--focus>.ps__rail-y,.ps--scrolling-x>.ps__rail-x,.ps--scrolling-y>.ps__rail-y,.ps:hover>.ps__rail-x,.ps:hover>.ps__rail-y{opacity:.6}.ps .ps__rail-x.ps--clicking,.ps .ps__rail-x:focus,.ps .ps__rail-x:hover,.ps .ps__rail-y.ps--clicking,.ps .ps__rail-y:focus,.ps .ps__rail-y:hover{background-color:#eee;opacity:.9}.ps__thumb-x{transition:background-color .2s linear,height .2s ease-in-out;-webkit-transition:background-color .2s linear,height .2s ease-in-out;height:6px;bottom:2px}.ps__thumb-x,.ps__thumb-y{background-color:#aaa;border-radius:6px;position:absolute}.ps__thumb-y{transition:background-color .2s linear,width .2s ease-in-out;-webkit-transition:background-color .2s linear,width .2s ease-in-out;width:6px;right:2px}.ps__rail-x.ps--clicking .ps__thumb-x,.ps__rail-x:focus>.ps__thumb-x,.ps__rail-x:hover>.ps__thumb-x{background-color:#999;height:11px}.ps__rail-y.ps--clicking .ps__thumb-y,.ps__rail-y:focus>.ps__thumb-y,.ps__rail-y:hover>.ps__thumb-y{background-color:#999;width:11px}@supports (-ms-overflow-style:none){.ps{overflow:auto!important}}@media (-ms-high-contrast:none),screen and (-ms-high-contrast:active){.ps{overflow:auto!important}}.ps{position:relative}
