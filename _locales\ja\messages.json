{"extDescription": {"message": "ブラウザのプロファイルを偽装します。いくつかのプライバシー強化オプションを含みます。"}, "notifications-profileChange": {"message": "プロフィ―ルが変更されました。"}, "notifications-unableToGetIPInfo": {"message": "IP 情報を取得できません"}, "notifications-usingIPInfo": {"message": "IP 情報を使用して:"}, "notifications-usingIPRule": {"message": "IP ルールを使用して:"}, "options-about-issueTracker": {"message": "Issue Tracker（課題管理）"}, "options-about-knownIssues": {"message": "既知の課題"}, "options-about-wiki": {"message": "Wiki"}, "options-about-support": {"message": "サポート"}, "options-about-sourceCode": {"message": "ソースコード"}, "options-about-translate": {"message": "翻訳を手伝う"}, "options-import-couldNotImport": {"message": "ファイルをインポートできませんでした。"}, "options-import-invalid-config": {"message": "不正な環境設定：設定構成の欠落"}, "options-import-invalid-excluded": {"message": "不正な環境設定：除外されたものが欠落"}, "options-import-invalid-excludedProfile": {"message": "不正な環境設定：除外されたプロファイルが欠落"}, "options-import-invalid-headers": {"message": "不正な環境設定：ヘッダの欠落"}, "options-import-invalid-ipRuleId": {"message": "不正な環境設定：無効な IP ルールの ID"}, "options-import-invalid-ipRuleName": {"message": "不正な環境設定：IP ルール名が欠落"}, "options-import-invalid-ipRuleRange": {"message": "不正な環境設定：無効な IP ルールの範囲"}, "options-import-invalid-ipRules": {"message": "不正な環境設定：IP ルールの欠落"}, "options-import-invalid-ipRulesDupe": {"message": "不正な設定：IP ルール ID の重複が見つかりました"}, "options-import-invalid-options": {"message": "不正な設定：オプションの欠落"}, "options-import-invalid-profile": {"message": "不正な設定：プロファイルの欠落"}, "options-import-invalid-setting": {"message": "不正な設定値："}, "options-import-invalid-settings": {"message": "不正な環境設定：環境設定が欠落"}, "options-import-invalid-spoofIP": {"message": "不正な設定：偽装したヘッダの IP 範囲に誤りがあります"}, "options-import-invalid-version": {"message": "不正な設定：バージョンが受け付けられません"}, "options-import-invalid-whitelist": {"message": "不正な設定：ホワイトリストの欠落"}, "options-import-invalid-whitelistDupe": {"message": "不正な設定：重複するホワイトリストのルール ID が見つかりました"}, "options-import-invalid-whitelistId": {"message": "不正な設定：無効なホワイトリストのルール ID"}, "options-import-invalid-whitelistName": {"message": "不正な設定：ホワイトリストルール名の欠落"}, "options-import-invalid-whitelistOpt": {"message": "不正な設定：ホワイトリストルールのオプションが不正です"}, "options-import-invalid-whitelistSpoofIP": {"message": "不正な設定：ホワイトリストルールの偽装 IP が不正です"}, "options-import-success": {"message": "環境設定を正常にインポートしました。拡張機能の再読み込み中..."}, "options-ipRules-editorTitle": {"message": "IP ルールエディタ"}, "options-ipRules-ipRule": {"message": "IP ルール"}, "options-ipRules-reload": {"message": "IP 情報の再読み込み"}, "options-ipRules-textareaLabel": {"message": "IP 範囲 / アドレス"}, "options-ipRules-textareaPlaceholder": {"message": "1回線あたり、1つの IP/IP 範囲"}, "options-modal-askDelete": {"message": "このルールを削除してよろしいですか？"}, "options-modal-askReset": {"message": "環境設定をリセットしても大丈夫ですか？"}, "options-modal-confirmDelete": {"message": "はい、削除して下さい！"}, "options-modal-confirmReset": {"message": "はい、私の環境設定をリセットして下さい！"}, "options-settings": {"message": "環境設定"}, "options-settings-import": {"message": "インポート"}, "options-settings-importing": {"message": "環境設定をインポートする"}, "options-settings-export": {"message": "エクスポート"}, "options-settings-reset": {"message": "Default（既定値）にリセット"}, "options-settings-permissions": {"message": "Chameleonは、フィンガープリントへの抵抗やトラッキング保護の有効化など、Firefoxのいくつかの設定を制御できます。これは他の拡張機能と競合する可能性があります。プライバシーの権限を削除することで、Chameleonがこれらの設定を制御することをオプトアウトできます。この権限を削除すると、これらの設定がリセットされることに注意してください。この権限が存在しない場合は、要求することができます。"}, "options-settings-permissions-legacy": {"message": "プライバシー許可を有効にするには、Chameleon の特別なバージョンをインストールする必要があります。これは、クロスプラットフォーム/この拡張機能の新しいバージョンでサポートされている権限の複雑さによるものです。詳細については、以下にリンクされている wiki を参照してください。"}, "options-settings-permissions-legacy-wiki": {"message": "wiki に詳細情報"}, "options-settings-permissions-request": {"message": "プライバシー許可をリクエストする"}, "options-settings-permissions-remove": {"message": "プライバシー許可を削除する"}, "options-tab-about": {"message": "概要"}, "options-tab-ipRules": {"message": "IP ルール"}, "options-whitelist-acceptLang": {"message": "受け入れ言語"}, "options-whitelist-editorTitle": {"message": "ホワイトリストのルールエディタ"}, "options-whitelist-headerIPLabel": {"message": "IP ヘッダ (Via や X-Forwarded のための)"}, "options-whitelist-options-audioContext": {"message": "Enable spoof audio context"}, "options-whitelist-options-clientRects": {"message": "Enable spoof client rects"}, "options-whitelist-options-cssExfil": {"message": "CSS Exfil をブロックする"}, "options-whitelist-options-mediaDevices": {"message": "メディアデバイスをブロックする"}, "options-whitelist-options-name": {"message": "window.name 保護を有効にする"}, "options-whitelist-options-tz": {"message": "タイムゾーンのスプーフィング（偽装化）を有効にします。"}, "options-whitelist-options-ws": {"message": "WebSocket を無効にする"}, "options-whitelist-rule": {"message": "ホワイトリストのルール"}, "options-whitelist-sitesTip": {"message": "1行に、1つのルール：ドメイン@@[任意の正規表現パターン]"}, "options-whitelist-textareaLabel": {"message": "IP 範囲 / アドレス"}, "options-whitelist-textareaPlaceholder": {"message": "1回線あたり、1つの IP/IP 範囲"}, "popup-home-change": {"message": "変更"}, "popup-home-currentProfile": {"message": "現在のプロファイル"}, "popup-home-currentProfile-defaultLanguage": {"message": "既定の言語"}, "popup-home-currentProfile-defaultScreen": {"message": "既定の画面"}, "popup-home-currentProfile-defaultTimezone": {"message": "既定のタイムゾーン"}, "popup-home-currentProfile-gettingTimezone": {"message": "IP 情報を取得する"}, "popup-home-currentProfile-screenProfile": {"message": "画面 (プロファイル)"}, "popup-home-disabled": {"message": "カメレオンは無効化状態です"}, "popup-home-enabled": {"message": "カメレオンは有効化されている"}, "popup-home-notification-disabled": {"message": "通知オフ"}, "popup-home-notification-enabled": {"message": "通知オン"}, "popup-home-onThisPage": {"message": "このページでは"}, "popup-home-theme-dark": {"message": "ダーク（黒基調）"}, "popup-home-theme-light": {"message": "ライト（白基調）"}, "popup-profile-changePeriodically": {"message": "定期的に変更する"}, "popup-profile-devicePhone": {"message": "電話"}, "popup-profile-deviceTablet": {"message": "タブレット端末"}, "popup-profile-interval-no": {"message": "不可"}, "popup-profile-interval-custom": {"message": "カスタム間隔"}, "popup-profile-interval-customMax": {"message": "最大 (分)"}, "popup-profile-interval-customMin": {"message": "最小 (分)"}, "popup-profile-interval-minute": {"message": "1 分ごと"}, "popup-profile-interval-5minutes": {"message": "5 分ごと"}, "popup-profile-interval-10minutes": {"message": "10 分ごと"}, "popup-profile-interval-20minutes": {"message": "20 分ごと"}, "popup-profile-interval-30minutes": {"message": "30 分ごと"}, "popup-profile-interval-40minutes": {"message": "40 分ごと"}, "popup-profile-interval-50minutes": {"message": "50 分ごと"}, "popup-profile-interval-hour": {"message": "1 時間ごと"}, "popup-profile-exclude": {"message": "除外"}, "popup-profile-randomAndroid": {"message": "ランダムな Android Browsers"}, "popup-profile-randomIOS": {"message": "ランダムな iOS Browsers"}, "popup-profile-randomMacOS": {"message": "ランダムな macOS Browsers"}, "popup-profile-randomLinux": {"message": "ランダムな Linux Browsers"}, "popup-profile-randomWindows": {"message": "ランダムな Windows Browsers"}, "popup-profile-random": {"message": "ランダム"}, "popup-profile-randomDesktopProfile": {"message": "ランダムなプロファイル (Desktop)"}, "popup-profile-randomMobileProfile": {"message": "ランダムなプロファイル (Mobile)"}, "popup-profile-showProfileOnIcon": {"message": "Show browser profile on icon"}, "popup-headers": {"message": "ヘッダー"}, "popup-headers-enableDNT": {"message": "DNT (Do Not Track) を有効にする"}, "popup-headers-preventEtag": {"message": "Etag トラッキングを防止する"}, "popup-headers-refererWarning": {"message": "以下のオプションの about:config 設定を変更しないでください。"}, "popup-headers-referer-trimming": {"message": "Trimming Policy のリファラー"}, "popup-headers-referer-trimming-sendFullURI": {"message": "完全な URI を送信する (規定)"}, "popup-headers-referer-trimming-schemeHostPortPath": {"message": "host, port + path のスキーム"}, "popup-headers-referer-trimming-schemeHostPort": {"message": "host + port のスキーム"}, "popup-headers-referer-xorigin": {"message": "X Origin Policy のリファラー"}, "popup-headers-referer-xorigin-alwaysSend": {"message": "常に送る (規定)"}, "popup-headers-referer-xorigin-matchBaseDomain": {"message": "基本ドメインと一致"}, "popup-headers-referer-xorigin-matchHost": {"message": "host と一致"}, "popup-headers-spoofAcceptLang": {"message": "Accept-Language を偽装する"}, "popup-headers-spoofIP": {"message": "X-Forwarded-For/Via IP を偽装する"}, "popup-headers-spoofIP-random": {"message": "ランダムな IP"}, "popup-headers-spoofIP-custom": {"message": "カスタム IP"}, "popup-headers-spoofIP-rangeFrom": {"message": "範囲の始点"}, "popup-headers-spoofIP-rangeTo": {"message": "範囲の終点"}, "popup-options": {"message": "オプション"}, "popup-options-grantPermissions": {"message": "設定を変更する権限を付与する"}, "popup-options-injection": {"message": "インジェクション"}, "popup-options-injection-limitTabHistory": {"message": "タブ履歴を制限する"}, "popup-options-injection-protectWinName": {"message": "関数 window.name（ウィンドウ名）を保護する"}, "popup-options-injection-audioContext": {"message": "音声コンテキストを偽装する"}, "popup-options-injection-clientRects": {"message": "Client Rects を偽装する"}, "popup-options-injection-protectKBFingerprint": {"message": "キーボードの指紋を保護する"}, "popup-options-injection-protectKBFingerprintDelay": {"message": "遅延 (ミリ秒)"}, "popup-options-injection-screen": {"message": "画面サイズ"}, "popup-options-injection-spoofFontFingerprint": {"message": "フォントの指紋を偽装する"}, "popup-options-standard": {"message": "標準"}, "popup-options-standard-blockMediaDevices": {"message": "メディアデバイスをブロックする"}, "popup-options-standard-blockCSSExfil": {"message": "CSS Exfil をブロックする"}, "popup-options-standard-disableWebRTC": {"message": "WebRTC を無効にする"}, "popup-options-standard-firstPartyIsolation": {"message": "ファーストパーティ分離を有効にする"}, "popup-options-standard-resistFingerprinting": {"message": "指紋採取を拒む機能を有効にします。"}, "popup-options-standard-spoofMediaDevices": {"message": "メディアデバイスを偽装する"}, "popup-options-standard-trackingProtection": {"message": "トラッキング保護モード"}, "popup-options-standard-trackingProtection-on": {"message": "On"}, "popup-options-standard-trackingProtection-off": {"message": "Off"}, "popup-options-standard-trackingProtection-privateBrowsing": {"message": "プライベートブラウジングで有効"}, "popup-options-standard-webRTCPolicy": {"message": "WebRTC ポリシー"}, "popup-options-standard-webRTCPolicy-nonProxified": {"message": "プロキシされていない UDP を無効にする"}, "popup-options-standard-webRTCPolicy-public": {"message": "パブリックインターフェースのみを使用する (最善)"}, "popup-options-standard-webRTCPolicy-publicPrivate": {"message": "パブリックおよびプライベートインターフェイスを使用する"}, "popup-options-standard-webSockets-blockAll": {"message": "全てブロック"}, "popup-options-standard-webSockets-blockThirdParty": {"message": "第三者をブロック"}, "popup-options-cookie": {"message": "クッキー"}, "popup-options-cookieNotPersistent": {"message": "ウィンドウを閉じた後、クッキーとサイトデータを削除する"}, "popup-options-cookiePolicy": {"message": "ポリシー"}, "popup-options-cookiePolicy-allowVisited": {"message": "訪問を許可する"}, "popup-options-cookiePolicy-rejectAll": {"message": "すべてを拒絶する"}, "popup-options-cookiePolicy-rejectThirdParty": {"message": "第三者を拒絶する"}, "popup-options-cookiePolicy-rejectTrackers": {"message": "トラッカーを拒絶する"}, "popup-options-cookiePolicy-rejectTrackersPartitionForeign": {"message": "Reject trackers and partition third-party cookies"}, "popup-whitelist-contextMenu": {"message": "ホワイトリストで現在のタブドメインを開くためのコンテキストメニュー項目を追加する"}, "popup-whitelist-defaultProfileLabel": {"message": "既定のプロファイル"}, "popup-whitelist-enable": {"message": "ホワイトリストの有効化"}, "popup-whitelist-isNotWhitelisted": {"message": "ホワイトリストに登録されていません"}, "popup-whitelist-isWhitelisted": {"message": "ホワイトリストに登録済み"}, "popup-whitelist-open": {"message": "ホワイトリストで開く"}, "text-addToRule": {"message": "Add to rule: $RULE_NAME$", "placeholders": {"rule_name": {"content": "$1", "example": "Firefox whitelist rule profile"}}}, "text-allowAll": {"message": "すべて許可する"}, "text-cancel": {"message": "取消"}, "text-createNewRule": {"message": "新しいルールを作成する"}, "text-default": {"message": "Default（規定）"}, "text-defaultWhitelistProfile": {"message": "デフォルトのホワイトリストプロファイル"}, "text-disableReferer": {"message": "リファラーを無効にする"}, "text-language": {"message": "言語"}, "text-name": {"message": "名称"}, "text-profile": {"message": "プロファイル"}, "text-realProfile": {"message": "真正プロファイル"}, "text-removeFromRule": {"message": "Remove from rule: $RULE_NAME$", "placeholders": {"rule_name": {"content": "$1", "example": "Firefox whitelist rule profile"}}}, "text-save": {"message": "保存する"}, "text-screen": {"message": "画面"}, "text-searchRules": {"message": "検索ルール"}, "text-startupDelay": {"message": "Startup delay (sec)"}, "text-timezone": {"message": "タイムゾーン"}, "text-whitelist": {"message": "ホワイトリスト"}}