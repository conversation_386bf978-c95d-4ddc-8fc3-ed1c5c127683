{"extDescription": {"message": "Tarayıcı profilinizi taklit edin. Birkaç gizlilik geliştirme seçeneği içerir."}, "notifications-profileChange": {"message": "<PERSON><PERSON>:"}, "notifications-unableToGetIPInfo": {"message": "IP bilgisi alınamıyor"}, "notifications-usingIPInfo": {"message": "IP Bilgilerini Kullanma:"}, "notifications-usingIPRule": {"message": "IP Kuralını Kullanma:"}, "options-about-issueTracker": {"message": "<PERSON><PERSON>"}, "options-about-knownIssues": {"message": "<PERSON><PERSON><PERSON>"}, "options-about-wiki": {"message": "Wiki"}, "options-about-support": {"message": "Destek"}, "options-about-sourceCode": {"message": "<PERSON><PERSON><PERSON>"}, "options-about-translate": {"message": "Çeviriye Yardım Et"}, "options-import-couldNotImport": {"message": "Dosya içe aktarılamadı"}, "options-import-invalid-config": {"message": "Geçersiz ayarlar: eksik konfigürasyon"}, "options-import-invalid-excluded": {"message": "Geçersiz <PERSON>: da<PERSON> e<PERSON>"}, "options-import-invalid-excludedProfile": {"message": "Geçersiz ayarlar: dahil edilmeyenin geçersiz profili var"}, "options-import-invalid-headers": {"message": "Geçersiz ayarlar: eks<PERSON> başlıklar"}, "options-import-invalid-ipRuleId": {"message": "Geçersiz ayarlar: geçersiz IP kural kimliği"}, "options-import-invalid-ipRuleName": {"message": "Geçersiz ayarlar: eksik <PERSON> kural ismi"}, "options-import-invalid-ipRuleRange": {"message": "Geçersiz ayarlar: geçersiz IP kuralı IP aralığı"}, "options-import-invalid-ipRules": {"message": "Geçersiz ayarlar: eksik IP kuralları"}, "options-import-invalid-ipRulesDupe": {"message": "Geçersiz ayarlar: tekrar eden IP kural kimliği bulundu"}, "options-import-invalid-options": {"message": "Geçersiz ayarlar: eks<PERSON>"}, "options-import-invalid-profile": {"message": "Geçersiz ayarlar: eksik profil"}, "options-import-invalid-setting": {"message": "Geçersiz a<PERSON>:"}, "options-import-invalid-settings": {"message": "Geçersiz a<PERSON>lar: eks<PERSON> ayar<PERSON>"}, "options-import-invalid-spoofIP": {"message": "Geçersiz ayarlar: Yalanlama başlığı IP aralığı geçersiz"}, "options-import-invalid-version": {"message": "Geçersiz ayarlar: s<PERSON><PERSON><PERSON><PERSON> kabul edilmiyor"}, "options-import-invalid-whitelist": {"message": "Geçersiz ayarlar: eksik beyaz liste"}, "options-import-invalid-whitelistDupe": {"message": "Geçersiz ayarlar: tekrar eden beyaz liste kural kimliği bulundu"}, "options-import-invalid-whitelistId": {"message": "Geçersiz ayarlar: geçersiz beyaz liste kural kimliği"}, "options-import-invalid-whitelistName": {"message": "Geçersiz ayarlar: eksik beyaz liste kural ismi"}, "options-import-invalid-whitelistOpt": {"message": "Geçersiz ayarlar: geçersiz beyaz liste kuralı seçenekleri"}, "options-import-invalid-whitelistSpoofIP": {"message": "Geçersiz ayarlar: geçersiz beyaz liste kuralı IP yalanlaması"}, "options-import-success": {"message": "Ayarlar başarıyla içe aktarıldı. Uzantı yeniden yükleniyor..."}, "options-ipRules-editorTitle": {"message": "IP Kural Düzenleyicisi"}, "options-ipRules-ipRule": {"message": "IP Kuralı"}, "options-ipRules-reload": {"message": "IP bilgilerini yeniden yükle"}, "options-ipRules-textareaLabel": {"message": "IP Aralıkları / Adresleri"}, "options-ipRules-textareaPlaceholder": {"message": "Satır başına bir IP/IP aralığı"}, "options-modal-askDelete": {"message": "Bu kuralı silmek istediğinizden emin misiniz?"}, "options-modal-askReset": {"message": "Ayarlarınızı sıfırlamak istediğinizden emin misiniz?"}, "options-modal-confirmDelete": {"message": "<PERSON><PERSON>, silin!"}, "options-modal-confirmReset": {"message": "<PERSON><PERSON>, a<PERSON><PERSON><PERSON>mı sıfırla!"}, "options-settings": {"message": "<PERSON><PERSON><PERSON>"}, "options-settings-import": {"message": "İçe Aktar"}, "options-settings-importing": {"message": "Ayarları içe aktar"}, "options-settings-export": {"message": "Dışa Aktar"}, "options-settings-reset": {"message": "Varsayılan ayarlara sıfırla"}, "options-settings-permissions": {"message": "<PERSON><PERSON><PERSON>, parmak izine direnme veya takip korumasını etkinleştirme gibi bazı Firefox ayarlarınızı kontrol edebilir. Bu, başka bir uzantı ile çelişebilir. Chameleon'un bu ayarları kontrol etmesini gizlilik iznini kaldırarak engelleyebilirsiniz. Not almanız gereken kısım, bu izni kaldırarak bu ayarları sıfırlamış olacaksınız. Mevcut olmaması durumunda bu izni talep edebilirsiniz."}, "options-settings-permissions-legacy": {"message": "Gizlilik izinlerini etkinleştirmek için özel bir Chameleon sürümü yüklemeniz gerekecektir. Bunun nedeni, bu uzantının çapraz platform / yeni sürümü için desteklenen izinlerle ilgili bazı zorluklardır. Aşağıda bağlantısı verilen wiki'de daha fazla ayrıntı bulunabilir."}, "options-settings-permissions-legacy-wiki": {"message": "Wiki hakkında daha fazla bilgi"}, "options-settings-permissions-request": {"message": "Gizlilik izinleri isteyin"}, "options-settings-permissions-remove": {"message": "Gizlilik izinlerini kaldırın"}, "options-tab-about": {"message": "Hakkında"}, "options-tab-ipRules": {"message": "IP kuralları"}, "options-whitelist-acceptLang": {"message": "Kabul edilen dil"}, "options-whitelist-editorTitle": {"message": "Beyaz Liste Kuralı Düzenleyicisi"}, "options-whitelist-headerIPLabel": {"message": "Başlık IP'si (Via & X-Forwarded-For)"}, "options-whitelist-options-audioContext": {"message": "Ses ortamını yalanlamayı etkinleştir"}, "options-whitelist-options-clientRects": {"message": "İstemci rectler yalanlamasını etkinleştir"}, "options-whitelist-options-cssExfil": {"message": "CSS Exfil'i Engelle"}, "options-whitelist-options-mediaDevices": {"message": "<PERSON><PERSON>a c<PERSON>ını engelle"}, "options-whitelist-options-name": {"message": "<PERSON><PERSON><PERSON> is<PERSON> k<PERSON>ı etkinleştir"}, "options-whitelist-options-tz": {"message": "Saat dilimi yalanlamasını etkinleştir"}, "options-whitelist-options-ws": {"message": "WebSocket'i devre dışı bırak"}, "options-whitelist-rule": {"message": "Beyaz Liste Kuralı"}, "options-whitelist-sitesTip": {"message": "<PERSON><PERSON><PERSON>r ba<PERSON>ına bir kural: alanadı@@[opsiyonel regex deseni]"}, "options-whitelist-textareaLabel": {"message": "IP Aralıkları / Adresleri"}, "options-whitelist-textareaPlaceholder": {"message": "Satır başına bir IP/IP aralığı"}, "popup-home-change": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "popup-home-currentProfile": {"message": "Şuanki Profil"}, "popup-home-currentProfile-defaultLanguage": {"message": "Varsayılan Dil"}, "popup-home-currentProfile-defaultScreen": {"message": "Varsayılan Ekran"}, "popup-home-currentProfile-defaultTimezone": {"message": "Varsayılan Saat Dilimi"}, "popup-home-currentProfile-gettingTimezone": {"message": "IP bilgileri alınıyor"}, "popup-home-currentProfile-screenProfile": {"message": "<PERSON><PERSON><PERSON> (Profil)"}, "popup-home-disabled": {"message": "Chameleon devre dışı"}, "popup-home-enabled": {"message": "Chamel<PERSON>"}, "popup-home-notification-disabled": {"message": "Bildir<PERSON>ler Kapalı"}, "popup-home-notification-enabled": {"message": "Bildirimler Açık"}, "popup-home-onThisPage": {"message": "bu say<PERSON>da"}, "popup-home-theme-dark": {"message": "<PERSON><PERSON>"}, "popup-home-theme-light": {"message": "Açık"}, "popup-profile-changePeriodically": {"message": "Periyodik olarak değ<PERSON>ştir"}, "popup-profile-devicePhone": {"message": "Telefon"}, "popup-profile-deviceTablet": {"message": "Tablet"}, "popup-profile-interval-no": {"message": "Hay<PERSON><PERSON>"}, "popup-profile-interval-custom": {"message": "<PERSON><PERSON>"}, "popup-profile-interval-customMax": {"message": "<PERSON><PERSON><PERSON><PERSON> (dakika)"}, "popup-profile-interval-customMin": {"message": "Minimum (dakika)"}, "popup-profile-interval-minute": {"message": "Her dakika"}, "popup-profile-interval-5minutes": {"message": "5 dakikada bir"}, "popup-profile-interval-10minutes": {"message": "10 dakikada bir"}, "popup-profile-interval-20minutes": {"message": "20 dakikada bir"}, "popup-profile-interval-30minutes": {"message": "30 dakikada bir"}, "popup-profile-interval-40minutes": {"message": "40 dakikada bir"}, "popup-profile-interval-50minutes": {"message": "50 dakikada bir"}, "popup-profile-interval-hour": {"message": "Her saat"}, "popup-profile-exclude": {"message": "<PERSON><PERSON> tut"}, "popup-profile-randomAndroid": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "popup-profile-randomIOS": {"message": "Rast<PERSON><PERSON>yıcıları"}, "popup-profile-randomMacOS": {"message": "Rastgele macOS Tarayıcılar"}, "popup-profile-randomLinux": {"message": "Rastgele Linux Tarayıcılar"}, "popup-profile-randomWindows": {"message": "Rastgele Windows Tarayıcıları"}, "popup-profile-random": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "popup-profile-randomDesktopProfile": {"message": "<PERSON><PERSON><PERSON><PERSON> (Masaüstü)"}, "popup-profile-randomMobileProfile": {"message": "<PERSON><PERSON><PERSON><PERSON> (Mobil)"}, "popup-profile-showProfileOnIcon": {"message": "Simgede tarayıcı profilini göster"}, "popup-headers": {"message": "Başlıklar"}, "popup-headers-enableDNT": {"message": "DNT'y<PERSON> (Beni Takip Etme)"}, "popup-headers-preventEtag": {"message": "<PERSON><PERSON> ta<PERSON>"}, "popup-headers-refererWarning": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON> i<PERSON><PERSON> about:config <PERSON><PERSON><PERSON><PERSON><PERSON>."}, "popup-headers-referer-trimming": {"message": "Yönlendirici Trimming Poliçesi"}, "popup-headers-referer-trimming-sendFullURI": {"message": "<PERSON><PERSON><PERSON>'y<PERSON> (varsayılan)"}, "popup-headers-referer-trimming-schemeHostPortPath": {"message": "<PERSON><PERSON>, host, port + yol"}, "popup-headers-referer-trimming-schemeHostPort": {"message": "Şema, host + port"}, "popup-headers-referer-xorigin": {"message": "Yönlendirici X Origin Poliçesi"}, "popup-headers-referer-xorigin-alwaysSend": {"message": "Her zaman gönder (varsayılan)"}, "popup-headers-referer-xorigin-matchBaseDomain": {"message": "Temel alan adını eşleştir (alan adı + TLD)"}, "popup-headers-referer-xorigin-matchHost": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON>"}, "popup-headers-spoofAcceptLang": {"message": "Kabul edilen dili yalanla"}, "popup-headers-spoofIP": {"message": "X-Forwarded-For/Via IP'yi ya<PERSON>la"}, "popup-headers-spoofIP-random": {"message": "Rastgele IP"}, "popup-headers-spoofIP-custom": {"message": "Özel IP"}, "popup-headers-spoofIP-rangeFrom": {"message": "Aralık Başlangıcı"}, "popup-headers-spoofIP-rangeTo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "popup-options": {"message": "Seçenekler"}, "popup-options-grantPermissions": {"message": "Ayarları değiştirmek için izin ver"}, "popup-options-injection": {"message": "Enjeksiyon"}, "popup-options-injection-limitTabHistory": {"message": "Sekme geçmişini sınırla"}, "popup-options-injection-protectWinName": {"message": "<PERSON><PERSON><PERSON> is<PERSON> koru"}, "popup-options-injection-audioContext": {"message": "Ses ortamını yalanla"}, "popup-options-injection-clientRects": {"message": "İstemci rectlerini (dörtgen parmak izini) yalanla"}, "popup-options-injection-protectKBFingerprint": {"message": "Klavye parmak izini koru"}, "popup-options-injection-protectKBFingerprintDelay": {"message": "<PERSON><PERSON><PERSON><PERSON> (ms)"}, "popup-options-injection-screen": {"message": "<PERSON><PERSON><PERSON> boy<PERSON>u"}, "popup-options-injection-spoofFontFingerprint": {"message": "Yazı tipi parmak izini yalanla"}, "popup-options-standard": {"message": "<PERSON><PERSON>"}, "popup-options-standard-blockMediaDevices": {"message": "<PERSON><PERSON>a c<PERSON>ını engelle"}, "popup-options-standard-blockCSSExfil": {"message": "CSS Exfil'i Engelle"}, "popup-options-standard-disableWebRTC": {"message": "WebRTC'yi devre dışı bırak"}, "popup-options-standard-firstPartyIsolation": {"message": "1. <PERSON><PERSON><PERSON>"}, "popup-options-standard-resistFingerprinting": {"message": "Parmak izine direnmeyi etkinleştir - Fingerprinting"}, "popup-options-standard-spoofMediaDevices": {"message": "<PERSON>hte medya cihaz<PERSON>ı"}, "popup-options-standard-trackingProtection": {"message": "<PERSON><PERSON><PERSON> koruma modu"}, "popup-options-standard-trackingProtection-on": {"message": "Açık"}, "popup-options-standard-trackingProtection-off": {"message": "<PERSON><PERSON><PERSON>"}, "popup-options-standard-trackingProtection-privateBrowsing": {"message": "<PERSON><PERSON><PERSON>"}, "popup-options-standard-webRTCPolicy": {"message": "WebRTC Politikası"}, "popup-options-standard-webRTCPolicy-nonProxified": {"message": "Proxysiz UDP'yi de<PERSON> dışı bırak"}, "popup-options-standard-webRTCPolicy-public": {"message": "Yalnızca Genel arayüzü kullan (en iyisi)"}, "popup-options-standard-webRTCPolicy-publicPrivate": {"message": "<PERSON><PERSON> ve <PERSON> k<PERSON>an"}, "popup-options-standard-webSockets-blockAll": {"message": "<PERSON><PERSON><PERSON> en<PERSON>"}, "popup-options-standard-webSockets-blockThirdParty": {"message": "3. <PERSON><PERSON><PERSON> en<PERSON>"}, "popup-options-cookie": {"message": "Çerez"}, "popup-options-cookieNotPersistent": {"message": "<PERSON><PERSON><PERSON> kapatıldıktan sonra çerezleri ve site verilerini silin"}, "popup-options-cookiePolicy": {"message": "Politika"}, "popup-options-cookiePolicy-allowVisited": {"message": "<PERSON><PERSON><PERSON><PERSON> izin ver"}, "popup-options-cookiePolicy-rejectAll": {"message": "Tümünü reddet"}, "popup-options-cookiePolicy-rejectThirdParty": {"message": "Üçüncü tarafı reddet"}, "popup-options-cookiePolicy-rejectTrackers": {"message": "İzleyicileri reddet"}, "popup-options-cookiePolicy-rejectTrackersPartitionForeign": {"message": "Takipçileri reddet ve üçüncü şahış çerezleri ayır"}, "popup-whitelist-contextMenu": {"message": "İçerik menüsü ögesini beyaz listedeki şu anki açık sekme alan adına ekle"}, "popup-whitelist-defaultProfileLabel": {"message": "Varsayılan Profil"}, "popup-whitelist-enable": {"message": "<PERSON><PERSON> <PERSON><PERSON>i et<PERSON>"}, "popup-whitelist-isNotWhitelisted": {"message": "beyaz <PERSON><PERSON>"}, "popup-whitelist-isWhitelisted": {"message": "beyaz <PERSON>e"}, "popup-whitelist-open": {"message": "Beyaz listede aç"}, "text-addToRule": {"message": "Ku<PERSON>a ekle: $RULE_NAME$", "placeholders": {"rule_name": {"content": "$1", "example": "Firefox whitelist rule profile"}}}, "text-allowAll": {"message": "<PERSON><PERSON><PERSON> izin ver"}, "text-cancel": {"message": "İptal"}, "text-createNewRule": {"message": "<PERSON><PERSON> k<PERSON> o<PERSON>ş<PERSON>"}, "text-default": {"message": "Varsayılan"}, "text-defaultWhitelistProfile": {"message": "Varsayılan Beyaz Liste Profili"}, "text-disableReferer": {"message": "Yönlendiriciyi devre dışı bırak"}, "text-language": {"message": "Dil"}, "text-name": {"message": "İsim"}, "text-profile": {"message": "Profil"}, "text-realProfile": {"message": "Gerçek Profil"}, "text-removeFromRule": {"message": "Kuraldan sil: $RULE_NAME$", "placeholders": {"rule_name": {"content": "$1", "example": "Firefox whitelist rule profile"}}}, "text-save": {"message": "<PERSON><PERSON>"}, "text-screen": {"message": "<PERSON><PERSON><PERSON>"}, "text-searchRules": {"message": "<PERSON><PERSON>"}, "text-startupDelay": {"message": "Başlangıç gecikmesi (saniye)"}, "text-timezone": {"message": "Saat dilimi"}, "text-whitelist": {"message": "Beyaz liste"}}